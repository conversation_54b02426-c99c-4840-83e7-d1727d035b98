ARG DEPS_IMAGE=node:22-alpine
ARG BUILD_IMAGE=node:22-alpine
ARG PROD_IMAGE=node:22-alpine
ARG RUN_IMAGE=node:22-alpine
###########################

# Stage 1: Dependency installation

FROM $DEPS_IMAGE AS deps

WORKDIR /app

# Copy files required for dependency installation
COPY package.json package-lock.json* ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci --legacy-peer-deps

###########################

### Stage 2: Build

FROM $BUILD_IMAGE AS builder

WORKDIR /app

# Install openssl for Alpine (needed for Prisma)
RUN apk add --no-cache openssl

# Copy installed dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy files needed for build
COPY --from=deps /app/package.json /app/package-lock.json ./
COPY tsconfig.json ./

# Copy prisma schema
COPY prisma ./prisma

# Copy source code
COPY src ./src

# Generate Prisma client
RUN npm run generate

# Compile TypeScript
RUN npm run build

###########################

### Stage 3: Production Dependencies
FROM $PROD_IMAGE AS prod

WORKDIR /app

# Install openssl for Alpine (needed for Prisma)
RUN apk add --no-cache openssl

# Copy package.json for installation of production dependencies
COPY package.json package-lock.json* ./

# Copy prisma schema for production dependencies
COPY prisma ./prisma

# Install only production dependencies
RUN npm ci --only=production --omit=dev --legacy-peer-deps

# Generate Prisma client for production
RUN npm run generate

###########################

### Stage 4: Runtime

FROM $RUN_IMAGE AS runtime

WORKDIR /app

# Install openssl for Alpine (needed for Prisma runtime)
RUN apk add --no-cache openssl

# Copy package.json for runtime
COPY --from=builder /app/package.json ./

# Copy compiled runtime files
COPY --from=builder /app/dist ./dist

# Copy prisma schema for runtime
COPY --from=builder /app/prisma ./prisma

# Copy node_modules with only production dependencies and generated Prisma client
COPY --from=prod /app/node_modules ./node_modules

# App's port
EXPOSE 4002

# Start the server
CMD ["node", "dist/index.js"]

###########################
