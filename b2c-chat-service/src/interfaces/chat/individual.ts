import type { DateNullI } from '@interfaces/common/data';
import { Prisma } from '@prisma/mongodb';
import type { MessageContentI } from '@schemas/chat/chat';

export type IndividualChatUpsertMessageI = {
  data: unknown;
};

export type IndividualChatMessageRawResultI = {
  _id: string;
  senderId: string;
  recieverId: string;
  content: Prisma.JsonValue;
  readAt: DateNullI;
  createdAt: Date;
};

export type IndividualChatMessageResultI = {
  id: string;
  senderId: string;
  recieverId: string;
  content: MessageContentI;
  readAt: DateNullI;
  createdAt: Date;
  userStatus: {
    status: string;
    lastSeen: Date | null;
  };
};

export interface ChatListResponse {
  data: IndividualChatMessageResultI[];
  total: number;
  hasMore: boolean;
}

export interface MessageListResponse {
  data: MessageData[];
  total: number;
  hasMore: boolean;
  lastSeen: DateNullI;
}

export interface MessageData {
  id: string;
  senderId: string;
  recieverId: string;
  content: {
    text?: string | null;
    media?: Array<{
      url: string;
      mimeType: string;
      name?: string | null;
    }>;
  };
  messageType: 'TEXT' | 'MEDIA' | 'MIXED';
  replyTo?: string | null;
  readAt?: Date | null;
  createdAt: Date;
  editedAt?: Date | null;
  deletedForAll: boolean;
  deletedFor: string[];
}
