import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import ChatModule from '@modules/chat';
import {
  IndividualChatDeleteManySpecificProfileSchema,
  IndividualChatFindAllSchema,
  IndividualChatFindAllSpecificProfileSchema,
  IndividualChatDeleteManyAllSchema,
  IndividualChatDeleteSpecificProfileChatSchema,
} from '@schemas/chat/chat';
import type { FastifyInstance, FastifyReply } from 'fastify';

const individualChatRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/api/v1/individual-chats', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = IndividualChatFindAllSchema.safeParse(request.query);
    if (error) {
      throw new AppError('INDCHT002');
    }
    const result = await ChatModule.IndividualChatModule.findAll(request, data);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/api/v1/individual-chat/profile', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = IndividualChatFindAllSpecificProfileSchema.safeParse(request.query);
    if (error) {
      throw new AppError('INDCHT002');
    }
    const result = await ChatModule.IndividualChatModule.findAllSpecificProfile(request, data);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.delete('/api/v1/individual-chats/all', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = IndividualChatDeleteManyAllSchema.safeParse(JSON.parse(request.body as string));
    if (error) {
      throw new AppError('INDCHT007');
    }
    await ChatModule.IndividualChatModule.deleteManyAll(request, data);
    reply.status(HttpStatus.NO_CONTENT);
  });

  fastify.delete(
    '/api/v1/individual-chat/profile/:profileId',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data } = IndividualChatDeleteSpecificProfileChatSchema.safeParse(request.params);
      if (error) {
        throw new AppError('INDCHT005');
      }
      await ChatModule.IndividualChatModule.deleteSpecificProfileChat(request, { profileId: data.profileId });
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
};

export default individualChatRoutes;
