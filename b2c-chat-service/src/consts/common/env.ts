import { EnvVariablesI } from '@interfaces/common/env';

export const ENV: EnvVariablesI = {
  API_KEY: process.env.API_KEY as string,
  ENCRYPTION_SECRET_KEY: process.env.ENCRPTION_SECRET_KEY,
  JWT_SECRET: process.env.JWT_SECRET,
  KAFKA_SOCKET_BROKER: process.env.KAFKA_SOCKET_BROKER,
  KAFKA_CHAT_CLIENT_ID: process.env.KAFKA_CHAT_CLIENT_ID,
  MONGO_DATABASE_URL: process.env.MONGO_DATABASE_URL,
  NODE_ENV: process.env.NODE_ENV as 'development' | 'production' | 'test',
  CHAT_PORT: parseInt(process.env.CHAT_PORT),
} as EnvVariablesI;
