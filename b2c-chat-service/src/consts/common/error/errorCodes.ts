import { HttpStatus } from '@consts/common/api/status';

export type ErrorCodeValueI = {
  status: HttpStatus;
  message: string;
};

export const ErrorCodes = {
  //#region General
  GEN001: {
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Something went wrong! Please try after sometime',
  },
  GEN002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid payload',
  },
  GEN003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is empty',
  },
  GEN004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid parameter',
  },
  GEN005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid query',
  },
  GEN006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid data',
  },
  GEN007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid input data',
  },
  //#endregion General
  //#region Database
  DB001: {
    status: HttpStatus.CONFLICT,
    message: 'The data with the same identifier already exists',
  },
  DB002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Data not found',
  },
  DB003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Referred data is invalid',
  },
  DB004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to complete the operation',
  },
  //#endregion Database
  //#region Auth
  AUTH001: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'Incorrect password',
  },
  AUTH002: {
    status: HttpStatus.NOT_FOUND,
    message: "Email isn't registered",
  },
  AUTH003: {
    status: HttpStatus.FORBIDDEN,
    message: "You haven't created a password",
  },
  AUTH004: {
    status: HttpStatus.FORBIDDEN,
    message: "Profile's googleSub & token's user_id are different",
  },
  AUTH005: {
    status: HttpStatus.CONFLICT,
    message: 'Email is already registered',
  },
  AUTH006: {
    status: HttpStatus.CONFLICT,
    message: 'Username is already taken',
  },
  AUTH007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Data is missing',
  },
  AUTH008: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Only EMAIL_PASSWORD type is allowed',
  },
  AUTH009: {
    status: HttpStatus.BAD_REQUEST,
    message: "platform isn't matching the appVersion's platform",
  },
  AUTH010: {
    status: HttpStatus.BAD_REQUEST,
    message: 'googleToken is invalid',
  },
  AUTH011: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-api-key is mandatory',
  },
  AUTH012: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-api-key is invalid',
  },
  AUTH013: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-platform is mandatory',
  },
  AUTH014: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-platform is invalid',
  },
  AUTH015: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-device-id is mandatory',
  },
  AUTH016: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-device-id is invalid',
  },
  AUTH017: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-version-no is mandatory',
  },
  AUTH018: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-version-no is invalid',
  },
  AUTH019: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'authorization token is mandatory',
  },
  AUTH020: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'authorization token is invalid',
  },
  AUTH021: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'authorization token is expired',
  },
  //#endregion Auth
  //#region Session
  SESS001: {
    status: HttpStatus.TOO_MANY_REQUESTS,
    message: 'Maximum session limit exceeded',
  },
  //#endregion Session
  //#region AppVersion
  APPVN001: {
    status: HttpStatus.NOT_FOUND,
    message: 'App version not found',
  },
  APPVN002: {
    status: HttpStatus.NOT_FOUND,
    message: "App version isn't active",
  },
  //#endregion AppVersion
  //#region AppConfig
  APPCFG001: {
    status: HttpStatus.NOT_FOUND,
    message: 'appConfig not found',
  },
  //#endregion AppConfig
  //#region IndividualChat
  INDCHT001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Invalid chat's data",
  },
  INDCHT002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Query is invalid',
  },
  INDCHT003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'At least one id is required',
  },
  INDCHT004: {
    status: HttpStatus.NOT_FOUND,
    message: "Some messages don't exist",
  },
  INDCHT005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the message(s)',
  },
  INDCHT006: {
    status: HttpStatus.NOT_FOUND,
    message: 'Payload is invalid',
  },
  INDCHT007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'At least one profileId is required',
  },
  INDCHT008: {
    status: HttpStatus.FORBIDDEN,
    message: 'Cannot delete message for everyone after time limit',
  },
  INDCHT009: {
    status: HttpStatus.FORBIDDEN,
    message: 'Only sender can delete message for everyone',
  },
  INDCHT010: {
    status: HttpStatus.NOT_FOUND,
    message: 'Message not found or already deleted',
  },
  INDCHT011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid message content',
  },
  INDCHT012: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Media upload failed',
  },
  INDCHT013: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Unsupported media type',
  },
  INDCHT014: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Message edit time limit exceeded',
  },
  INDCHT015: {
    status: HttpStatus.FORBIDDEN,
    message: 'Cannot edit message sent by another user',
  },
  //#endregion IndividualChat
};

export type ErrorCodeI = keyof typeof ErrorCodes;
