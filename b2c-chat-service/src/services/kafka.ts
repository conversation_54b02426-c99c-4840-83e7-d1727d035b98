import { ENV } from '@consts/common/env';
import { KafkaTopicI } from '@consts/kafka';
import { delay } from '@utils/fn/process';
import { Kafka, Consumer, Producer } from 'kafkajs';

class KafkaService {
  public static readonly instance: KafkaService = new KafkaService();
  public consumer: Consumer;
  private producer: Producer;
  private kafka: Kafka;
  private isSubscribed: boolean = false;
  private isConsumerConnected: boolean = false;
  private isProducerConnected: boolean = false;
  private consumerConnectAttempts: number = 0;
  private readonly maxAttempts: number = 20;
  private readonly reconnectDelay: number = 15000;
  private readonly topics: KafkaTopicI[] = [
    'individual-chat',
    'delete-for-everyone',
    'delete-for-me',
    'edit-message',
    'mark-as-read',
    'socket-message-response',
    'user-status-update',
    'read-receipt',
  ];

  private constructor() {
    if (!this.kafka) {
      this.kafka = new Kafka({
        clientId: ENV.KAFKA_CHAT_CLIENT_ID,
        brokers: [ENV.KAFKA_SOCKET_BROKER],
        connectionTimeout: 45000,
        requestTimeout: 90000,
        retry: {
          initialRetryTime: 2000,
          maxRetryTime: 60000,
          retries: 20,
        },
      });
      this.consumer = this.kafka.consumer({
        groupId: `${ENV.KAFKA_CHAT_CLIENT_ID}-${Date.now()}`,
        sessionTimeout: 90000,
        heartbeatInterval: 15000,
        maxWaitTimeInMs: 10000,
        retry: {
          initialRetryTime: 2000,
          retries: 15,
        },
      });
      this.producer = this.kafka.producer({
        allowAutoTopicCreation: true,
        idempotent: true,
      });
      this.setupEventListeners();
    }
  }

  private setupEventListeners = async (): Promise<void> => {
    this.consumer.on('consumer.disconnect', () => {
      this.isConsumerConnected = false;
      setTimeout(() => this.connectConsumer(), 10000);
    });
    this.consumer.on('consumer.crash', (error) => {
      this.isConsumerConnected = false;
      setTimeout(() => this.connectConsumer(), 10000);
    });
    this.consumer.on('consumer.connect', () => {
      this.isConsumerConnected = true;
      this.consumerConnectAttempts = 0;
    });
    this.consumer.on('consumer.group_join', () => {
      //
    });
    this.consumer.on('consumer.network.request_timeout', (payload) => {
      //
    });
  };

  private waitForKafkaCoordinator = async (): Promise<void> => {
    const admin = this.kafka.admin();
    let attempts = 0;
    const maxAttempts = 40;

    while (attempts < maxAttempts) {
      try {
        await admin.connect();

        await admin.listTopics();

        try {
          await admin.createTopics({
            topics: [
              {
                topic: 'coordinator-test-consumer',
                numPartitions: 1,
                replicationFactor: 1,
              },
            ],
          });
          await admin.deleteTopics({
            topics: ['coordinator-test-consumer'],
          });
        } catch (error) {
          throw new Error(`Coordinator not ready: ${error.message}`);
        }

        await admin.disconnect();
        return;
      } catch (error) {
        attempts++;
        try {
          await admin.disconnect();
        } catch (disconnectError) {
          // Ignore
        }
        if (attempts < maxAttempts) {
          await delay(5000);
        }
      }
    }
    throw new Error('Kafka coordinator not available after maximum attempts');
  };

  private subscribeToTopics = async (): Promise<void> => {
    if (!this.isSubscribed) {
      await this.consumer.subscribe({
        topics: this.topics,
        fromBeginning: false,
      });
      this.isSubscribed = true;
      //
    }
  };

  private connectConsumer = async (): Promise<void> => {
    if (this.isConsumerConnected || this.consumerConnectAttempts > this.maxAttempts) {
      return;
    }
    try {
      await this.waitForKafkaCoordinator();

      await delay(15000);

      await this.consumer.connect();
      await this.subscribeToTopics();
      this.isConsumerConnected = true;
    } catch (error) {
      ++this.consumerConnectAttempts;
      if (this.consumerConnectAttempts <= this.maxAttempts) {
        await delay(this.reconnectDelay);
        await this.connectConsumer();
      } else {
      }
    }
    return;
  };

  private connectProducer = async (): Promise<void> => {
    if (!this.isProducerConnected) {
      try {
        await this.producer.connect();
        this.isProducerConnected = true;
      } catch (error) {
        throw error;
      }
    }
  };
  private disconnectConsumer = async (): Promise<void> => {
    if (this.isConsumerConnected) {
      try {
        await this.consumer.disconnect();
        this.isConsumerConnected = false;
      } catch (_error) {
        //
      }
    }
  };

  private disconnectProducer = async (): Promise<void> => {
    if (this.isProducerConnected) {
      try {
        await this.producer.disconnect();
        this.isProducerConnected = false;
      } catch (_error) {
        //
      }
    }
  };

  public sendMessage = async (topic: string, message: any): Promise<void> => {
    try {
      await this.connectProducer();

      await this.producer.send({
        topic,
        messages: [
          {
            value: JSON.stringify(message),
            key: `${Date.now()}`,
          },
        ],
      });
    } catch (error) {
      throw error;
    }
  };

  public start = async (callbackFn): Promise<void> => {
    await delay(45000);

    await this.connectConsumer();
    this.run(callbackFn);
  };

  private run = async (callbackFn): Promise<void> => {
    await this.consumer.run({
      eachMessage: async ({ message, topic }) => {
        try {
          const data = JSON.parse(message.value?.toString() || '{}');
          await callbackFn({ data, topic });
        } catch (error) {}
      },
    });
  };

  public stop = async (): Promise<void> => {
    await this.disconnectConsumer();
    await this.disconnectProducer();
  };
}

export default KafkaService;
