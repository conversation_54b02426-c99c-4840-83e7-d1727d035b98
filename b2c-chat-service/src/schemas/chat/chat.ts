import { PaginationSchema, ObjectIdSchema, UUIDSchema } from '@schemas/common/common';
import { isNullUndefined } from '@utils/data/data';
import { z } from 'zod';

export const MimeTypeE = z.enum(['JPEG', 'PDF', 'TEXT', 'MP3', 'MP4']);

export const MediaSchema = z.object({
  url: z.string().url(),
  mimeType: MimeTypeE,
  name: z.string().optional(),
});
export type MediaI = z.infer<typeof MediaSchema>;

export const MessageContentSchema = z
  .object({
    text: z.string().trim().optional(),
    media: z.array(MediaSchema).default([]),
  })
  .superRefine((data, ctx) => {
    if (isNullUndefined(data?.text) && (!data?.media || data.media.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Either text or media are required',
      });
    }
  });
export type MessageContentI = z.infer<typeof MessageContentSchema>;

export const IndividualChatSaveMessageDataSchema = z
  .object({
    senderId: UUIDSchema.optional(),
    recieverId: UUIDSchema.optional(),
    content: MessageContentSchema.optional(),
    replyTo: z.union([ObjectIdSchema.optional(), z.null()]),
    readAt: z.date().optional(),
  })
  .superRefine((data, ctx) => {
    if (
      ['senderId', 'recieverId', 'content'].some((key) => isNullUndefined(data[key])) &&
      ['readAt'].some((key) => isNullUndefined(data[key]))
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Either chat's data or readAt is required",
      });
    }
  });

export type IndividualChatSaveMessageDataI = z.infer<typeof IndividualChatSaveMessageDataSchema>;

export const IndividualChatFindAllSchema = PaginationSchema;
export type IndividualChatFindAllI = z.infer<typeof IndividualChatFindAllSchema>;

export const IndividualChatFindAllSpecificProfileSchema = PaginationSchema.extend({
  profileId: UUIDSchema,
});
export type IndividualChatFindAllSpecificProfileI = z.infer<typeof IndividualChatFindAllSpecificProfileSchema>;

export const IndividualChatDeleteManyAllSchema = z.object({
  profileIds: z
    .array(UUIDSchema)
    .min(1)
    .transform((data) => Array.from(new Set(data))),
});
export type IndividualChatDeleteManyAllI = z.infer<typeof IndividualChatDeleteManyAllSchema>;

export const IndividualChatDeleteManySpecificProfileSchema = z.object({
  ids: z.array(ObjectIdSchema).min(1),
  profileId: UUIDSchema,
});
export type IndividualChatDeleteManySpecificProfileI = z.infer<typeof IndividualChatDeleteManySpecificProfileSchema>;

export const IndividualChatDeleteForEveryoneSchema = z.object({
  id: ObjectIdSchema,
});
export type IndividualChatDeleteForEveryoneI = z.infer<typeof IndividualChatDeleteForEveryoneSchema>;

export const IndividualChatEditMessageSchema = z.object({
  id: ObjectIdSchema,
  content: MessageContentSchema,
});
export type IndividualChatEditMessageI = z.infer<typeof IndividualChatEditMessageSchema>;
export const IndividualChatDeleteSpecificProfileChatSchema = z.object({
  profileId: z.string().min(1),
});
export type IndividualChatDeleteSpecificProfileChatI = z.infer<typeof IndividualChatDeleteSpecificProfileChatSchema>;
