import { ObjectIdR, SemverR } from '@consts/common/regex/textVersion';
import { z } from 'zod';
export const VersionNoSchema = z.string().regex(SemverR, 'x-version-no is invalid');
export const UUIDSchema = z.string().uuid();
export type UUIDI = z.infer<typeof UUIDSchema>;
export const ObjectIdSchema = z.string().regex(ObjectIdR, {
  message: 'Invalid ObjectId format',
});
export const PaginationSchema = z.object({
  page: z.union([z.string(), z.number()]),
  pageSize: z
    .union([z.string(), z.number()])
    .default(10)
    .transform((data) => parseInt(String(data)))
    .refine((data) => data > 0 && data <= 10, {
      message: 'pageSize must be between 1 and 10',
    }),
});
export type PaginationSchemaI = z.infer<typeof PaginationSchema>;
export const RouteParamsSchema = z.object({
  id: UUIDSchema,
});
export type RouteParamsI = z.infer<typeof RouteParamsSchema>;
