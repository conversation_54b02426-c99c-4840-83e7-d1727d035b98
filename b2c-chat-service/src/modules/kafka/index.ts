import type { KafkaTopicI } from '@consts/kafka';
import type { ObjUnknownI } from '@interfaces/common/data';
import ChatModule from '@modules/chat';
import { FastifyRequestI } from '@interfaces/common/declaration';
import KafkaService from '@services/kafka';
import { MessageData } from './types';

const KafkaModule = {
  crudMessage: async ({ data, topic }: ObjUnknownI): Promise<void> => {
    const messageData = data as MessageData;
    switch (topic as KafkaTopicI) {
      case 'individual-chat': {
        try {
          const result = await ChatModule.IndividualChatModule.upsertMessage({ data: messageData });
          const responseData = {
            type: 'message-created',
            data: {
              id: result,
              senderId: messageData.senderId,
              recieverId: messageData.recieverId,
              content: messageData.content,
              messageType: messageData.messageType || 'TEXT',
              replyTo: messageData.replyTo || null,
              readAt: null,
              createdAt: new Date(),
              editedAt: null,
              deletedForAll: false,
              deletedFor: [],
            },
            originalSenderId: messageData.senderId,
            originalRecieverId: messageData.recieverId,
          };
          await KafkaService.instance.sendMessage('socket-message-response', responseData);
        } catch (error) {
          const errorData = {
            type: 'message-error',
            data: {
              error: 'Failed to create message',
              originalData: messageData,
            },
            originalSenderId: messageData.senderId,
            originalRecieverId: messageData.recieverId,
          };
          await KafkaService.instance.sendMessage('socket-message-response', errorData);
        }
        break;
      }
      case 'delete-for-everyone': {
        try {
          if (messageData.senderId && messageData.ids) {
            const request = { profileId: messageData.profileId } as FastifyRequestI;
            await ChatModule.IndividualChatModule.deleteManySpecificProfile(request, messageData);
            const responseData = {
              type: 'message-deleted',
              data: {
                id: messageData.id,
                senderId: messageData.senderId,
                recieverId: messageData.recieverId,
              },
              originalSenderId: messageData.senderId,
              originalRecieverId: messageData.recieverId,
            };
            await KafkaService.instance.sendMessage('socket-message-response', responseData);
          } else {
            const errorData = {
              type: 'delete-error',
              data: {
                error: 'Invalid delete for everyone data',
                originalData: messageData,
              },
              originalSenderId: messageData.senderId,
              originalRecieverId: messageData.recieverId,
            };
            await KafkaService.instance.sendMessage('socket-message-response', errorData);
          }
        } catch (error) {
          const errorData = {
            type: 'delete-error',
            data: {
              error: 'Failed to delete message for everyone',
              originalData: messageData,
            },
            originalSenderId: messageData.senderId,
            originalRecieverId: messageData.recieverId,
          };
          await KafkaService.instance.sendMessage('socket-message-response', errorData);
        }
        break;
      }
      case 'delete-for-me': {
        try {
          if (messageData.senderId && messageData.ids && messageData.recieverId) {
            const request = { profileId: messageData.profileId } as FastifyRequestI;
            await ChatModule.IndividualChatModule.deleteManySpecificProfile(request, messageData);
            const responseData = {
              type: 'messages-deleted',
              data: {
                ids: messageData.ids,
                senderId: messageData.senderId,
                recieverId: messageData.recieverId,
              },
              originalSenderId: messageData.senderId,
              originalRecieverId: messageData.recieverId,
            };
            await KafkaService.instance.sendMessage('socket-message-response', responseData);
          } else {
            const errorData = {
              type: 'delete-error',
              data: {
                error: 'Invalid delete for me data',
                originalData: messageData,
              },
              originalSenderId: messageData.senderId,
              originalRecieverId: messageData.recieverId,
            };
            await KafkaService.instance.sendMessage('socket-message-response', errorData);
          }
        } catch (error) {
          const errorData = {
            type: 'delete-error',
            data: {
              error: 'Failed to delete messages for user',
              originalData: messageData,
            },
            originalSenderId: messageData.senderId,
            originalRecieverId: messageData.recieverId,
          };
          await KafkaService.instance.sendMessage('socket-message-response', errorData);
        }
        break;
      }
      case 'edit-message': {
        try {
          if (messageData.senderId && messageData.id && messageData.content) {
            const request = { profileId: messageData.profileId } as FastifyRequestI;
            await ChatModule.IndividualChatModule.editMessage(request, messageData);
            const responseData = {
              type: 'message-edited',
              data: {
                id: messageData.id,
                senderId: messageData.senderId,
                recieverId: messageData.recieverId,
                content: messageData.content,
                editedAt: new Date(),
              },
              originalSenderId: messageData.senderId,
              originalRecieverId: messageData.recieverId,
            };
            await KafkaService.instance.sendMessage('socket-message-response', responseData);
          } else {
            const errorData = {
              type: 'edit-error',
              data: {
                error: 'Invalid edit message data',
                originalData: messageData,
              },
              originalSenderId: messageData.senderId,
              originalRecieverId: messageData.recieverId,
            };
            await KafkaService.instance.sendMessage('socket-message-response', errorData);
          }
        } catch (error) {
          const errorData = {
            type: 'edit-error',
            data: {
              error: 'Failed to edit message',
              originalData: messageData,
            },
            originalSenderId: messageData.senderId,
            originalRecieverId: messageData.recieverId,
          };
          await KafkaService.instance.sendMessage('socket-message-response', errorData);
        }
        break;
      }
      case 'user-status-update': {
        try {
          await ChatModule.IndividualChatModule.updateUserStatus(messageData);
          const responseData = {
            type: 'user-status-resp',
            data: {
              profileId: messageData.profileId,
              status: messageData.status,
              lastSeen: messageData.lastSeen,
            },
          };
          await KafkaService.instance.sendMessage('socket-message-response', responseData);
        } catch (error) {
          const errorData = {
            type: 'user-status-error',
            data: {
              error: 'Failed to update user status',
              originalData: messageData,
            },
          };
          await KafkaService.instance.sendMessage('socket-message-response', errorData);
        }
        break;
      }
      default:
        break;
    }
  },
};

export default KafkaModule;
