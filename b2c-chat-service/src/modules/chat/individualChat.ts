import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';

import type {
  ChatListResponse,
  IndividualChatMessageRawResultI,
  IndividualChatMessageResultI,
  IndividualChatUpsertMessageI,
  MessageListResponse,
} from '@interfaces/chat/individual';
import type { RunCommandRawI } from '@interfaces/common/db';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import type { Prisma } from '@prisma/mongodb';
import { IndividualChatSaveMessageDataSchema } from '@schemas/chat/chat';
import type {
  IndividualChatDeleteManyAllI,
  IndividualChatFindAllI,
  IndividualChatFindAllSpecificProfileI,
} from '@schemas/chat/chat';

const IndividualChatModule = {
  findAll: async (
    request: FastifyRequestI,
    { page = 0, pageSize = 20 }: IndividualChatFindAllI,
  ): Promise<ChatListResponse> => {
    const selfProfileId = request.profileId;
    const skip = Number.parseInt(page as string) * pageSize;

    const pipeline = [
      {
        $match: {
          $or: [{ senderId: selfProfileId }, { recieverId: selfProfileId }],
          deletedFor: { $nin: [selfProfileId] },
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $group: {
          _id: {
            $cond: [{ $eq: ['$senderId', selfProfileId] }, '$recieverId', '$senderId'],
          },
          latestMessage: { $first: '$$ROOT' },
        },
      },
      {
        $replaceRoot: {
          newRoot: '$latestMessage',
        },
      },
      {
        $addFields: {
          otherUserId: {
            $cond: [{ $eq: ['$senderId', selfProfileId] }, '$recieverId', '$senderId'],
          },
        },
      },
      {
        $lookup: {
          from: 'UserStatus',
          localField: 'otherUserId',
          foreignField: 'profileId',
          as: 'userStatus',
        },
      },
      {
        $addFields: {
          userStatus: {
            $cond: [
              { $gt: [{ $size: '$userStatus' }, 0] },
              { $arrayElemAt: ['$userStatus', 0] },
              { status: 'offline', lastSeen: null },
            ],
          },
        },
      },
      {
        $project: {
          _id: 1,
          senderId: 1,
          recieverId: 1,
          content: 1,
          readAt: 1,
          createdAt: 1,
          userStatus: {
            status: '$userStatus.status',
            lastSeen: '$userStatus.lastSeen',
          },
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $facet: {
          data: [{ $skip: skip }, { $limit: pageSize }],
          totalCount: [{ $count: 'count' }],
        },
      },
    ];

    const result = (await prismaMG.$runCommandRaw({
      aggregate: 'IndividualChat',
      pipeline,
      explain: false,
    })) as unknown as RunCommandRawI<{
      data: (IndividualChatMessageRawResultI & {
        userStatus: { status: string; lastSeen: Date | null };
      })[];
      totalCount: [{ count: number }];
    }>;

    const data = result?.cursor?.firstBatch?.[0]?.data || [];
    const total = result?.cursor?.firstBatch?.[0]?.totalCount?.[0]?.count || 0;
    const hasMore = skip + data.length < total;
    const individualChatResult: (IndividualChatMessageResultI & {
      userStatus: { status: string; lastSeen: Date | null };
    })[] = data.map((item) => ({
      id: item._id,
      senderId: item.senderId,
      recieverId: item.recieverId,
      content: parseContent(item.content),
      readAt: item.readAt,
      createdAt: item.createdAt,
      userStatus: item.userStatus,
    }));

    return {
      data: individualChatResult,
      total,
      hasMore,
    };
  },
  findAllSpecificProfile: async (
    request: FastifyRequestI,
    { page = 0, pageSize = 20, profileId }: IndividualChatFindAllSpecificProfileI,
  ): Promise<MessageListResponse> => {
    const selfProfileId = request.profileId;
    const skip = Number.parseInt(page as string) * pageSize;

    const [results, total, userStatus] = await Promise.all([
      prismaMG.individualChat.findMany({
        where: {
          OR: [
            { senderId: selfProfileId, recieverId: profileId },
            { senderId: profileId, recieverId: selfProfileId },
          ],
          NOT: {
            deletedFor: { has: selfProfileId },
          },
        },
        select: {
          id: true,
          senderId: true,
          recieverId: true,
          content: true,
          messageType: true,
          replyTo: true,
          readAt: true,
          createdAt: true,
          editedAt: true,
          deletedForAll: true,
          deletedFor: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
        skip,
        take: pageSize,
      }),
      prismaMG.individualChat.count({
        where: {
          OR: [
            { senderId: selfProfileId, recieverId: profileId },
            { senderId: profileId, recieverId: selfProfileId },
          ],
          NOT: {
            deletedFor: { has: selfProfileId },
          },
        },
      }),
      prismaMG.userStatus.findUnique({
        where: {
          profileId: profileId,
        },
        select: {
          status: true,
          lastSeen: true,
        },
      }),
    ]);

    const hasMore = skip + results.length < total;

    return {
      data: results.map((item) => ({
        id: item.id,
        senderId: item.senderId,
        recieverId: item.recieverId,
        content: item.content,
        messageType: item.messageType,
        replyTo: item.replyTo,
        readAt: item.readAt,
        createdAt: item.createdAt,
        editedAt: item.editedAt,
        deletedForAll: item.deletedForAll,
        deletedFor: item.deletedFor,
        profileId: selfProfileId,
      })),
      total,
      hasMore,
      lastSeen: userStatus?.lastSeen || null,
    };
  },
  upsertMessage: async ({ data: dataP }: IndividualChatUpsertMessageI): Promise<string> => {
    const { error, data } = IndividualChatSaveMessageDataSchema.safeParse(dataP);
    if (error) {
      throw new AppError('INDCHT001');
    }
    const messageType = determineMessageType(data.content);
    const mediaData =
      data.content?.media?.map((media) => ({
        url: media.url || '',
        mimeType: media.mimeType || 'TEXT',
        name: media.name || null,
      })) || [];
    const result = await prismaMG.individualChat.create({
      data: {
        recieverId: data.recieverId,
        senderId: data.senderId,
        content: {
          text: data.content?.text || null,
          media: mediaData,
        },
        messageType,
        replyTo: data.replyTo,
      },
      select: {
        id: true,
      },
    });
    return result.id;
  },

  editMessage: async (request: FastifyRequestI, messageData: any): Promise<void> => {
    const selfProfileId = request.profileId;
    const message = await prismaMG.individualChat.findUnique({
      where: { id: messageData.id },
      select: { senderId: true, createdAt: true, deletedForAll: true },
    });
    if (!message) {
      throw new AppError('INDCHT010');
    }
    if (message.deletedForAll) {
      throw new AppError('INDCHT010');
    }
    if (message.senderId !== selfProfileId) {
      throw new AppError('INDCHT015');
    }
    const editTimeLimit = new Date(message.createdAt);
    editTimeLimit.setMinutes(editTimeLimit.getMinutes() + 15);
    if (new Date() > editTimeLimit) {
      throw new AppError('INDCHT014');
    }
    const messageType = determineMessageType(messageData.content);
    const mediaData =
      messageData.content?.media?.map((media) => ({
        id: media.id || '',
        url: media.url || '',
        mimeType: media.mimeType || 'TEXT',
        name: media.name || null,
      })) || [];

    await prismaMG.individualChat.update({
      where: { id: messageData.id },
      data: {
        content: {
          text: messageData.content?.text || null,
          media: mediaData,
        },
        messageType,
        editedAt: new Date(),
      },
    });
  },

  deleteManyAll: async (request: FastifyRequestI, { profileIds }: IndividualChatDeleteManyAllI): Promise<void> => {
    const selfProfileId = request.profileId;
    if (!profileIds?.length) {
      throw new AppError('INDCHT007');
    }
    await prismaMG.individualChat.updateMany({
      where: {
        OR: profileIds.reduce((acc, profileId) => {
          acc.push(
            ...[
              { senderId: selfProfileId, recieverId: profileId },
              { senderId: profileId, recieverId: selfProfileId },
            ],
          );
          return acc;
        }, []),
        deletedForAll: false,
        NOT: {
          deletedFor: {
            has: selfProfileId,
          },
        },
      },
      data: {
        deletedFor: {
          push: selfProfileId,
        },
      },
    });
  },

  deleteManySpecificProfile: async (request: FastifyRequestI, payload: any): Promise<any> => {
    const selfProfileId = request.profileId;
    const { senderId, recieverId, ids, type } = payload;

    if (!ids?.length) {
      throw new AppError('INDCHT003');
    }

    const whereCondition: Prisma.IndividualChatWhereInput = {
      id: { in: ids },
      OR: [
        { senderId: senderId, recieverId: recieverId },
        { senderId: recieverId, recieverId: senderId },
      ],
      NOT: {
        deletedFor: {
          has: selfProfileId,
        },
      },
    };

    const messagesToUpdate = await prismaMG.individualChat.findMany({
      where: whereCondition,
      select: {
        id: true,
        senderId: true,
        recieverId: true,
        deletedFor: true,
        deletedForAll: true,
      },
    });

    if (type === 'FOR_ME') {
      for (const message of messagesToUpdate) {
        const currentDeletedFor = Array.isArray(message.deletedFor) ? message.deletedFor : [];
        if (!currentDeletedFor.includes(selfProfileId)) {
          const updatedDeletedFor = [...currentDeletedFor, selfProfileId];
          await prismaMG.individualChat.update({
            where: { id: message.id },
            data: {
              deletedFor: updatedDeletedFor,
            },
          });
        }
      }
    } else if (type === 'FOR_EVERYONE') {
      const senderMessages = messagesToUpdate.filter((msg) => msg.senderId === senderId);

      if (senderMessages.length > 0) {
        await prismaMG.individualChat.updateMany({
          where: {
            id: { in: senderMessages.map((msg) => msg.id) },
            senderId: senderId,
          },
          data: {
            deletedForAll: true,
          },
        });
      }

      const receiverMessages = messagesToUpdate.filter((msg) => msg.senderId !== selfProfileId);
      for (const message of receiverMessages) {
        const currentDeletedFor = Array.isArray(message.deletedFor) ? message.deletedFor : [];
        if (!currentDeletedFor.includes(selfProfileId)) {
          const updatedDeletedFor = [...currentDeletedFor, selfProfileId];
          await prismaMG.individualChat.update({
            where: { id: message.id },
            data: {
              deletedFor: updatedDeletedFor,
              deletedForAll: true,
              content: {
                text: 'This message has been deleted',
                media: [],
              },
            },
          });
        }
      }
    }

    return {
      success: true,
      deletedCount: messagesToUpdate.length,
      messageIds: messagesToUpdate.map((msg) => msg.id),
    };
  },

  deleteSpecificProfileChat: async (request: FastifyRequestI, { profileId }: { profileId: string }): Promise<void> => {
    const selfProfileId = request.profileId;

    if (!profileId) {
      throw new AppError('INDCHT005');
    }

    await prismaMG.individualChat.updateMany({
      where: {
        OR: [
          { senderId: selfProfileId, recieverId: profileId },
          { senderId: profileId, recieverId: selfProfileId },
        ],
        NOT: {
          deletedFor: {
            has: selfProfileId,
          },
        },
      },
      data: {
        deletedFor: {
          push: selfProfileId,
        },
      },
    });
  },

  updateUserStatus: async (messageData: any): Promise<void> => {
    await prismaMG.userStatus.upsert({
      where: {
        profileId: messageData.profileId,
      },
      update: {
        status: messageData.status,
        lastSeen: new Date(messageData.lastSeen),
      },
      create: {
        profileId: messageData.profileId,
        status: messageData.status,
        lastSeen: new Date(messageData.lastSeen),
      },
    });
  },
};

function determineMessageType(content: any): 'TEXT' | 'MEDIA' | 'MIXED' {
  const hasText = !!content?.text && content.text.trim().length > 0;
  const hasMedia = Array.isArray(content?.media) && content.media.length > 0;

  if (hasText && hasMedia) return 'MIXED';
  if (hasMedia) return 'MEDIA';
  return 'TEXT';
}

const parseContent = (
  content: Prisma.JsonValue,
): {
  text?: string;
  media?: {
    url?: string;
    mimeType?: 'JPEG' | 'TEXT' | 'PDF';
    name?: string;
  }[];
} => {
  if (typeof content === 'object' && content !== null && !Array.isArray(content)) {
    return content as {
      text?: string;
      media?: {
        url?: string;
        mimeType?: 'JPEG' | 'TEXT' | 'PDF';
        name?: string;
      }[];
    };
  }
  return {};
};

export default IndividualChatModule;
