generator client {
  provider = "prisma-client-js"
  output   = "../../node_modules/@prisma/mongodb"
}

datasource db {
  provider = "mongodb"
  url      = env("MONGO_DATABASE_URL")
}

model IndividualChat {
  id            String          @id @default(auto()) @map("_id") @db.ObjectId
  senderId      String
  recieverId    String
  messageType   MessageTypeE    @default(TEXT)
  content       MessageContentI
  replyTo       String?         @db.ObjectId
  readAt        DateTime?       @db.Timestamp
  deletedFor    String[]        @default([])
  deletedForAll Boolean         @default(false)
  editedAt      DateTime?       @db.Timestamp
  createdAt     DateTime        @default(now()) @db.Timestamp
  updatedAt     DateTime        @updatedAt @db.Timestamp

  @@index([senderId, recieverId, createdAt])
  @@index([recieverId, senderId, createdAt])
  @@index([deletedFor])
  @@index([deletedForAll])
  @@index([replyTo])
}

model UserStatus {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  profileId String   @unique
  status    String   @default("offline")
  lastSeen  DateTime @default(now()) @db.Timestamp

  @@index([status])
}

type MessageContentI {
  text  String?
  media MediaI[]
}

type MediaI {
  url      String
  mimeType MimeTypeE
  name     String?
}

enum MessageTypeE {
  TEXT
  MEDIA
  MIXED
}

enum MimeTypeE {
  JPEG
  TEXT
  PDF
  MP3
  MP4
}
