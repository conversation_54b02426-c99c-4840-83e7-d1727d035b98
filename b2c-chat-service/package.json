{"name": "b2c-chat-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"prepare": "if [ \"$NODE_ENV\" = \"development\" ]; then husky install; fi", "dev": "ts-node-dev --respawn --transpile-only --require tsconfig-paths/register --poll --watch src src/index.ts", "start": "node ./dist/index.js", "build": "tsc -p tsconfig.json && tsc-alias -p tsconfig.json", "lint": "eslint . --ext .ts --ignore-pattern dist/", "lint:fix": "eslint . --ext .ts --ignore-pattern dist/ --fix", "fmt": "prettier --write . && npm run fmt:all", "lint-staged": "lint-staged", "migrate:mg": "prisma db push --schema=./prisma/mongodb/schema.prisma", "migrate:mg1": "prisma migrate dev --name init --schema=./prisma/mongodb/schema.prisma", "migrate": "npm run migrate:mg", "reset:mg": "prisma migrate reset --schema=./prisma/mongodb/schema.prisma", "reset:all": " npm run reset:mg", "deploy:mg": "prisma migrate deploy --schema=./prisma/mongodb/schema.prisma", "deploy:all": "npm run deploy:mg", "generate:mg": "prisma generate --schema=prisma/mongodb/schema.prisma", "generate": "npm run generate:mg", "fmt:mg": "prisma format --schema=prisma/mongodb/schema.prisma", "fmt:all": "npm run fmt:mg"}, "repository": {"type": "git", "url": "git+https://github.com/navicater/b2c-chat-service.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/navicater/b2c-chat-service/issues"}, "homepage": "https://github.com/navicater/b2c-chat-service#readme", "dependencies": {"@fastify/autoload": "^6.0.3", "@fastify/cors": "^10.0.1", "@fastify/env": "^5.0.1", "@fastify/helmet": "^13.0.1", "@fastify/swagger": "^9.4.0", "@fastify/swagger-ui": "^5.2.1", "@fastify/under-pressure": "^9.0.3", "@prisma/client": "^6.7.0", "@types/sharp": "^0.31.1", "dotenv": "^16.4.7", "dotenv-expand": "^12.0.1", "fastify": "^5.2.0", "fastify-cli": "^7.1.0", "fastify-healthcheck": "^5.1.0", "fastify-type-provider-zod": "^4.0.2", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "sharp": "^0.34.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/eslint__js": "^8.42.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "fastify-tsconfig": "^2.0.0", "husky": "^9.1.7", "lint-staged": "^15.2.5", "prettier": "^3.2.5", "prisma": "^6.0.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.22.0"}}