ARG DEPS_IMAGE=node:22-alpine
ARG RUN_IMAGE=node:22-alpine
###########################

# Stage 1: Dependency installation

FROM $DEPS_IMAGE AS deps

WORKDIR /app

# Copy files required for dependency installation
COPY package.json package-lock.json* ./
 
# Install all dependencies
RUN npm ci --legacy-peer-deps

# Copy Prisma schema for generation
COPY prisma ./prisma

# Generate Prisma client
RUN npm run generate

###########################

### Stage 2: Runtime

FROM $RUN_IMAGE AS runtime

WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Copy node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy generated Prisma client
COPY --from=deps /app/node_modules/.prisma ./node_modules/.prisma

# Copy source code and config files
COPY src ./src
COPY prisma ./prisma
COPY tsconfig.json ./

# App's port
EXPOSE 4002

# Start the server with migration and dev mode
CMD ["sh", "-c", "npm run migrate && npm run dev"]

###########################
