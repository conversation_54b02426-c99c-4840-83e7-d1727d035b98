name: Deploy Web Application
on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy-to-production:
    runs-on: ubuntu-latest
    name: Deploy to Production
    steps:
      - name: Checkout network-web-app
        uses: actions/checkout@v4
        with:
          repository: navicater/network-web-app
          path: network-web-app
          token: ${{ secrets.ORG_GITHUB_TOKEN }}

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.DO_SSH_PRIVATE_KEY_DEV }}
          known_hosts: unnecessary

      - name: Compress & Transfer App
        run: |
          tar -czf web-app-deployment.tar.gz -C network-web-app .
          rsync -avz --progress -e "ssh -o StrictHostKeyChecking=no" web-app-deployment.tar.gz root@${{ secrets.DO_VM_HOST }}:/app/
          ssh -o StrictHostKeyChecking=no root@${{ secrets.DO_VM_HOST }} << 'EOF'
          cd /app
          rm -rf web-app-source
          mkdir -p web-app-source
          tar -xzf web-app-deployment.tar.gz -C web-app-source/
          rm web-app-deployment.tar.gz
          EOF

      - name: Deploy & Restart
        run: |
          ssh -o StrictHostKeyChecking=no root@${{ secrets.DO_VM_HOST }} << 'EOF'
          cd /app/web-app-source
          cat > .env.production << 'ENVEOF'
          NEXT_PUBLIC_BASE_URL=${{ secrets.NEXT_PUBLIC_BASE_URL }}
          NEXT_PUBLIC_API_KEY=${{ secrets.NEXT_PUBLIC_API_KEY }}
          NEXT_PUBLIC_VERSION_NO=${{ secrets.NEXT_PUBLIC_VERSION_NO }}
          NEXT_PUBLIC_ENV=production
          GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }}
          GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET }}
          AUTH_GOOGLE_ID=${{ secrets.AUTH_GOOGLE_ID }}
          AUTH_GOOGLE_SECRET=${{ secrets.AUTH_GOOGLE_SECRET }}
          NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL=${{ secrets.NEXTAUTH_URL }}
          NODE_ENV=production
          ENVEOF
          npm install --legacy-peer-deps
          npm run build
          pm2 restart webapp --update-env 2>/dev/null || pm2 start npm --name "webapp" -- start
          pm2 save
          EOF
