DOCKER_COMPOSE = sudo docker compose
PROD_SERVICE = app-prod
DEV_SERVICE = app-dev

.PHONY: help
help:
	@echo "Available commands:"
	@echo "  make start-prod    - Start production environment"
	@echo "  make start-dev     - Start development environment"
	@echo "  make stop          - Stop all services"
	@echo "  make scale-prod N=X - Scale production service to X instances"
	@echo "  make logs-prod     - View logs for production service"
	@echo "  make logs-dev      - View logs for development service"
	@echo "  make build         - Build all services"
	@echo "  make clean         - Remove all containers and images"

.PHONY: start-prod
start-prod:
	$(DOCKER_COMPOSE) up -d $(PROD_SERVICE)

.PHONY: start-dev
start-dev:
	$(DOCKER_COMPOSE) up -d $(DEV_SERVICE)

.PHONY: stop
stop:
	$(DOCKER_COMPOSE) down

.PHONY: scale-prod
scale-prod:
	$(DOCKER_COMPOSE) up -d --scale $(PROD_SERVICE)=$(N) $(PROD_SERVICE)

.PHONY: logs-prod
logs-prod:
	$(DOCKER_COMPOSE) logs -f $(PROD_SERVICE)

.PHONY: logs-dev
logs-dev:
	$(DOCKER_COMPOSE) logs -f $(DEV_SERVICE)

.PHONY: build
build:
	$(DOCKER_COMPOSE) build

.PHONY: clean
clean:
	$(DOCKER_COMPOSE) down --rmi all -v --remove-orphans
