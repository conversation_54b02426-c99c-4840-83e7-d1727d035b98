import {
  NavicaterAiIcon,
  NavicaterAiIconActive,
  LibraryIcon,
  LibraryIconActive,
  AdminPanelIcon,
  AdminPanelIconActive,
  SettingsIcon,
  SettingsIconActive,
} from "@/assets/images/svgs/common";

export const sidebarItems = [
  {
    icon: <NavicaterAiIcon />,
    iconActive: <NavicaterAiIconActive />,
    label: "globalSidebar.navigation.navicater_ai.label",
    href: "/assistant",
    isCollapsible: false,
    isExpanded: false,
    isBottom: false,
  },
  {
    icon: <LibraryIcon />,
    iconActive: <LibraryIconActive />,
    label: "globalSidebar.navigation.library.label",
    href: "/library",
    isCollapsible: false,
    isExpanded: false,
    isBottom: false,
  },
  {
    icon: <AdminPanelIcon />,
    iconActive: <AdminPanelIconActive />,
    label: "globalSidebar.navigation.admin_panel.label",
    href: "/admin",
    isCollapsible: false,
    isExpanded: false,
    isBottom: false,
  },
  {
    icon: <SettingsIcon />,
    iconActive: <SettingsIconActive />,
    label: "globalSidebar.navigation.settings.label",
    href: "/settings",
    isCollapsible: false,
    isExpanded: false,
    isBottom: true,
  },
];
