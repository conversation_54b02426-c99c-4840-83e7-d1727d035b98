import {
  Formats,
  MarkupTranslationValues,
  RichTranslationValues,
  TranslationValues,
} from "next-intl";
import { ComponentProps, ReactNode } from "react";

export type ClassNameI = ComponentProps<"div">["className"];

export type IdTitleI = {
  id: string;
  title: string;
};

type Nullable<T> = T | null;

export type UndefinedNullI = undefined | null;
export type StringUndefinedI = string | undefined;
export type StringUndefinedNullI = string | UndefinedNullI;
export type StringNullI = Nullable<string>;
export type BooleanNullI = Nullable<boolean>;
export type NumberNullI = Nullable<number>;
export type KeyStrStrI = Record<string, string>;
export type StrNullVoidFnI = (id: StringNullI) => void;

export interface TFunctionI {
  <T>(key: T, values?: TranslationValues, formats?: Formats): string;
  rich<T>(key: T, values?: RichTranslationValues, formats?: Formats): ReactNode;
  markup<T>(
    key: T,
    values?: MarkupTranslationValues,
    formats?: Formats,
  ): string;
  raw<T>(key: T): string;
  has<T>(key: T): boolean;
}

export type LanguageSupported = "en" | "es" | "zh-CN" | "zh-TW" | "ar";
