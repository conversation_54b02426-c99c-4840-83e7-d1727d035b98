import React from "react";

export const OrgSetupIcon = () => (
  <svg
    width="23"
    height="18"
    viewBox="0 0 23 18"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M22.25 16.5H20.75V6C20.9489 6 21.1397 5.92098 21.2803 5.78033C21.421 5.63968 21.5 5.44891 21.5 5.25C21.5 5.05109 21.421 4.86032 21.2803 4.71967C21.1397 4.57902 20.9489 4.5 20.75 4.5H16.25V1.5C16.4489 1.5 16.6397 1.42098 16.7803 1.28033C16.921 1.13968 17 0.948912 17 0.75C17 0.551088 16.921 0.360322 16.7803 0.21967C16.6397 0.0790178 16.4489 0 16.25 0H2.75C2.55109 0 2.36032 0.0790178 2.21967 0.21967C2.07902 0.360322 2 0.551088 2 0.75C2 0.948912 2.07902 1.13968 2.21967 1.28033C2.36032 1.42098 2.55109 1.5 2.75 1.5V16.5H1.25C1.05109 16.5 0.860322 16.579 0.71967 16.7197C0.579018 16.8603 0.5 17.0511 0.5 17.25C0.5 17.4489 0.579018 17.6397 0.71967 17.7803C0.860322 17.921 1.05109 18 1.25 18H22.25C22.4489 18 22.6397 17.921 22.7803 17.7803C22.921 17.6397 23 17.4489 23 17.25C23 17.0511 22.921 16.8603 22.7803 16.7197C22.6397 16.579 22.4489 16.5 22.25 16.5ZM19.25 6V16.5H16.25V6H19.25ZM4.25 1.5H14.75V16.5H12.5V12C12.5 11.8011 12.421 11.6103 12.2803 11.4697C12.1397 11.329 11.9489 11.25 11.75 11.25H7.25C7.05109 11.25 6.86032 11.329 6.71967 11.4697C6.57902 11.6103 6.5 11.8011 6.5 12V16.5H4.25V1.5ZM11 16.5H8V12.75H11V16.5ZM5.75 4.5C5.75 4.30109 5.82902 4.11032 5.96967 3.96967C6.11032 3.82902 6.30109 3.75 6.5 3.75H8C8.19891 3.75 8.38968 3.82902 8.53033 3.96967C8.67098 4.11032 8.75 4.30109 8.75 4.5C8.75 4.69891 8.67098 4.88968 8.53033 5.03033C8.38968 5.17098 8.19891 5.25 8 5.25H6.5C6.30109 5.25 6.11032 5.17098 5.96967 5.03033C5.82902 4.88968 5.75 4.69891 5.75 4.5ZM10.25 4.5C10.25 4.30109 10.329 4.11032 10.4697 3.96967C10.6103 3.82902 10.8011 3.75 11 3.75H12.5C12.6989 3.75 12.8897 3.82902 13.0303 3.96967C13.171 4.11032 13.25 4.30109 13.25 4.5C13.25 4.69891 13.171 4.88968 13.0303 5.03033C12.8897 5.17098 12.6989 5.25 12.5 5.25H11C10.8011 5.25 10.6103 5.17098 10.4697 5.03033C10.329 4.88968 10.25 4.69891 10.25 4.5ZM5.75 8.25C5.75 8.05109 5.82902 7.86032 5.96967 7.71967C6.11032 7.57902 6.30109 7.5 6.5 7.5H8C8.19891 7.5 8.38968 7.57902 8.53033 7.71967C8.67098 7.86032 8.75 8.05109 8.75 8.25C8.75 8.44891 8.67098 8.63968 8.53033 8.78033C8.38968 8.92098 8.19891 9 8 9H6.5C6.30109 9 6.11032 8.92098 5.96967 8.78033C5.82902 8.63968 5.75 8.44891 5.75 8.25ZM10.25 8.25C10.25 8.05109 10.329 7.86032 10.4697 7.71967C10.6103 7.57902 10.8011 7.5 11 7.5H12.5C12.6989 7.5 12.8897 7.57902 13.0303 7.71967C13.171 7.86032 13.25 8.05109 13.25 8.25C13.25 8.44891 13.171 8.63968 13.0303 8.78033C12.8897 8.92098 12.6989 9 12.5 9H11C10.8011 9 10.6103 8.92098 10.4697 8.78033C10.329 8.63968 10.25 8.44891 10.25 8.25Z" />
  </svg>
);

export const OrgSetupIconActive = () => (
  <svg
    width="23"
    height="18"
    viewBox="0 0 23 18"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M22.25 16.5H20.75V6C20.9489 6 21.1397 5.92098 21.2803 5.78033C21.421 5.63968 21.5 5.44891 21.5 5.25C21.5 5.05109 21.421 4.86032 21.2803 4.71967C21.1397 4.57902 20.9489 4.5 20.75 4.5H16.25V1.5C16.4489 1.5 16.6397 1.42098 16.7803 1.28033C16.921 1.13968 17 0.948912 17 0.75C17 0.551088 16.921 0.360322 16.7803 0.21967C16.6397 0.0790178 16.4489 0 16.25 0H2.75C2.55109 0 2.36032 0.0790178 2.21967 0.21967C2.07902 0.360322 2 0.551088 2 0.75C2 0.948912 2.07902 1.13968 2.21967 1.28033C2.36032 1.42098 2.55109 1.5 2.75 1.5V16.5H1.25C1.05109 16.5 0.860322 16.579 0.71967 16.7197C0.579018 16.8603 0.5 17.0511 0.5 17.25C0.5 17.4489 0.579018 17.6397 0.71967 17.7803C0.860322 17.921 1.05109 18 1.25 18H22.25C22.4489 18 22.6397 17.921 22.7803 17.7803C22.921 17.6397 23 17.4489 23 17.25C23 17.0511 22.921 16.8603 22.7803 16.7197C22.6397 16.579 22.4489 16.5 22.25 16.5ZM6.5 3.75H8C8.19891 3.75 8.38968 3.82902 8.53033 3.96967C8.67098 4.11032 8.75 4.30109 8.75 4.5C8.75 4.69891 8.67098 4.88968 8.53033 5.03033C8.38968 5.17098 8.19891 5.25 8 5.25H6.5C6.30109 5.25 6.11032 5.17098 5.96967 5.03033C5.82902 4.88968 5.75 4.69891 5.75 4.5C5.75 4.30109 5.82902 4.11032 5.96967 3.96967C6.11032 3.82902 6.30109 3.75 6.5 3.75ZM5.75 8.25C5.75 8.05109 5.82902 7.86032 5.96967 7.71967C6.11032 7.57902 6.30109 7.5 6.5 7.5H8C8.19891 7.5 8.38968 7.57902 8.53033 7.71967C8.67098 7.86032 8.75 8.05109 8.75 8.25C8.75 8.44891 8.67098 8.63968 8.53033 8.78033C8.38968 8.92098 8.19891 9 8 9H6.5C6.30109 9 6.11032 8.92098 5.96967 8.78033C5.82902 8.63968 5.75 8.44891 5.75 8.25ZM11.75 16.5H7.25V12H11.75V16.5ZM12.5 9H11C10.8011 9 10.6103 8.92098 10.4697 8.78033C10.329 8.63968 10.25 8.44891 10.25 8.25C10.25 8.05109 10.329 7.86032 10.4697 7.71967C10.6103 7.57902 10.8011 7.5 11 7.5H12.5C12.6989 7.5 12.8897 7.57902 13.0303 7.71967C13.171 7.86032 13.25 8.05109 13.25 8.25C13.25 8.44891 13.171 8.63968 13.0303 8.78033C12.8897 8.92098 12.6989 9 12.5 9ZM12.5 5.25H11C10.8011 5.25 10.6103 5.17098 10.4697 5.03033C10.329 4.88968 10.25 4.69891 10.25 4.5C10.25 4.30109 10.329 4.11032 10.4697 3.96967C10.6103 3.82902 10.8011 3.75 11 3.75H12.5C12.6989 3.75 12.8897 3.82902 13.0303 3.96967C13.171 4.11032 13.25 4.30109 13.25 4.5C13.25 4.69891 13.171 4.88968 13.0303 5.03033C12.8897 5.17098 12.6989 5.25 12.5 5.25ZM19.25 16.5H16.25V6H19.25V16.5Z" />
  </svg>
);

export const UsersIcon = () => (
  <svg
    width="24"
    height="18"
    viewBox="0 0 24 18"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M22.95 10.1C22.8712 10.1591 22.7816 10.2021 22.6862 10.2265C22.5908 10.251 22.4915 10.2564 22.394 10.2424C22.2965 10.2285 22.2027 10.1955 22.1179 10.1453C22.0332 10.0952 21.9591 10.0288 21.9 9.94998C21.448 9.34235 20.8596 8.84935 20.1822 8.51066C19.5048 8.17196 18.7574 7.99705 18 7.99998C17.8526 7.99997 17.7083 7.95647 17.5855 7.87493C17.4626 7.79339 17.3664 7.67743 17.3091 7.54155C17.2702 7.44927 17.2501 7.35014 17.2501 7.24998C17.2501 7.14983 17.2702 7.0507 17.3091 6.95842C17.3664 6.82254 17.4626 6.70657 17.5855 6.62503C17.7083 6.54349 17.8526 6.5 18 6.49998C18.4209 6.49995 18.8332 6.3819 19.1903 6.15924C19.5474 5.93659 19.8349 5.61825 20.0201 5.2404C20.2054 4.86254 20.2809 4.44031 20.2382 4.02167C20.1955 3.60302 20.0363 3.20474 19.7786 2.87207C19.5209 2.53939 19.175 2.28566 18.7803 2.13969C18.3856 1.99371 17.9579 1.96135 17.5458 2.04628C17.1336 2.13121 16.7535 2.33002 16.4487 2.62013C16.1439 2.91024 15.9265 3.28003 15.8213 3.68748C15.7967 3.7829 15.7535 3.87253 15.6942 3.95125C15.635 4.02998 15.5608 4.09627 15.4759 4.14632C15.391 4.19638 15.2971 4.22923 15.1995 4.24299C15.102 4.25676 15.0026 4.25117 14.9072 4.22655C14.8118 4.20192 14.7222 4.15875 14.6435 4.09949C14.5647 4.04023 14.4984 3.96604 14.4484 3.88116C14.3983 3.79628 14.3655 3.70237 14.3517 3.6048C14.338 3.50723 14.3435 3.4079 14.3682 3.31248C14.5142 2.74747 14.7901 2.22433 15.1738 1.78469C15.5576 1.34505 16.0387 1.00105 16.5788 0.780063C17.119 0.559073 17.7032 0.467192 18.2851 0.511732C18.867 0.556271 19.4304 0.736001 19.9306 1.03662C20.4308 1.33724 20.8539 1.75045 21.1663 2.24338C21.4787 2.7363 21.6718 3.29533 21.7301 3.87599C21.7884 4.45665 21.7104 5.04291 21.5023 5.58812C21.2942 6.13333 20.9617 6.62245 20.5313 7.01655C21.5511 7.45811 22.4377 8.15881 23.1028 9.04905C23.1619 9.12804 23.2049 9.21791 23.2292 9.31352C23.2535 9.40913 23.2587 9.5086 23.2445 9.60622C23.2303 9.70384 23.197 9.79771 23.1465 9.88243C23.0959 9.96716 23.0292 10.0411 22.95 10.1ZM17.8988 15.875C17.9531 15.9603 17.9895 16.0558 18.0059 16.1556C18.0223 16.2554 18.0184 16.3575 17.9943 16.4558C17.9703 16.554 17.9266 16.6464 17.8659 16.7273C17.8052 16.8082 17.7288 16.876 17.6412 16.9267C17.5536 16.9773 17.4567 17.0097 17.3563 17.0219C17.2559 17.0341 17.154 17.0258 17.0569 16.9976C16.9597 16.9695 16.8693 16.9219 16.791 16.8579C16.7127 16.7938 16.6482 16.7146 16.6013 16.625C16.1288 15.825 15.456 15.162 14.6491 14.7014C13.8422 14.2409 12.9291 13.9986 12 13.9986C11.0709 13.9986 10.1579 14.2409 9.35102 14.7014C8.54412 15.162 7.87125 15.825 7.39879 16.625C7.3519 16.7146 7.28737 16.7938 7.20908 16.8579C7.13078 16.9219 7.04034 16.9695 6.94319 16.9976C6.84605 17.0258 6.7442 17.0341 6.64379 17.0219C6.54338 17.0097 6.44647 16.9773 6.35889 16.9267C6.27132 16.876 6.19489 16.8082 6.1342 16.7273C6.07352 16.6464 6.02983 16.554 6.00576 16.4558C5.9817 16.3575 5.97775 16.2554 5.99417 16.1556C6.01059 16.0558 6.04702 15.9603 6.10129 15.875C6.82841 14.6257 7.9371 13.6425 9.26441 13.07C8.51753 12.4981 7.96863 11.7066 7.69485 10.8067C7.42108 9.90677 7.4362 8.94366 7.73808 8.05277C8.03997 7.16188 8.61346 6.38798 9.37792 5.83987C10.1424 5.29175 11.0594 4.99698 12 4.99698C12.9407 4.99698 13.8577 5.29175 14.6222 5.83987C15.3866 6.38798 15.9601 7.16188 16.262 8.05277C16.5639 8.94366 16.579 9.90677 16.3052 10.8067C16.0315 11.7066 15.4825 12.4981 14.7357 13.07C16.063 13.6425 17.1717 14.6257 17.8988 15.875ZM12 12.5C12.5934 12.5 13.1734 12.324 13.6667 11.9944C14.1601 11.6647 14.5446 11.1962 14.7717 10.648C14.9987 10.0999 15.0582 9.49666 14.9424 8.91471C14.8266 8.33277 14.5409 7.79822 14.1214 7.37866C13.7018 6.95911 13.1673 6.67338 12.5853 6.55763C12.0034 6.44187 11.4002 6.50128 10.852 6.72834C10.3038 6.95541 9.83528 7.33993 9.50563 7.83327C9.17599 8.32662 9.00004 8.90664 9.00004 9.49998C9.00004 10.2956 9.31611 11.0587 9.87872 11.6213C10.4413 12.1839 11.2044 12.5 12 12.5ZM6.75004 7.24998C6.75004 7.05107 6.67102 6.86031 6.53037 6.71965C6.38972 6.579 6.19895 6.49998 6.00004 6.49998C5.57922 6.49995 5.16685 6.3819 4.80976 6.15924C4.45267 5.93659 4.16518 5.61825 3.97994 5.2404C3.79471 4.86254 3.71915 4.44031 3.76185 4.02167C3.80455 3.60302 3.9638 3.20474 4.22152 2.87207C4.47923 2.53939 4.82507 2.28566 5.21976 2.13969C5.61445 1.99371 6.04216 1.96135 6.45432 2.04628C6.86648 2.13121 7.24656 2.33002 7.55139 2.62013C7.85622 2.91024 8.07359 3.28003 8.17879 3.68748C8.22852 3.88018 8.35276 4.04523 8.52418 4.14632C8.6956 4.24742 8.90016 4.27627 9.09285 4.22655C9.28555 4.17682 9.4506 4.05258 9.55169 3.88116C9.65279 3.70974 9.68164 3.50518 9.63192 3.31248C9.48589 2.74747 9.21002 2.22433 8.82624 1.78469C8.44246 1.34505 7.96137 1.00105 7.42125 0.780063C6.88112 0.559073 6.29688 0.467192 5.715 0.511732C5.13312 0.556271 4.56967 0.736001 4.06947 1.03662C3.56928 1.33724 3.14614 1.75045 2.83375 2.24338C2.52135 2.7363 2.32831 3.29533 2.26998 3.87599C2.21165 4.45665 2.28965 5.04291 2.49777 5.58812C2.70589 6.13333 3.03838 6.62245 3.46879 7.01655C2.44999 7.45852 1.56446 8.15919 0.90004 9.04905C0.780569 9.20818 0.729204 9.40825 0.757247 9.60525C0.785289 9.80225 0.890442 9.98004 1.04957 10.0995C1.2087 10.219 1.40877 10.2704 1.60577 10.2423C1.80278 10.2143 1.98057 10.1091 2.10004 9.94998C2.55209 9.34235 3.14049 8.84935 3.81788 8.51066C4.49526 8.17196 5.24271 7.99705 6.00004 7.99998C6.19895 7.99998 6.38972 7.92097 6.53037 7.78031C6.67102 7.63966 6.75004 7.4489 6.75004 7.24998Z" />
  </svg>
);

export const UsersIconActive = () => (
  <svg
    width="24"
    height="17"
    viewBox="0 0 24 17"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M6.01126 9.85624C6.0138 9.90704 6.00599 9.95783 5.98828 10.0055C5.97058 10.0532 5.94336 10.0968 5.90828 10.1336C5.8732 10.1704 5.831 10.1998 5.78423 10.2198C5.73747 10.2398 5.68712 10.2501 5.63626 10.25H1.50001C1.33073 10.2502 1.16634 10.1932 1.03358 10.0882C0.900815 9.98319 0.807483 9.83634 0.76876 9.67155C0.744649 9.55876 0.74576 9.44203 0.772015 9.32972C0.79827 9.2174 0.84902 9.11228 0.920635 9.02186C1.58233 8.14435 2.46028 7.45342 3.46876 7.01655C3.026 6.61287 2.68642 6.10897 2.47846 5.54706C2.27049 4.98514 2.2002 4.38158 2.27346 3.78691C2.34672 3.19225 2.56141 2.62379 2.89955 2.12916C3.23768 1.63453 3.68941 1.22812 4.21691 0.943973C4.74441 0.659826 5.33232 0.506212 5.93139 0.495998C6.53047 0.485784 7.12327 0.619267 7.66015 0.885267C8.19703 1.15127 8.66235 1.54204 9.01715 2.02486C9.37195 2.50768 9.60589 3.06848 9.69938 3.6603C9.71146 3.73989 9.6972 3.82124 9.65879 3.89198C9.62037 3.96272 9.5599 4.01897 9.48657 4.05218C8.44631 4.53309 7.56532 5.30153 6.94755 6.26681C6.32977 7.2321 6.00099 8.35395 6.00001 9.49999C6.00001 9.61999 6.00001 9.73812 6.01126 9.85624ZM23.0738 9.02093C22.4136 8.14443 21.5376 7.45388 20.5313 7.01655C20.974 6.61287 21.3136 6.10897 21.5216 5.54706C21.7295 4.98514 21.7998 4.38158 21.7266 3.78691C21.6533 3.19225 21.4386 2.62379 21.1005 2.12916C20.7623 1.63453 20.3106 1.22812 19.7831 0.943973C19.2556 0.659826 18.6677 0.506212 18.0686 0.495998C17.4696 0.485784 16.8768 0.619267 16.3399 0.885267C15.803 1.15127 15.3377 1.54204 14.9829 2.02486C14.6281 2.50768 14.3941 3.06848 14.3006 3.6603C14.2886 3.73989 14.3028 3.82124 14.3412 3.89198C14.3796 3.96272 14.4401 4.01897 14.5134 4.05218C15.5537 4.53309 16.4347 5.30153 17.0525 6.26681C17.6702 7.2321 17.999 8.35395 18 9.49999C18 9.61999 18 9.73812 17.9888 9.85624C17.9862 9.90704 17.994 9.95783 18.0117 10.0055C18.0294 10.0532 18.0567 10.0968 18.0917 10.1336C18.1268 10.1704 18.169 10.1998 18.2158 10.2198C18.2626 10.2398 18.3129 10.2501 18.3638 10.25H22.5C22.6693 10.2502 22.8337 10.1932 22.9664 10.0882C23.0992 9.98319 23.1925 9.83634 23.2313 9.67155C23.2555 9.55855 23.2544 9.44158 23.228 9.32907C23.2015 9.21656 23.1505 9.11131 23.0784 9.02093H23.0738ZM14.73 13.0691C15.4768 12.4971 16.0256 11.7056 16.2992 10.8057C16.5729 9.90579 16.5577 8.94275 16.2558 8.05192C15.9538 7.16109 15.3804 6.38727 14.6159 5.83921C13.8515 5.29115 12.9345 4.99641 11.9939 4.99641C11.0533 4.99641 10.1364 5.29115 9.37191 5.83921C8.60747 6.38727 8.03399 7.16109 7.73206 8.05192C7.43012 8.94275 7.41493 9.90579 7.6886 10.8057C7.96227 11.7056 8.51106 12.4971 9.25782 13.0691C7.9327 13.6432 6.8262 14.6265 6.10032 15.875C6.03449 15.989 5.99983 16.1184 5.99984 16.25C5.99985 16.3817 6.03452 16.511 6.10036 16.6251C6.16621 16.7391 6.26091 16.8338 6.37495 16.8996C6.48899 16.9654 6.61834 17 6.75001 17H17.25C17.3817 17 17.511 16.9654 17.6251 16.8996C17.7391 16.8338 17.8338 16.7391 17.8997 16.6251C17.9655 16.511 18.0002 16.3817 18.0002 16.25C18.0002 16.1184 17.9655 15.989 17.8997 15.875C17.1723 14.6257 16.0637 13.6423 14.7366 13.0691H14.73Z" />
  </svg>
);

export const WorkspacesIcon = () => (
  <svg
    width="22"
    height="17"
    viewBox="0 0 22 17"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M20.75 11.75H20V2.75C20 2.15326 19.7629 1.58097 19.341 1.15901C18.919 0.737053 18.3467 0.5 17.75 0.5H4.25C3.65326 0.5 3.08097 0.737053 2.65901 1.15901C2.23705 1.58097 2 2.15326 2 2.75V11.75H1.25C1.05109 11.75 0.860322 11.829 0.71967 11.9697C0.579018 12.1103 0.5 12.3011 0.5 12.5V14C0.5 14.5967 0.737053 15.169 1.15901 15.591C1.58097 16.0129 2.15326 16.25 2.75 16.25H19.25C19.8467 16.25 20.419 16.0129 20.841 15.591C21.2629 15.169 21.5 14.5967 21.5 14V12.5C21.5 12.3011 21.421 12.1103 21.2803 11.9697C21.1397 11.829 20.9489 11.75 20.75 11.75ZM3.5 2.75C3.5 2.55109 3.57902 2.36032 3.71967 2.21967C3.86032 2.07902 4.05109 2 4.25 2H17.75C17.9489 2 18.1397 2.07902 18.2803 2.21967C18.421 2.36032 18.5 2.55109 18.5 2.75V11.75H3.5V2.75ZM20 14C20 14.1989 19.921 14.3897 19.7803 14.5303C19.6397 14.671 19.4489 14.75 19.25 14.75H2.75C2.55109 14.75 2.36032 14.671 2.21967 14.5303C2.07902 14.3897 2 14.1989 2 14V13.25H20V14ZM13.25 4.25C13.25 4.44891 13.171 4.63968 13.0303 4.78033C12.8897 4.92098 12.6989 5 12.5 5H9.5C9.30109 5 9.11032 4.92098 8.96967 4.78033C8.82902 4.63968 8.75 4.44891 8.75 4.25C8.75 4.05109 8.82902 3.86032 8.96967 3.71967C9.11032 3.57902 9.30109 3.5 9.5 3.5H12.5C12.6989 3.5 12.8897 3.57902 13.0303 3.71967C13.171 3.86032 13.25 4.05109 13.25 4.25Z" />
  </svg>
);

export const WorkspacesIconActive = () => (
  <svg
    width="22"
    height="17"
    viewBox="0 0 22 17"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M20.75 11.75H20V2.75C20 2.15326 19.7629 1.58097 19.341 1.15901C18.919 0.737053 18.3467 0.5 17.75 0.5H4.25C3.65326 0.5 3.08097 0.737053 2.65901 1.15901C2.23705 1.58097 2 2.15326 2 2.75V11.75H1.25C1.05109 11.75 0.860322 11.829 0.71967 11.9697C0.579018 12.1103 0.5 12.3011 0.5 12.5V14C0.5 14.5967 0.737053 15.169 1.15901 15.591C1.58097 16.0129 2.15326 16.25 2.75 16.25H19.25C19.8467 16.25 20.419 16.0129 20.841 15.591C21.2629 15.169 21.5 14.5967 21.5 14V12.5C21.5 12.3011 21.421 12.1103 21.2803 11.9697C21.1397 11.829 20.9489 11.75 20.75 11.75ZM9.5 2.75H12.5C12.6989 2.75 12.8897 2.82902 13.0303 2.96967C13.171 3.11032 13.25 3.30109 13.25 3.5C13.25 3.69891 13.171 3.88968 13.0303 4.03033C12.8897 4.17098 12.6989 4.25 12.5 4.25H9.5C9.30109 4.25 9.11032 4.17098 8.96967 4.03033C8.82902 3.88968 8.75 3.69891 8.75 3.5C8.75 3.30109 8.82902 3.11032 8.96967 2.96967C9.11032 2.82902 9.30109 2.75 9.5 2.75ZM20 14C20 14.1989 19.921 14.3897 19.7803 14.5303C19.6397 14.671 19.4489 14.75 19.25 14.75H2.75C2.55109 14.75 2.36032 14.671 2.21967 14.5303C2.07902 14.3897 2 14.1989 2 14V13.25H20V14Z" />
  </svg>
);

export const RolesIcon = () => (
  <svg
    width="20"
    height="18"
    viewBox="0 0 20 18"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.75 7.5C16.75 7.69891 16.671 7.88968 16.5303 8.03033C16.3897 8.17098 16.1989 8.25 16 8.25H12.25C12.0511 8.25 11.8603 8.17098 11.7197 8.03033C11.579 7.88968 11.5 7.69891 11.5 7.5C11.5 7.30109 11.579 7.11032 11.7197 6.96967C11.8603 6.82902 12.0511 6.75 12.25 6.75H16C16.1989 6.75 16.3897 6.82902 16.5303 6.96967C16.671 7.11032 16.75 7.30109 16.75 7.5ZM16 9.75H12.25C12.0511 9.75 11.8603 9.82902 11.7197 9.96967C11.579 10.1103 11.5 10.3011 11.5 10.5C11.5 10.6989 11.579 10.8897 11.7197 11.0303C11.8603 11.171 12.0511 11.25 12.25 11.25H16C16.1989 11.25 16.3897 11.171 16.5303 11.0303C16.671 10.8897 16.75 10.6989 16.75 10.5C16.75 10.3011 16.671 10.1103 16.5303 9.96967C16.3897 9.82902 16.1989 9.75 16 9.75ZM19.75 2.25V15.75C19.75 16.1478 19.592 16.5294 19.3107 16.8107C19.0294 17.092 18.6478 17.25 18.25 17.25H1.75C1.35218 17.25 0.970644 17.092 0.68934 16.8107C0.408035 16.5294 0.25 16.1478 0.25 15.75V2.25C0.25 1.85218 0.408035 1.47064 0.68934 1.18934C0.970644 0.908035 1.35218 0.75 1.75 0.75H18.25C18.6478 0.75 19.0294 0.908035 19.3107 1.18934C19.592 1.47064 19.75 1.85218 19.75 2.25ZM18.25 15.75V2.25H1.75V15.75H18.25ZM10.7256 12.5625C10.7754 12.7552 10.7465 12.9598 10.6454 13.1312C10.5443 13.3026 10.3793 13.4268 10.1866 13.4766C9.99387 13.5263 9.78931 13.4974 9.61789 13.3963C9.44647 13.2952 9.32223 13.1302 9.2725 12.9375C9.02594 11.9756 8.04812 11.25 6.99906 11.25C5.95 11.25 4.97312 11.9756 4.72563 12.9375C4.6759 13.1302 4.55166 13.2952 4.38024 13.3963C4.20882 13.4974 4.00426 13.5263 3.81156 13.4766C3.61887 13.4268 3.45382 13.3026 3.35272 13.1312C3.25163 12.9598 3.22277 12.7552 3.2725 12.5625C3.51588 11.6566 4.09118 10.8755 4.88406 10.3744C4.46272 9.95553 4.17522 9.42102 4.05802 8.83859C3.94082 8.25616 3.99918 7.65205 4.22572 7.10283C4.45226 6.55361 4.83676 6.08402 5.3305 5.75358C5.82423 5.42315 6.40496 5.24675 6.99906 5.24675C7.59317 5.24675 8.17389 5.42315 8.66763 5.75358C9.16136 6.08402 9.54587 6.55361 9.77241 7.10283C9.99894 7.65205 10.0573 8.25616 9.94011 8.83859C9.8229 9.42102 9.53541 9.95553 9.11406 10.3744C9.90781 10.8748 10.4836 11.6562 10.7266 12.5625H10.7256ZM7 9.75C7.29667 9.75 7.58668 9.66203 7.83335 9.4972C8.08003 9.33238 8.27229 9.09811 8.38582 8.82403C8.49935 8.54994 8.52906 8.24834 8.47118 7.95736C8.4133 7.66639 8.27044 7.39912 8.06066 7.18934C7.85088 6.97956 7.58361 6.8367 7.29264 6.77882C7.00166 6.72094 6.70006 6.75065 6.42597 6.86418C6.15189 6.97771 5.91762 7.16997 5.7528 7.41665C5.58797 7.66332 5.5 7.95333 5.5 8.25C5.5 8.64782 5.65804 9.02936 5.93934 9.31066C6.22064 9.59196 6.60218 9.75 7 9.75Z"
      fill="#404040"
    />
  </svg>
);

export const RolesIconActive = () => (
  <svg
    width="20"
    height="18"
    viewBox="0 0 20 18"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M8.5 8.25C8.5 8.54667 8.41203 8.83668 8.2472 9.08336C8.08238 9.33003 7.84811 9.52229 7.57403 9.63582C7.29994 9.74935 6.99834 9.77906 6.70736 9.72118C6.41639 9.6633 6.14912 9.52044 5.93934 9.31066C5.72956 9.10088 5.5867 8.83361 5.52882 8.54264C5.47094 8.25166 5.50065 7.95006 5.61418 7.67597C5.72771 7.40189 5.91997 7.16762 6.16665 7.0028C6.41332 6.83797 6.70333 6.75 7 6.75C7.39782 6.75 7.77936 6.90804 8.06066 7.18934C8.34196 7.47064 8.5 7.85218 8.5 8.25ZM19.75 2.25V15.75C19.75 16.1478 19.592 16.5294 19.3107 16.8107C19.0294 17.092 18.6478 17.25 18.25 17.25H1.75C1.35218 17.25 0.970644 17.092 0.68934 16.8107C0.408035 16.5294 0.25 16.1478 0.25 15.75V2.25C0.25 1.85218 0.408035 1.47064 0.68934 1.18934C0.970644 0.908035 1.35218 0.75 1.75 0.75H18.25C18.6478 0.75 19.0294 0.908035 19.3107 1.18934C19.592 1.47064 19.75 1.85218 19.75 2.25ZM10.7266 12.5625C10.4839 11.6563 9.9084 10.875 9.115 10.3744C9.53634 9.95553 9.82384 9.42102 9.94104 8.83859C10.0582 8.25616 9.99988 7.65205 9.77334 7.10283C9.54681 6.55361 9.1623 6.08402 8.66857 5.75358C8.17483 5.42315 7.5941 5.24675 7 5.24675C6.4059 5.24675 5.82517 5.42315 5.33143 5.75358C4.8377 6.08402 4.45319 6.55361 4.22666 7.10283C4.00012 7.65205 3.94175 8.25616 4.05896 8.83859C4.17616 9.42102 4.46366 9.95553 4.885 10.3744C4.09211 10.8755 3.51681 11.6566 3.27344 12.5625C3.22371 12.7552 3.25257 12.9598 3.35366 13.1312C3.45475 13.3026 3.6198 13.4268 3.8125 13.4766C4.0052 13.5263 4.20975 13.4974 4.38118 13.3963C4.55259 13.2952 4.67683 13.1302 4.72656 12.9375C4.97406 11.9766 5.95094 11.25 7 11.25C8.04906 11.25 9.02687 11.9747 9.27344 12.9375C9.32317 13.1302 9.4474 13.2952 9.61882 13.3963C9.79025 13.4974 9.9948 13.5263 10.1875 13.4766C10.3802 13.4268 10.5452 13.3026 10.6463 13.1312C10.7474 12.9598 10.7763 12.7552 10.7266 12.5625ZM16.75 10.5C16.75 10.3011 16.671 10.1103 16.5303 9.96967C16.3897 9.82902 16.1989 9.75 16 9.75H12.25C12.0511 9.75 11.8603 9.82902 11.7197 9.96967C11.579 10.1103 11.5 10.3011 11.5 10.5C11.5 10.6989 11.579 10.8897 11.7197 11.0303C11.8603 11.171 12.0511 11.25 12.25 11.25H16C16.1989 11.25 16.3897 11.171 16.5303 11.0303C16.671 10.8897 16.75 10.6989 16.75 10.5ZM16.75 7.5C16.75 7.30109 16.671 7.11032 16.5303 6.96967C16.3897 6.82902 16.1989 6.75 16 6.75H12.25C12.0511 6.75 11.8603 6.82902 11.7197 6.96967C11.579 7.11032 11.5 7.30109 11.5 7.5C11.5 7.69891 11.579 7.88968 11.7197 8.03033C11.8603 8.17098 12.0511 8.25 12.25 8.25H16C16.1989 8.25 16.3897 8.17098 16.5303 8.03033C16.671 7.88968 16.75 7.69891 16.75 7.5Z" />
  </svg>
);
