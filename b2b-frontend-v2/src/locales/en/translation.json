{"paymentSuccess": {"title": "Payment Successful!", "description": "Thank you for subscribing! Your account is now active. Please proceed with the onboarding to get started.", "button": "Start Onboarding"}, "userManagement": {"users": "Users", "import": "Import", "add": "Add", "search": "Search", "fullName": "FULL NAME", "email": "EMAIL", "phoneNumber": "PHONE NUMBER", "country": "COUNTRY", "employmentId": "EMPLOYMENT ID", "workspace": "WORKSPACE", "seat": "SEAT", "role": "ROLE", "status": "STATUS"}, "userImportModal": {"title": "Import Users", "description": "Upload a CSV file containing user data", "dropHere": "Drop CSV file here", "dragAndDrop": "Drag and drop your CSV file here, or", "browse": "browse", "csvOnly": "Only CSV files are supported", "csvRequirements": "CSV format requirements:", "csvFormat": "File must be in CSV format", "headerMatch": "Headers must match: First Name, Last Name, etc.", "requiredFields": "All required fields must be included", "cancel": "Cancel", "importing": "Importing", "importUsers": "Import Users", "downloadTemplate": "Download CSV Template", "downloadTemplateDescription": "Use this template as a starting point for your user import.", "downloadCSV": "Download CSV"}, "addusermodal": {"title": "Add New User", "firstName": "First Name", "firstNamePlaceholder": "Enter first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter last name", "country": "Country", "countryPlaceholder": "Select country", "passportNo": "Passport Number", "passportNoPlaceholder": "Enter passport number", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number", "employmentId": "Employment ID", "employmentIdPlaceholder": "Enter employment ID", "workspacePlaceholder": "Select workspace", "seatPlaceholder": "Select seat", "userInformation": "User Information", "assignTitle": "Assign workspace, seat & role", "assignDescription": "Configure assignments to streamline their access and responsibilities.", "rolePlaceholder": "Select role", "addAssignment": "Add Assignment", "cancel": "Cancel", "add": "Add User", "adding": "Adding...", "validation": {"firstName": {"required": "First name is required"}, "lastName": {"required": "Last name is required"}, "country": {"required": "Country is required"}, "passport": {"required": "Passport number is required"}, "phone": {"required": "Phone number is required", "invalid": "Invalid phone number format"}, "employmentId": {"required": "Employment ID is required"}, "workspace": {"required": "Workspace is required"}, "seat": {"required": "Seat is required"}, "role": {"required": "Role is required"}, "assignment": {"required": "At least one assignment is required"}}}, "subscription": {"title": "Subscribe to Our Annual Plan", "subtitle": "Pricing is $1,000 per ship annually. Includes one office workspace in your subscription. Additional office workspaces can be added for $10,000 each. Enter your ship count to calculate your total subscription fee.", "includedFeatures": {"title": "Included Features", "ships": "Ships", "workspace": "1 Office Workspaces", "library": "Unlimited Library", "members": "Unlimited Team Members"}, "fields": {"numberOfShips": {"label": "Number of Ships", "placeholder": "Enter no of ships"}, "numberOfWorkspaces": {"label": "Number of Office Workspace", "placeholder": "Enter number of workspaces"}}, "pricing": {"title": "Pricing", "perYear": "/year"}, "subscribe": "Subscribe Now", "subscribing": "Subscribing", "startingTrial": "Starting Trial", "trial": "Start 14-day Free Trial", "or": "or", "validation": {"numberOfShips": {"required": "Number of ships is required", "numeric": "Please enter a valid number"}, "numberOfWorkspaces": {"required": "Number of workspaces is required", "numeric": "Please enter a valid number"}}, "error": "Something went wrong. Please try again."}, "getStarted": {"form": {"title": "Let's Get Started!", "subtitle": "Fill out the form to help us understand your company and its needs. Once submitted, our team will verify your details and guide you through the next steps to access the platform.", "fields": {"fullName": {"label": "Full name", "placeholder": "Enter full name"}, "workEmail": {"label": "Work Email", "placeholder": "Enter work email"}, "phoneNumber": {"label": "Phone Number", "placeholder": "Enter phone number"}, "position": {"label": "Position", "placeholder": "Enter your position"}, "companyName": {"label": "Company Name", "placeholder": "Enter company name"}, "numberOfShips": {"label": "Number of Ships", "placeholder": "Enter no of ships managed", "options": {"1-100": "1-100 ships", "100+": "100+ ships"}}}, "submit": {"button": "Submit", "loading": "Submitting..."}, "validation": {"fullName": {"required": "Full name is required", "min": "Full name must be at least 2 characters"}, "workEmail": {"required": "Work email is required", "invalid": "Please enter a valid email address"}, "phoneNumber": {"required": "Phone number is required", "invalid": "Please enter a valid phone number"}, "position": {"required": "Position is required", "min": "Position must be at least 2 characters"}, "companyName": {"required": "Company name is required", "min": "Company name must be at least 2 characters"}, "numberOfShips": {"required": "Please select number of ships"}}}, "success": {"title": "Thank You for Getting Started!", "message": "We've received your details and will review them shortly. Our team will reach out to you with the next steps. Stay tuned!", "action": "View website"}}, "demoRequest": {"form": {"title": "Experience the Future of Maritime Management", "subtitle": "Schedule your personalized demo and see how our platform can streamline your operations.", "fields": {"fullName": {"label": "Full name", "placeholder": "Enter full name"}, "workEmail": {"label": "Work Email", "placeholder": "Enter work email"}, "phoneNumber": {"label": "Phone Number", "placeholder": "Enter phone number"}, "position": {"label": "Position", "placeholder": "Enter your position"}, "companyName": {"label": "Company Name", "placeholder": "Enter company name"}, "numberOfShips": {"label": "Number of Ships", "placeholder": "Select no of ships managed", "options": {"1-100": "1-100 ships", "100+": "100+ ships"}}}, "submit": {"button": "Submit", "loading": "Submitting..."}, "validation": {"fullName": {"required": "Full name is required", "min": "Full name must be at least 2 characters"}, "workEmail": {"required": "Work email is required", "invalid": "Please enter a valid email address"}, "phoneNumber": {"required": "Phone number is required", "invalid": "Please enter a valid phone number"}, "position": {"required": "Position is required", "min": "Position must be at least 2 characters"}, "companyName": {"required": "Company name is required", "min": "Company name must be at least 2 characters"}, "numberOfShips": {"required": "Please select number of ships"}}}, "success": {"title": "Thank you for your interest!", "message": "Our team will contact you shortly to schedule your demo.", "action": "View website"}}, "auth": {"verification": {"title": "Email Verification", "code": {"label": "Verification Code", "placeholder": "Enter verification code"}, "verifyBtn": "Verify email", "verifyingBtn": "Verifying...", "success": "Email verified successfully.", "validation": {"code": {"required": "Verification code is required", "length": "Verification code must be 6 digits", "numeric": "Verification code must contain only numbers"}}, "error": "Failed to verify email. Please try again."}, "login": {"title": "<PERSON><PERSON>", "email": {"label": "Email <PERSON>d", "placeholder": "Enter email id"}, "password": {"label": "Password", "placeholder": "Enter password"}, "rememberMe": "Remember Me", "forgotPassword": "Forgot password?", "loginBtn": "<PERSON><PERSON>", "altLoginText": "or login with", "googleBtn": "Google", "microsoftBtn": "Microsoft", "privacyText": "By proceeding, you agree to the <userAgreement>User Agreement</userAgreement>, <privacyPolicy>Privacy Policy</privacyPolicy>, and <cookiePolicy>Cookie Policy</cookiePolicy>.", "notHaveAccount": "Do not have a account?", "signUpLink": "Signup", "validation": {"email": {"required": "Email is required", "invalid": "Please enter a valid email address"}, "password": {"required": "Password is required", "min": "Password must be at least {min} characters long"}}, "error": "<PERSON><PERSON> failed. Please try again."}, "signup": {"title": "Signup", "email": {"label": "Email <PERSON>d", "placeholder": "Enter email id"}, "password": {"label": "Password", "placeholder": "Enter password"}, "rememberMe": "Remember Me", "signupBtn": "Signup", "altSignupText": "or signup with", "googleBtn": "Google", "microsoftBtn": "Microsoft", "privacyText": "By proceeding, you agree to the <userAgreement>User Agreement</userAgreement>, <privacyPolicy>Privacy Policy</privacyPolicy>, and <cookiePolicy>Cookie Policy</cookiePolicy>.", "alreadyHaveAccount": "Already have an account?", "loginLink": "<PERSON><PERSON>", "validation": {"email": {"required": "Email is required", "invalid": "Please enter a valid email address"}, "password": {"required": "Password is required", "min": "Password must be at least {min} characters long", "requirements": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"}}, "error": "Signup failed. Please try again."}, "forgotPwd": {"title": "Forgot Password", "email": {"label": "Email <PERSON>d", "placeholder": "Enter email id"}, "resetSent": "Reset email has been sent successfully.", "description": "Please provide the email address linked to your account, and we will send you the link to reset your password.", "sendingBtn": "Sending...", "sendBtn": "Send Email", "validation": {"email": {"required": "Email is required", "invalid": "Please enter a valid email address"}}, "error": "Failed to send reset email. Please try again."}, "resetPwd": {"title": "Reset password", "newPwd": {"label": "New Password", "placeholder": "Enter new password"}, "confirmPwd": {"label": "Confirm Password", "placeholder": "Confirm new password"}, "resetBtn": "Reset Password", "resettingBtn": "Resetting...", "success": "Your password has been successfully reset.", "validation": {"newPwd": {"required": "New password is required", "min": "New password must be at least {min} characters long", "requirements": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"}, "confirmPwd": {"required": "Confirm password is required", "match": "Passwords must match"}}, "error": "Failed to reset password. Please try again."}}, "onboarding": {"progress": "Step {current} of {total}", "backBtn": "Back", "nextBtn": "Next", "finishBtn": "Finish", "loading": "Loading...", "error": "Failed to onboard user. Please try again.", "profile": {"title": "Let's Start with Your Profile", "subtitle": "Tell us a bit about yourself to personalize your experience.", "firstName": {"label": "First Name", "placeholder": "Enter first name"}, "lastName": {"label": "Last Name", "placeholder": "Enter last name"}, "country": {"label": "Country", "placeholder": "Select country"}, "passport": {"label": "Passport No", "placeholder": "Enter passport no"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number"}, "employmentId": {"label": "Employment ID", "placeholder": "Enter employment id"}, "validation": {"firstName": {"required": "First name is required", "min": "First name must be at least {min} characters", "max": "First name must not exceed {max} characters"}, "lastName": {"required": "Last name is required", "min": "Last name must be at least {min} characters", "max": "Last name must not exceed {max} characters"}, "country": {"required": "Country is required"}, "passport": {"required": "Passport number is required", "invalid": "Please enter a valid passport number"}, "phone": {"required": "Phone number is required", "invalid": "Please enter a valid phone number"}, "employmentId": {"required": "Employment ID is required", "invalid": "Please enter a valid employment ID"}}}, "org": {"title": "Set Up Your Organization", "subtitle": "Provide a few details to establish your organization's structure.", "name": {"label": "Organization Name", "placeholder": "Enter organization name"}, "email": {"label": "Contact Email", "placeholder": "Enter email"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number"}, "address": {"label": "Address", "placeholder": "Add address"}, "validation": {"name": {"required": "Organization name is required", "min": "Organization name must be at least {min} characters", "max": "Organization name must not exceed {max} characters"}, "email": {"required": "Contact email is required", "invalid": "Please enter a valid email address"}, "phone": {"required": "Phone number is required", "invalid": "Please enter a valid phone number"}, "address": {"required": "Address is required", "min": "Address must be at least {min} characters", "max": "Address must not exceed {max} characters"}}}, "workspace": {"title": "Add Your First Office Workspace", "subtitle": "Create a workspace for your office to organize teams and operations.", "name": {"label": "Workspace Name", "placeholder": "Enter workspace name"}, "address": {"label": "Address", "placeholder": "Add address"}, "description": {"label": "Description", "placeholder": "Add description"}, "validation": {"name": {"required": "Workspace name is required", "min": "Workspace name must be at least {min} characters", "max": "Workspace name must not exceed {max} characters"}, "address": {"required": "Address is required", "min": "Address must be at least {min} characters", "max": "Address must not exceed {max} characters"}, "description": {"max": "Description must not exceed {max} characters"}}}, "adminSeat": {"title": "Create Your Admin Seat", "subtitle": "Set up your first seat to manage your organization. This seat will give you control over the platform.", "seatId": {"label": "Seat ID"}, "department": {"label": "Department", "placeholder": "Select department"}, "position": {"label": "Position", "placeholder": "Select position"}, "user": {"label": "User"}, "description": {"label": "Description", "placeholder": "Add description"}, "validation": {"department": {"required": "Department is required"}, "position": {"required": "Position is required"}, "description": {"max": "Description must not exceed {max} characters"}}}}, "globalSidebar": {"navigation": {"navicater_ai": {"label": "Navicater AI"}, "library": {"label": "Library"}, "admin_panel": {"label": "Admin Panel"}, "settings": {"label": "Settings"}}}, "adminSidebar": {"title": "ADMIN PANEL", "navigation": {"org_setup": {"label": "Organizational Setup"}, "users": {"label": "Users"}, "workspaces": {"label": "Workspace"}, "roles": {"label": "Roles & Permissions"}}}, "chatSidebar": {"title": "AI Assistant"}, "librarySidebar": {"title": "Knowledge Library"}, "settingSidebar": {"title": "Settings"}}