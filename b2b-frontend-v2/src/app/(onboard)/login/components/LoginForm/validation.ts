import { TFunctionI } from "@/types/common/data";
import { z } from "zod";

export const createLoginSchema = (t: TFunctionI) =>
  z.object({
    email: z
      .string()
      .trim()
      .min(1, { message: t("auth.login.validation.email.required") })
      .email({ message: t("auth.login.validation.email.invalid") }),
    password: z
      .string()
      .min(1, { message: t("auth.login.validation.password.required") })
      .min(8, { message: t("auth.login.validation.password.min", { min: 8 }) }),
  });

export type LoginFormData = z.infer<ReturnType<typeof createLoginSchema>>;
