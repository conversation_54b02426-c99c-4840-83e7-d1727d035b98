"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { LoginFormData, createLoginSchema } from "./validation";

const useLoginForm = () => {
  const t = useTranslations();
  const [rememberMe, setRememberMe] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const schema = createLoginSchema(t);

  const methods = useForm<LoginFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const { setError, reset } = methods;

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log({ ...data, rememberMe });
      resetLoginForm();
    } catch (error) {
      console.log(error);
      setError("root", { message: t("auth.login.error") });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleRememberMe = () => {
    setRememberMe((prev) => !prev);
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.log(error);
      setError("root", { message: t("auth.login.error") });
    } finally {
      setIsLoading(false);
    }
  };

  const handleMicrosoftLogin = async () => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.log(error);
      setError("root", { message: t("auth.login.error") });
    } finally {
      setIsLoading(false);
    }
  };

  const resetLoginForm = () => {
    reset();
    setRememberMe(false);
    setIsLoading(false);
  };

  return {
    methods,
    isLoading,
    rememberMe,
    toggleRememberMe,
    handleGoogleLogin,
    handleMicrosoftLogin,
    onSubmit,
  };
};

export default useLoginForm;
