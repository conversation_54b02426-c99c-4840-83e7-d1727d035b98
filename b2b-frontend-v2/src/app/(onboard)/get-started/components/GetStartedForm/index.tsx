"use client";

import { FormProvider } from "react-hook-form";
import { useTranslations } from "next-intl";
import useGetStartedForm from "./useHook";
import React from "react";
import Select from "@/components/Select";
import TextInput from "@/components/TextInput";
import Button from "@/components/Button";
import FormHeading from "@/components/FormHeading";

const GetStartedForm: React.FC = () => {
  const t = useTranslations();
  const {
    methods,
    isLoading,
    isSuccess,
    onSubmit,
    updateNumberOfShips,
    selectedNumberOfShips,
  } = useGetStartedForm();
  const {
    formState: { errors },
  } = methods;

  if (isSuccess) {
    return (
      <div className="flex flex-col items-center w-full max-w-[750px] mx-auto p-6 text-center">
        <FormHeading
          title={t("getStarted.success.title")}
          subTitle={t("getStarted.success.message")}
        />
        <Button
          variant="primary"
          className="w-fit mt-4"
          onClick={() => (window.location.href = "/")}
        >
          {t("getStarted.success.action")}
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center w-full max-w-[700px] mx-auto p-6">
      <FormHeading
        title={t("getStarted.form.title")}
        subTitle={t("getStarted.form.subtitle")}
      />
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-5 rounded-2xl w-full h-full bg-white">
            <div className="col-span-1">
              <TextInput
                label={t("getStarted.form.fields.fullName.label")}
                placeholder={t("getStarted.form.fields.fullName.placeholder")}
                name="fullName"
                errorText={errors.fullName?.message}
              />
            </div>
            <div className="col-span-1">
              <TextInput
                label={t("getStarted.form.fields.workEmail.label")}
                placeholder={t("getStarted.form.fields.workEmail.placeholder")}
                name="workEmail"
                type="email"
                errorText={errors.workEmail?.message}
              />
            </div>
            <div className="col-span-1">
              <TextInput
                label={t("getStarted.form.fields.phoneNumber.label")}
                placeholder={t(
                  "getStarted.form.fields.phoneNumber.placeholder",
                )}
                name="phoneNumber"
                type="text"
                errorText={errors.phoneNumber?.message}
              />
            </div>
            <div className="col-span-1">
              <TextInput
                label={t("getStarted.form.fields.position.label")}
                placeholder={t("getStarted.form.fields.position.placeholder")}
                name="position"
                errorText={errors.position?.message}
              />
            </div>
            <div className="col-span-1">
              <TextInput
                label={t("getStarted.form.fields.companyName.label")}
                placeholder={t(
                  "getStarted.form.fields.companyName.placeholder",
                )}
                name="companyName"
                errorText={errors.companyName?.message}
              />
            </div>
            <div className="col-span-1">
              <Select
                label={t("getStarted.form.fields.numberOfShips.label")}
                name="numberOfShips"
                options={Object.entries(
                  t.raw("getStarted.form.fields.numberOfShips.options"),
                ).map(([id, title]) => ({
                  id,
                  title: title as string,
                }))}
                handleChange={updateNumberOfShips}
                selected={selectedNumberOfShips}
                placeholder={t(
                  "getStarted.form.fields.numberOfShips.placeholder",
                )}
                errorText={errors.numberOfShips?.message}
              />
            </div>
          </div>
          <Button
            variant="primary"
            isLoading={isLoading}
            type="submit"
            spinnerProps={{
              colorClass: "text-white",
              size: 20,
              text: t("getStarted.form.submit.loading"),
              textColorClass: "text-white",
            }}
          >
            {t("getStarted.form.submit.button")}
          </Button>
        </form>
      </FormProvider>
    </div>
  );
};

export default GetStartedForm;
