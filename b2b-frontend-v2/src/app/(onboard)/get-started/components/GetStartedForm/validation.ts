import { TFunctionI } from "@/types/common/data";
import { z } from "zod";

export const createGetStartedSchema = (t: TFunctionI) =>
  z.object({
    fullName: z
      .string()
      .min(1, { message: t("getStarted.form.validation.fullName.required") })
      .min(2, { message: t("getStarted.form.validation.fullName.min") }),
    workEmail: z
      .string()
      .min(1, { message: t("getStarted.form.validation.workEmail.required") })
      .email({ message: t("getStarted.form.validation.workEmail.invalid") }),
    phoneNumber: z
      .string()
      .min(1, { message: t("getStarted.form.validation.phoneNumber.required") })
      .min(10, {
        message: t("getStarted.form.validation.phoneNumber.invalid"),
      }),
    position: z
      .string()
      .min(1, { message: t("getStarted.form.validation.position.required") })
      .min(2, { message: t("getStarted.form.validation.position.min") }),
    companyName: z
      .string()
      .min(1, { message: t("getStarted.form.validation.companyName.required") })
      .min(2, { message: t("getStarted.form.validation.companyName.min") }),
    numberOfShips: z.string().min(1, {
      message: t("getStarted.form.validation.numberOfShips.required"),
    }),
  });

export type GetStartedFormData = z.infer<
  ReturnType<typeof createGetStartedSchema>
>;
