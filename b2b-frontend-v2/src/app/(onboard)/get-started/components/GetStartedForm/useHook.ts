"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { createGetStartedSchema, GetStartedFormData } from "./validation";
import { IdTitleI, StringNullI } from "@/types/common/data";

const useGetStartedForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [selectedNumberOfShips, setSelectedNumberOfShips] =
    useState<IdTitleI | null>(null);
  const t = useTranslations();

  const methods = useForm<GetStartedFormData>({
    resolver: zodResolver(createGetStartedSchema(t)),
    defaultValues: {
      fullName: "",
      workEmail: "",
      phoneNumber: "",
      position: "",
      companyName: "",
      numberOfShips: "",
    },
  });

  const { setError, reset, setValue } = methods;

  const updateNumberOfShips = (id: StringNullI) => {
    const shipOption = id
      ? {
          id,
          title: t.raw(
            `getStarted.form.fields.numberOfShips.options.${id}`,
          ) as string,
        }
      : null;
    setSelectedNumberOfShips(shipOption);
    setValue("numberOfShips", shipOption?.id ?? "", { shouldValidate: true });
  };

  const onSubmit = async (data: GetStartedFormData) => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Form submitted:", data);
      setIsSuccess(true);
      resetGetStartedForm();
    } catch (error) {
      console.log(error);
      setError("root", {
        message: t("form.messages.error"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetGetStartedForm = () => {
    reset();
    setSelectedNumberOfShips(null);
    setIsLoading(false);
    setIsSuccess(false);
  };

  return {
    methods,
    isLoading,
    isSuccess,
    onSubmit,
    selectedNumberOfShips,
    updateNumberOfShips,
  };
};

export default useGetStartedForm;
