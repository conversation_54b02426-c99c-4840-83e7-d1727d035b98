"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { VerificationFormData, createVerificationSchema } from "./validation";

const useVerification = () => {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);

  const schema = createVerificationSchema(t);

  const methods = useForm<VerificationFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      verificationCode: "",
    },
  });

  const { setError, reset } = methods;

  const onSubmit = async (data: VerificationFormData) => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Verification code:", data);
      resetVerificationForm();
    } catch (error) {
      console.log(error);
      setError("root", {
        message: t("auth.verification.error"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetVerificationForm = () => {
    reset();
    setIsSuccess(false);
    setIsLoading(false);
  };

  return {
    methods,
    isLoading,
    isSuccess,
    onSubmit,
  };
};

export default useVerification;
