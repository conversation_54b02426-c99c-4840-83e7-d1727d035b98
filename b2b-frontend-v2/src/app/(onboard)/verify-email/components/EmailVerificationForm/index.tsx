"use client";

import { FormProvider } from "react-hook-form";
import { useTranslations } from "next-intl";
import React from "react";
import TextInput from "@/components/TextInput";
import Button from "@/components/Button";
import FormAlert from "@/components/Alert";
import useVerification from "./useHook";
import FormHeading from "@/components/FormHeading";

const EmailVerificationForm: React.FC = () => {
  const t = useTranslations();
  const { methods, isLoading, isSuccess, onSubmit } = useVerification();
  const {
    formState: { errors },
  } = methods;

  return (
    <div className="flex flex-col items-center w-full max-w-[430px] mx-auto p-6">
      <FormHeading
        title={t("auth.verification.title")}
        subTitle=""
        useCompactLogo={false}
      />
      <FormProvider {...methods}>
        <form
          className="grid grid-cols-1 gap-6 w-full bg-white p-6 rounded-xl"
          onSubmit={methods.handleSubmit(onSubmit)}
        >
          <div className="col-span-1">
            <TextInput
              label={t("auth.verification.code.label")}
              placeholder={t("auth.verification.code.placeholder")}
              type="text"
              errorText={errors.verificationCode?.message}
              name="verificationCode"
            />
          </div>
          {errors.root && (
            <div className="col-span-1">
              <FormAlert
                variant="error"
                message={
                  (errors.root.message as string) ||
                  t("auth.verification.error")
                }
              />
            </div>
          )}
          {isSuccess && (
            <div className="col-span-1">
              <FormAlert
                variant="success"
                message={t("auth.verification.success")}
              />
            </div>
          )}
          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
            spinnerProps={{
              colorClass: "text-white",
              size: 20,
              text: t("auth.verification.verifyingBtn"),
              textColorClass: "text-white",
            }}
          >
            {t("auth.verification.verifyBtn")}
          </Button>
        </form>
      </FormProvider>
    </div>
  );
};

export default EmailVerificationForm;
