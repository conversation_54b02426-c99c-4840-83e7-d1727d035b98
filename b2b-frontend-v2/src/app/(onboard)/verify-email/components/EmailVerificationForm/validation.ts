import { TFunctionI } from "@/types/common/data";
import { z } from "zod";

export const createVerificationSchema = (t: TFunctionI) =>
  z.object({
    verificationCode: z
      .string()
      .min(1, {
        message: t("auth.verification.validation.code.required"),
      })
      .length(6, {
        message: t("auth.verification.validation.code.length"),
      })
      .regex(/^[0-9]+$/, {
        message: t("auth.verification.validation.code.numeric"),
      }),
  });

export type VerificationFormData = z.infer<
  ReturnType<typeof createVerificationSchema>
>;
