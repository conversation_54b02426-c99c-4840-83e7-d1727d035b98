"use client";

import { FormProvider } from "react-hook-form";
import { useTranslations } from "next-intl";
import React from "react";
import TextInput from "@/components/TextInput";
import Button from "@/components/Button";
import FormAlert from "@/components/Alert";
import useResetPassword from "./useHook";
import FormHeading from "@/components/FormHeading";

const ResetPasswordForm: React.FC = () => {
  const t = useTranslations();
  const { methods, isLoading, isSuccess, onSubmit } = useResetPassword();
  const {
    formState: { errors },
  } = methods;

  return (
    <div className="flex flex-col items-center w-full">
      <FormHeading
        title={t("auth.resetPwd.title")}
        subTitle=""
        useCompactLogo={false}
      />
      <FormProvider {...methods}>
        <form
          className="grid grid-cols-1 gap-4 max-w-[430px] p-5 rounded-2xl w-full h-full bg-white"
          onSubmit={methods.handleSubmit(onSubmit)}
        >
          <div className="col-span-1">
            <TextInput
              label={t("auth.resetPwd.newPwd.label")}
              placeholder={t("auth.resetPwd.newPwd.placeholder")}
              type="password"
              name="newPassword"
              errorText={errors.newPassword?.message}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("auth.resetPwd.confirmPwd.label")}
              placeholder={t("auth.resetPwd.confirmPwd.placeholder")}
              type="password"
              name="confirmPassword"
              errorText={errors.confirmPassword?.message}
            />
          </div>
          {errors.root && (
            <div className="col-span-1">
              <FormAlert
                variant="error"
                message={errors.root.message as string}
              />
            </div>
          )}
          {isSuccess && (
            <div className="col-span-1">
              <FormAlert
                variant="success"
                message={t("auth.resetPwd.success")}
              />
            </div>
          )}
          <div className="col-span-1">
            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading}
              spinnerProps={{
                colorClass: "text-white",
                size: 20,
                text: t("auth.resetPwd.resettingBtn"),
                textColorClass: "text-white",
              }}
            >
              {t("auth.resetPwd.resetBtn")}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default ResetPasswordForm;
