"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { ResetPasswordFormData, createResetPasswordSchema } from "./validation";

const useResetPassword = () => {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);

  const schema = createResetPasswordSchema(t);

  const methods = useForm<ResetPasswordFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  });

  const { setError, reset } = methods;

  const onSubmit = async (data: ResetPasswordFormData) => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Password reset data:", data);
      setIsSuccess(true);
      resetPasswordForm();
    } catch (error) {
      console.log(error);
      setError("root", {
        message: t("auth.resetPwd.error"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetPasswordForm = () => {
    reset();
    setIsLoading(false);
    setIsSuccess(false);
  };

  return {
    methods,
    isLoading,
    isSuccess,
    onSubmit,
  };
};

export default useResetPassword;
