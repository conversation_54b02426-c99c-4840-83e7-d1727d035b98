import { TFunctionI } from "@/types/common/data";
import { z } from "zod";

export const createResetPasswordSchema = (t: TFunctionI) =>
  z
    .object({
      newPassword: z
        .string()
        .min(1, {
          message: t("auth.resetPwd.validation.newPwd.required"),
        })
        .min(8, {
          message: t("auth.resetPwd.validation.newPwd.min", {
            min: 8,
          }),
        })
        .regex(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
          {
            message: t("auth.resetPwd.validation.newPwd.requirements"),
          },
        ),
      confirmPassword: z.string().min(1, {
        message: t("auth.resetPwd.validation.confirmPwd.required"),
      }),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: t("auth.resetPwd.validation.confirmPwd.match"),
      path: ["confirmPassword"],
    });

export type ResetPasswordFormData = z.infer<
  ReturnType<typeof createResetPasswordSchema>
>;
