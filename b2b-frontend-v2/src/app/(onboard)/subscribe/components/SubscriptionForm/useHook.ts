"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { createSubscriptionSchema, SubscriptionFormData } from "./validation";

const BASE_PRICE = 70000;
const SHIP_PRICE = 1000;
const WORKSPACE_PRICE = 5000;
const COUPON_DISCOUNT = 0.1;

const useSubscriptionForm = () => {
  const [isTrialLoading, setIsTrialLoading] = useState(false);
  const [isSubscriptionLoading, setIsSubscriptionLoading] = useState(false);

  const [appliedCoupon, setAppliedCoupon] = useState<string | null>(null);
  const t = useTranslations();

  const methods = useForm<SubscriptionFormData>({
    resolver: zodResolver(createSubscriptionSchema(t)),
    defaultValues: {
      numberOfShips: "60",
      numberOfWorkspaces: "2",
      couponCode: "",
    },
  });

  const { watch, setError, setValue, reset } = methods;

  const numberOfShips = parseInt(watch("numberOfShips") || "0", 10);
  const numberOfWorkspaces = parseInt(watch("numberOfWorkspaces") || "1", 10);

  const calculateOriginalPrice = () => {
    const shipsCost = numberOfShips * SHIP_PRICE;
    const workspacesCost = (numberOfWorkspaces - 1) * WORKSPACE_PRICE;
    return BASE_PRICE + shipsCost + workspacesCost;
  };

  const calculatePrice = () => {
    const originalPrice = calculateOriginalPrice();
    return appliedCoupon
      ? originalPrice * (1 - COUPON_DISCOUNT)
      : originalPrice;
  };

  const handleApplyCoupon = (code: string) => {
    setAppliedCoupon(code);
    setValue("couponCode", code);
  };

  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
    setValue("couponCode", "");
  };

  const onSubscribe = async (data: SubscriptionFormData) => {
    setIsSubscriptionLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Subscription data:", { ...data, appliedCoupon });
      resetSubscriptionForm();
    } catch (error) {
      console.error(error);
      setError("root", {
        message: t("error"),
      });
    } finally {
      setIsSubscriptionLoading(false);
    }
  };

  const onStartTrial = async () => {
    setIsTrialLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Starting trial with data:", {
        numberOfShips: numberOfShips.toString(),
        numberOfWorkspaces: numberOfWorkspaces.toString(),
        appliedCoupon,
      });
    } catch (error) {
      console.error(error);
      setError("root", {
        message: t("subscription.error"),
      });
    } finally {
      setIsTrialLoading(false);
    }
  };

  const resetSubscriptionForm = () => {
    reset();
    setAppliedCoupon(null);
    setIsSubscriptionLoading(false);
    setIsTrialLoading(false);
  };

  return {
    methods,
    isTrialLoading,
    isSubscriptionLoading,
    originalPrice: calculateOriginalPrice(),
    totalPrice: calculatePrice(),
    onSubscribe,
    onStartTrial,
    handleApplyCoupon,
    handleRemoveCoupon,
    appliedCoupon,
  };
};

export default useSubscriptionForm;
