"use client";

import { FormProvider } from "react-hook-form";
import { useTranslations } from "next-intl";
import TextInput from "@/components/TextInput";
import Button from "@/components/Button";
import useSubscriptionForm from "./useHook";
import FormHeading from "@/components/FormHeading";
import Coupon from "@/components/Coupon";

const SubscriptionForm = () => {
  const t = useTranslations();
  const {
    methods,
    isTrialLoading,
    isSubscriptionLoading,
    totalPrice,
    onSubscribe,
    onStartTrial,
    handleApplyCoupon,
    handleRemoveCoupon,
    appliedCoupon,
    originalPrice,
  } = useSubscriptionForm();

  const {
    formState: { errors },
  } = methods;

  const includedFeatures = [
    t("subscription.includedFeatures.ships"),
    t("subscription.includedFeatures.workspace"),
    t("subscription.includedFeatures.library"),
    t("subscription.includedFeatures.members"),
  ];

  return (
    <div className="flex flex-col items-center w-full max-w-[750px] mx-auto p-6">
      <FormHeading
        title={t("subscription.title")}
        useCompactLogo={false}
        subTitle={t("subscription.subtitle")}
      />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 bg-white p-5 rounded-xl shadow-sm w-full">
        <div className="space-y-2">
          <div className="text-xl font-medium text-neutral-950">
            {t("subscription.includedFeatures.title")}
          </div>
          <div className="space-y-2">
            {includedFeatures.map((feature) => (
              <div key={feature} className="flex items-center gap-2">
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 0.874023C7.39303 0.874023 5.82214 1.35055 4.486 2.24333C3.14985 3.13612 2.10844 4.40507 1.49348 5.88972C0.87852 7.37437 0.717618 9.00804 1.03112 10.5841C1.34463 12.1602 2.11846 13.608 3.25476 14.7443C4.39106 15.8806 5.8388 16.6544 7.4149 16.9679C8.99099 17.2814 10.6247 17.1205 12.1093 16.5055C13.594 15.8906 14.8629 14.8492 15.7557 13.513C16.6485 12.1769 17.125 10.606 17.125 8.99902C17.1227 6.84484 16.266 4.77953 14.7427 3.25629C13.2195 1.73305 11.1542 0.876298 9 0.874023ZM12.5672 7.56621L8.19219 11.9412C8.13415 11.9993 8.06522 12.0454 7.98934 12.0769C7.91347 12.1083 7.83214 12.1245 7.75 12.1245C7.66787 12.1245 7.58654 12.1083 7.51067 12.0769C7.43479 12.0454 7.36586 11.9993 7.30782 11.9412L5.43282 10.0662C5.31554 9.94894 5.24966 9.78988 5.24966 9.62402C5.24966 9.45817 5.31554 9.29911 5.43282 9.18184C5.55009 9.06456 5.70915 8.99868 5.875 8.99868C6.04086 8.99868 6.19992 9.06456 6.31719 9.18184L7.75 10.6154L11.6828 6.68184C11.7409 6.62377 11.8098 6.5777 11.8857 6.54628C11.9616 6.51485 12.0429 6.49868 12.125 6.49868C12.2071 6.49868 12.2884 6.51485 12.3643 6.54628C12.4402 6.5777 12.5091 6.62377 12.5672 6.68184C12.6253 6.7399 12.6713 6.80884 12.7027 6.88471C12.7342 6.96058 12.7504 7.0419 12.7504 7.12402C12.7504 7.20615 12.7342 7.28746 12.7027 7.36333C12.6713 7.4392 12.6253 7.50814 12.5672 7.56621Z"
                    fill="#448600"
                  />
                </svg>
                <span className="text-neutral-700 text-sm">{feature}</span>
              </div>
            ))}
          </div>
        </div>
        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubscribe)}
            className="space-y-5"
          >
            <div className="bg-neutral-50 rounded-lg p-6 space-y-3">
              <h2 className="text-xl font-semibold text-neutral-950">
                Pricing
              </h2>
              <div className="flex flex-col gap-2">
                {appliedCoupon && (
                  <span className="text-neutral-400 text-base line-through">
                    ${originalPrice.toLocaleString()}
                  </span>
                )}
                <div className="flex items-baseline gap-2">
                  <span className="text-3xl leading-none font-medium text-neutral-950">
                    ${totalPrice.toLocaleString()}
                  </span>
                  <span className="text-lg text-neutral-600">/year</span>
                </div>
              </div>
            </div>
            <div className="space-y-5">
              <TextInput
                label={t("subscription.fields.numberOfShips.label")}
                name="numberOfShips"
                type="text"
                placeholder={t("subscription.fields.numberOfShips.placeholder")}
                errorText={errors.numberOfShips?.message}
              />
              <TextInput
                label={t("subscription.fields.numberOfWorkspaces.label")}
                name="numberOfWorkspaces"
                type="text"
                placeholder={t(
                  "subscription.fields.numberOfWorkspaces.placeholder",
                )}
                errorText={errors.numberOfWorkspaces?.message}
              />
            </div>
            <Coupon
              code="WELCOME10"
              description="Get a 10% discount on your first subscription payment."
              isApplied={appliedCoupon === "WELCOME10"}
              onApply={handleApplyCoupon}
              onRemove={handleRemoveCoupon}
            />
            <Button
              variant="primary"
              type="submit"
              isLoading={isSubscriptionLoading}
              spinnerProps={{
                colorClass: "text-white",
                size: 20,
                text: t("subscription.subscribing"),
                textColorClass: "text-white",
              }}
            >
              {t("subscription.subscribe")}
            </Button>
            <div className="relative">
              <div
                className="absolute inset-0 flex items-center"
                aria-hidden="true"
              >
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center">
                <span className="bg-white px-2 text-sm text-gray-500">
                  {t("subscription.or")}
                </span>
              </div>
            </div>
            <Button
              variant="ghost"
              type="button"
              onClick={onStartTrial}
              isLoading={isTrialLoading}
              spinnerProps={{
                colorClass: "text-primary-600",
                size: 20,
                text: t("subscription.startingTrial"),
                textColorClass: "text-primary-600",
              }}
            >
              {t("subscription.trial")}
            </Button>
          </form>
        </FormProvider>
      </div>
    </div>
  );
};

export default SubscriptionForm;
