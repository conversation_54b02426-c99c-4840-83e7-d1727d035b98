import { TFunctionI } from "@/types/common/data";
import { z } from "zod";

export const createSubscriptionSchema = (t: TFunctionI) =>
  z.object({
    numberOfShips: z
      .string()
      .min(1, { message: t("subscription.validation.numberOfShips.required") })
      .regex(/^\d+$/, {
        message: t("subscription.validation.numberOfShips.numeric"),
      }),
    numberOfWorkspaces: z
      .string()
      .min(1, {
        message: t("subscription.validation.numberOfWorkspaces.required"),
      })
      .regex(/^\d+$/, {
        message: t("subscription.validation.numberOfWorkspaces.numeric"),
      }),
    couponCode: z.string().optional(),
  });

export type SubscriptionFormData = z.infer<
  ReturnType<typeof createSubscriptionSchema>
>;
