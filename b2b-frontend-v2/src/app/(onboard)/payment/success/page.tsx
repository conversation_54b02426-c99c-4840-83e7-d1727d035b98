"use client";

import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import Button from "@/components/Button";
import FormHeading from "@/components/FormHeading";

const PaymentSuccess = () => {
  const router = useRouter();
  const t = useTranslations();

  return (
    <div className="min-h-screen flex items-center justify-center p-6">
      <div className="max-w-[600px] flex flex-col items-center text-center">
        <FormHeading
          title={t("paymentSuccess.title")}
          subTitle={t("paymentSuccess.description")}
        />
        <Button
          variant="primary"
          onClick={() => router.push("/onboarding")}
          className="w-fit"
        >
          {t("paymentSuccess.button")}
        </Button>
      </div>
    </div>
  );
};

export default PaymentSuccess;
