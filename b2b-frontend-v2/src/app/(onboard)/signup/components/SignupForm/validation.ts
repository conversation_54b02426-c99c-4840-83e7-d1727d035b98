import { TFunctionI } from "@/types/common/data";
import { z } from "zod";

export const createSignupSchema = (t: TFunctionI) =>
  z.object({
    email: z
      .string()
      .trim()
      .min(1, { message: t("auth.signup.validation.email.required") })
      .email({ message: t("auth.signup.validation.email.invalid") }),
    password: z
      .string()
      .min(1, { message: t("auth.signup.validation.password.required") })
      .min(8, {
        message: t("auth.signup.validation.password.min", { min: 8 }),
      })
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        {
          message: t("auth.signup.validation.password.requirements"),
        },
      ),
  });

export type SignupFormData = z.infer<ReturnType<typeof createSignupSchema>>;
