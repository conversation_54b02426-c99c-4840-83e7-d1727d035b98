"use client";

import { FormProvider } from "react-hook-form";
import { useTranslations } from "next-intl";
import React from "react";
import TextInput from "@/components/TextInput";
import Button from "@/components/Button";
import FormAlert from "@/components/Alert";
import useSignupForm from "./useHook";
import FormHeading from "@/components/FormHeading";
import Checkbox from "@/components/Checkbox";
import Link from "next/link";

const SignupForm: React.FC = () => {
  const {
    methods,
    isLoading,
    rememberMe,
    toggleRememberMe,
    handleGoogleSignup,
    handleMicrosoftSignup,
    onSubmit,
  } = useSignupForm();

  const {
    formState: { errors },
  } = methods;

  const t = useTranslations();

  return (
    <div className="flex flex-col items-center w-full max-w-[480px] mx-auto p-6">
      <FormHeading
        title={t("auth.signup.title")}
        subTitle=""
        useCompactLogo={false}
      />
      <FormProvider {...methods}>
        <form
          className="grid grid-cols-1 gap-6 w-full bg-white p-6 rounded-xl"
          onSubmit={methods.handleSubmit(onSubmit)}
        >
          <div className="space-y-4">
            <div>
              <TextInput
                label={t("auth.signup.email.label")}
                placeholder={t("auth.signup.email.placeholder")}
                name="email"
                type="email"
                errorText={errors.email?.message}
              />
            </div>
            <div>
              <TextInput
                label={t("auth.signup.password.label")}
                placeholder={t("auth.signup.password.placeholder")}
                name="password"
                type="password"
                errorText={errors.password?.message}
              />
            </div>
          </div>
          <Checkbox
            id="rememberMe"
            name="rememberMe"
            checked={rememberMe}
            onChange={toggleRememberMe}
            label={t("auth.login.rememberMe")}
            className="mt-4"
          />
          <div className="text-sm text-neutral-950 my-2">
            {t.rich("auth.login.privacyText", {
              userAgreement: (chunks) => (
                <Link
                  href="/user-agreement"
                  className="text-primary-600 hover:underline"
                >
                  {chunks}
                </Link>
              ),
              privacyPolicy: (chunks) => (
                <Link
                  href="/privacy-policy"
                  className="text-primary-600 hover:underline"
                >
                  {chunks}
                </Link>
              ),
              cookiePolicy: (chunks) => (
                <Link
                  href="/cookie-policy"
                  className="text-primary-600 hover:underline"
                >
                  {chunks}
                </Link>
              ),
            })}
          </div>
          {errors.root && (
            <FormAlert
              variant="error"
              message={errors.root.message as string}
            />
          )}
          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
            spinnerProps={{
              colorClass: "text-white",
              size: 20,
              text: t("auth.signup.signingUp"),
              textColorClass: "text-white",
            }}
          >
            {t("auth.signup.signupBtn")}
          </Button>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-neutral-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="bg-white px-2 text-black">
                {t("auth.signup.altSignupText")}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Button
              type="button"
              variant="ghost"
              onClick={handleGoogleSignup}
              className="flex items-center justify-center gap-2"
              isLoading={isLoading}
              spinnerProps={{
                colorClass: "text-primary-600",
                size: 20,
                text: t("auth.signup.signingUpWithGoogle"),
                textColorClass: "text-primary-600",
              }}
            >
              <svg
                className="w-5 h-5"
                viewBox="0 0 16 16"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M14.9025 6.62883H7.96333V9.50466H11.9567C11.585 11.3322 10.0292 12.3822 7.96333 12.3822C7.38527 12.3832 6.81271 12.27 6.27848 12.0492C5.74425 11.8284 5.25886 11.5044 4.85015 11.0956C4.44144 10.6868 4.11746 10.2013 3.89678 9.66707C3.6761 9.1328 3.56307 8.56022 3.56416 7.98216C3.56317 7.40418 3.67629 6.83168 3.89702 6.29751C4.11775 5.76333 4.44175 5.27798 4.85045 4.86928C5.25914 4.46058 5.7445 4.13658 6.27867 3.91585C6.81285 3.69512 7.38534 3.58201 7.96333 3.583C9.0125 3.583 9.96083 3.9555 10.705 4.56466L12.8717 2.39883C11.5517 1.248 9.85916 0.537996 7.96333 0.537996C6.98484 0.535135 6.01543 0.725754 5.11087 1.09889C4.20631 1.47202 3.38444 2.02031 2.69254 2.71221C2.00064 3.40411 1.45235 4.22598 1.07922 5.13054C0.706088 6.0351 0.515469 7.0045 0.518329 7.983C0.515359 8.96152 0.705902 9.93097 1.079 10.8356C1.45209 11.7402 2.00037 12.5621 2.6923 13.254C3.38422 13.946 4.20613 14.4942 5.11074 14.8673C6.01535 15.2404 6.98481 15.431 7.96333 15.428C11.6858 15.428 15.0708 12.7205 15.0708 7.983C15.0708 7.543 15.0033 7.06883 14.9025 6.62883Z" />
              </svg>
              {t("auth.signup.googleBtn")}
            </Button>
            <Button
              type="button"
              variant="ghost"
              onClick={handleMicrosoftSignup}
              className="flex items-center justify-center gap-2"
              isLoading={isLoading}
              spinnerProps={{
                colorClass: "text-primary-600",
                size: 20,
                text: t("auth.signup.signingUpWithMicrosoft"),
                textColorClass: "text-primary-600",
              }}
            >
              <svg
                className="w-5 h-5"
                viewBox="0 0 16 16"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M7.625 15.4988H0.5V8.37378H7.625V15.4988ZM15.5 15.4988H8.375V8.37378H15.5V15.4988ZM7.625 7.62378H0.5V0.498779H7.625V7.62378ZM15.5 7.62378H8.375V0.498779H15.5V7.62378Z" />
              </svg>
              {t("auth.signup.microsoftBtn")}
            </Button>
          </div>
        </form>
      </FormProvider>
      <div className="text-center text-sm mt-4">
        {t("auth.signup.alreadyHaveAccount")}{" "}
        <Link href="/login" className="text-primary-600 hover:underline">
          {t("auth.signup.loginLink")}
        </Link>
      </div>
    </div>
  );
};

export default SignupForm;
