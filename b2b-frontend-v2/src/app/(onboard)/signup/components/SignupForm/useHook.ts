"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { SignupFormData, createSignupSchema } from "./validation";
import { useTranslations } from "next-intl";

const useSignupForm = () => {
  const [rememberMe, setRememberMe] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const t = useTranslations();

  const schema = createSignupSchema(t);

  const methods = useForm<SignupFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const { setError, reset } = methods;

  const onSubmit = async (data: SignupFormData) => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log({ ...data, rememberMe });
      resetSignupForm();
    } catch (error) {
      console.log(error);
      setError("root", { message: "Signup failed. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleRememberMe = () => {
    setRememberMe((prev) => !prev);
  };

  const handleGoogleSignup = async () => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.log(error);
      setError("root", { message: "Signup failed. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  const handleMicrosoftSignup = async () => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.log(error);
      setError("root", { message: "Signup failed. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  const resetSignupForm = () => {
    reset();
    setRememberMe(false);
    setIsLoading(false);
  };

  return {
    methods,
    isLoading,
    rememberMe,
    toggleRememberMe,
    handleGoogleSignup,
    handleMicrosoftSignup,
    onSubmit,
  };
};

export default useSignupForm;
