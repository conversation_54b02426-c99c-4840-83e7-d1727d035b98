"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { createDemoRequestSchema, DemoRequestFormData } from "./validation";
import { IdTitleI, StringNullI } from "@/types/common/data";

const useDemoRequestForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [selectedNumberOfShips, setSelectedNumberOfShips] =
    useState<IdTitleI | null>(null);
  const t = useTranslations();

  const methods = useForm<DemoRequestFormData>({
    resolver: zodResolver(createDemoRequestSchema(t)),
    defaultValues: {
      fullName: "",
      workEmail: "",
      phoneNumber: "",
      position: "",
      companyName: "",
      numberOfShips: "",
    },
  });

  const { setError, reset, setValue } = methods;

  const onSubmit = async (data: DemoRequestFormData) => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Form submitted:", data);
      setIsSuccess(true);
      resetDemoRequestForm();
    } catch (error) {
      console.log(error);
      setError("root", {
        message: t("form.messages.error"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateNumberOfShips = (id: StringNullI) => {
    const shipOption = id
      ? {
          id,
          title: t.raw(
            `demoRequest.form.fields.numberOfShips.options.${id}`,
          ) as string,
        }
      : null;
    setSelectedNumberOfShips(shipOption);
    setValue("numberOfShips", shipOption?.id ?? "", { shouldValidate: true });
  };

  const resetDemoRequestForm = () => {
    reset();
    setSelectedNumberOfShips(null);
    setIsSuccess(false);
    setIsLoading(false);
  };

  return {
    methods,
    isLoading,
    isSuccess,
    onSubmit,
    selectedNumberOfShips,
    updateNumberOfShips,
  };
};

export default useDemoRequestForm;
