import { TFunctionI } from "@/types/common/data";
import { z } from "zod";

export const createDemoRequestSchema = (t: TFunctionI) =>
  z.object({
    fullName: z
      .string()
      .min(1, { message: t("demoRequest.form.validation.fullName.required") })
      .min(2, { message: t("demoRequest.form.validation.fullName.min") }),
    workEmail: z
      .string()
      .min(1, { message: t("demoRequest.form.validation.workEmail.required") })
      .email({ message: t("demoRequest.form.validation.workEmail.invalid") }),
    phoneNumber: z
      .string()
      .min(1, {
        message: t("demoRequest.form.validation.phoneNumber.required"),
      })
      .min(10, {
        message: t("demoRequest.form.validation.phoneNumber.invalid"),
      }),
    position: z
      .string()
      .min(1, { message: t("demoRequest.form.validation.position.required") })
      .min(2, { message: t("demoRequest.form.validation.position.min") }),
    companyName: z
      .string()
      .min(1, {
        message: t("demoRequest.form.validation.companyName.required"),
      })
      .min(2, { message: t("demoRequest.form.validation.companyName.min") }),
    numberOfShips: z.string().min(1, {
      message: t("demoRequest.form.validation.numberOfShips.required"),
    }),
  });

export type DemoRequestFormData = z.infer<
  ReturnType<typeof createDemoRequestSchema>
>;
