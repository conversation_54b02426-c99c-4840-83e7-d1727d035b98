import { Metadata, Viewport } from "next";
import { Inter, Manrope } from "next/font/google";
import { NextIntlClientProvider } from "next-intl";
import { getMessages, getLocale } from "next-intl/server";
import { getLangDir } from "rtl-detect";
import "@/styles/globals.css";

export const viewport: Viewport = {
  initialScale: 1,
  width: "device-width",
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: "Navicater Solutions",
  description:
    "Navicater Solutions specializes in cutting-edge AI tools and research-driven solutions for the maritime industry, empowering professionals with advanced insights and troubleshooting capabilities.",
  authors: [{ name: "Navicater Solutions", url: "https://navicater.com" }],
  manifest: "/manifest.webmanifest",
  metadataBase: new URL("https://dashboard.navicater.com"),
  appleWebApp: {
    capable: true,
    title: "Navicater",
    statusBarStyle: "default",
  },
  icons: {
    icon: [
      { url: "/assets/images/logo/logo-48x48.png" },
      { url: "/assets/images/logo/logo-72x72.png" },
      { url: "/assets/images/logo/logo-192x192.png" },
    ],
    apple: [{ url: "/assets/images/logo/logo-48x48.png" }],
  },
};

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const manrope = Manrope({
  subsets: ["latin"],
  variable: "--font-manrope",
});

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const messages = await getMessages();
  const locale = await getLocale();
  const direction = getLangDir(locale);

  return (
    <html lang={locale} dir={direction}>
      <head>
        <link
          rel="shortcut icon"
          href="/assets/images/logo/logo-48x48.png"
          type="image/x-icon"
        />
      </head>
      <body className={`${inter.variable} ${manrope.variable} font-inter`}>
        <NextIntlClientProvider messages={messages}>
          <main>{children}</main>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
