"use client";

import { useTranslations } from "next-intl";
import { FormProvider } from "react-hook-form";
import React from "react";
import TextInput from "@/components/TextInput";
import Button from "@/components/Button";
import FormAlert from "@/components/Alert";
import useForgotPassword from "./useHook";
import FormHeading from "@/components/FormHeading";

const ForgotPasswordForm: React.FC = () => {
  const { methods, isLoading, isSuccess, onSubmit } = useForgotPassword();
  const {
    formState: { errors },
  } = methods;
  const t = useTranslations();

  return (
    <div className="flex flex-col items-center w-full max-w-[430px] mx-auto p-6">
      <FormHeading
        title={t("auth.forgotPwd.title")}
        subTitle=""
        useCompactLogo={false}
      />
      <FormProvider {...methods}>
        <form
          className="grid grid-cols-1 gap-6 w-full bg-white p-6 rounded-xl"
          onSubmit={methods.handleSubmit(onSubmit)}
        >
          <div className="col-span-1">
            <div className="w-full">
              <TextInput
                label={t("auth.forgotPwd.email.label")}
                placeholder={t("auth.forgotPwd.email.placeholder")}
                name="email"
                type="email"
                errorText={errors.email?.message}
              />
            </div>
          </div>
          <div className="col-span-1">
            <p className="text-sm text-neutral-500 mb-4">
              {t("auth.forgotPwd.description")}
            </p>
          </div>
          {isSuccess && (
            <div className="col-span-1">
              <FormAlert
                variant="success"
                message={t("auth.forgotPwd.resetSent")}
              />
            </div>
          )}
          {errors.root && (
            <div className="col-span-1">
              <FormAlert
                variant="error"
                message={errors.root.message as string}
              />
            </div>
          )}
          <div className="col-span-1">
            <Button
              variant="primary"
              isLoading={isLoading}
              type="submit"
              spinnerProps={{
                colorClass: "text-white",
                size: 20,
                text: t("auth.forgotPwd.sendingBtn"),
                textColorClass: "text-white",
              }}
            >
              {t("auth.forgotPwd.sendBtn")}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default ForgotPasswordForm;
