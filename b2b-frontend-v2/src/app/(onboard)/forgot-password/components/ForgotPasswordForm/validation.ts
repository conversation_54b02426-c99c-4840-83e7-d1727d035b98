import { TFunctionI } from "@/types/common/data";
import { z } from "zod";

export const createForgotPasswordSchema = (t: TFunctionI) =>
  z.object({
    email: z
      .string()
      .trim()
      .min(1, { message: t("auth.forgotPwd.validation.email.required") })
      .email({ message: t("auth.forgotPwd.validation.email.invalid") }),
  });

export type ForgotPasswordFormData = z.infer<
  ReturnType<typeof createForgotPasswordSchema>
>;
