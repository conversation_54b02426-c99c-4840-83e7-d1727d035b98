"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import {
  createForgotPasswordSchema,
  ForgotPasswordFormData,
} from "./validation";

const useForgotPassword = () => {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const methods = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(createForgotPasswordSchema(t)),
    defaultValues: {
      email: "",
    },
  });

  const { setError, reset } = methods;

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log({ data });
      setIsSuccess(true);
      resetForgotPasswordForm();
    } catch (error) {
      console.log(error);
      setError("root", {
        message: t("auth.forgotPwd.error"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForgotPasswordForm = () => {
    reset();
    setIsLoading(false);
    setIsSuccess(false);
  };

  return {
    methods,
    isLoading,
    isSuccess,
    onSubmit,
  };
};

export default useForgotPassword;
