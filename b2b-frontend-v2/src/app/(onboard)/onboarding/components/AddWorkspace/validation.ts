import { z } from "zod";
import { TFunctionI } from "@/types/common/data";

export const createWorkspaceSchema = (t: TFunctionI) =>
  z.object({
    workspaceName: z
      .string()
      .min(1, {
        message: t("onboarding.workspace.validation.name.required"),
      })
      .min(2, {
        message: t("onboarding.workspace.validation.name.min", {
          min: 2,
        }),
      })
      .max(50, {
        message: t("onboarding.workspace.validation.name.max", {
          max: 50,
        }),
      }),
    address: z
      .string()
      .min(1, {
        message: t("onboarding.workspace.validation.address.required"),
      })
      .min(5, {
        message: t("onboarding.workspace.validation.address.min", { min: 5 }),
      })
      .max(200, {
        message: t("onboarding.workspace.validation.address.max", { max: 200 }),
      }),
    description: z
      .string()
      .max(500, {
        message: t("onboarding.workspace.validation.description.max", {
          max: 500,
        }),
      })
      .optional(),
  });

export type WorkspaceFormData = z.infer<
  ReturnType<typeof createWorkspaceSchema>
>;
