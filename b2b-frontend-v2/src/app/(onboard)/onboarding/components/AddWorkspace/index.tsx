"use client";

import { useTranslations } from "next-intl";
import { StepComponentProps } from "../OnboardingStepper/types";
import { FormProvider } from "react-hook-form";
import React, { useEffect, useImperativeHandle } from "react";
import TextInput from "@/components/TextInput";
import useWorkspaceSetup from "./useHook";
import FormHeading from "@/components/FormHeading";

const AddWorkspace: React.FC<StepComponentProps> = ({ ref, formData }) => {
  const t = useTranslations();
  const { methods } = useWorkspaceSetup();
  const {
    formState: { errors },
    setValue,
  } = methods;

  useImperativeHandle(ref, () => ({
    triggerValidation: async () => {
      const result = await methods.trigger();
      return result;
    },
    getFormData: () => methods.getValues(),
  }));

  useEffect(() => {
    if (formData?.AddWorkspace) {
      const { workspaceName, address, description } = formData.AddWorkspace;

      if (workspaceName) setValue("workspaceName", workspaceName);
      if (address) setValue("address", address);
      if (description) setValue("description", description);
    }
  }, [formData]);

  return (
    <div className="flex flex-col items-center w-full">
      <FormHeading
        title={t("onboarding.workspace.title")}
        subTitle={t("onboarding.workspace.subtitle")}
      />
      <FormProvider {...methods}>
        <form
          onSubmit={(e) => e.preventDefault()}
          className="grid grid-cols-1 md:grid-cols-2 gap-4 p-5 rounded-2xl w-full h-full bg-white"
        >
          <div className="col-span-1 md:col-span-2">
            <div className="w-full md:w-1/2">
              <TextInput
                label={t("onboarding.workspace.name.label")}
                placeholder={t("onboarding.workspace.name.placeholder")}
                name="workspaceName"
                errorText={errors.workspaceName?.message}
              />
            </div>
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.workspace.address.label")}
              type="textarea"
              placeholder={t("onboarding.workspace.address.placeholder")}
              name="address"
              errorText={errors.address?.message}
              rows={4}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.workspace.description.label")}
              placeholder={t("onboarding.workspace.description.placeholder")}
              name="description"
              type="textarea"
              errorText={errors.description?.message}
              rows={4}
              optional
            />
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default AddWorkspace;
