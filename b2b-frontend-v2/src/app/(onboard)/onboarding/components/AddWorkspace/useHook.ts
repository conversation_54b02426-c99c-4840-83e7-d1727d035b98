"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { WorkspaceFormData, createWorkspaceSchema } from "./validation";

const useWorkspaceSetup = () => {
  const t = useTranslations();

  const methods = useForm<WorkspaceFormData>({
    resolver: zodResolver(createWorkspaceSchema(t)),
    defaultValues: {
      workspaceName: "",
      address: "",
      description: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  return {
    methods,
  };
};

export default useWorkspaceSetup;
