"use client";

import { FormProvider } from "react-hook-form";
import { useTranslations } from "next-intl";
import { StepComponentProps } from "../OnboardingStepper/types";
import React, { useImperativeHandle, useEffect } from "react";
import Select from "@/components/Select";
import TextInput from "@/components/TextInput";
import useProfileDetails from "./useHook";
import FormHeading from "@/components/FormHeading";

const ProfileDetails: React.FC<StepComponentProps> = ({ ref, formData }) => {
  const t = useTranslations();
  const { methods, countries, country, updateCountry } = useProfileDetails();

  const {
    formState: { errors },
    setValue,
  } = methods;

  useImperativeHandle(ref, () => ({
    triggerValidation: async () => {
      const result = await methods.trigger();
      return result;
    },
    getFormData: () => methods.getValues(),
  }));

  useEffect(() => {
    if (formData?.ProfileDetails) {
      const {
        firstName,
        lastName,
        country: countryId,
        passportNo,
        phoneNumber,
        employmentId,
      } = formData.ProfileDetails;

      if (firstName) setValue("firstName", firstName);
      if (lastName) setValue("lastName", lastName);
      if (countryId) updateCountry(countryId);
      if (passportNo) setValue("passportNo", passportNo);
      if (phoneNumber) setValue("phoneNumber", phoneNumber);
      if (employmentId) setValue("employmentId", employmentId);
    }
  }, [formData]);

  return (
    <div className="flex flex-col items-center w-full">
      <FormHeading
        title={t("onboarding.profile.title")}
        subTitle={t("onboarding.profile.subtitle")}
      />
      <FormProvider {...methods}>
        <form
          onSubmit={(e) => e.preventDefault()}
          className="grid grid-cols-1 md:grid-cols-2 gap-4 p-5 rounded-2xl w-full h-full bg-white"
        >
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.profile.firstName.label")}
              placeholder={t("onboarding.profile.firstName.placeholder")}
              name="firstName"
              errorText={errors.firstName?.message}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.profile.lastName.label")}
              placeholder={t("onboarding.profile.lastName.placeholder")}
              name="lastName"
              errorText={errors.lastName?.message}
            />
          </div>
          <div className="col-span-1">
            <Select
              handleChange={updateCountry}
              label={t("onboarding.profile.country.label")}
              name="country"
              options={countries}
              selected={country}
              placeholder={t("onboarding.profile.country.placeholder")}
              errorText={errors.country?.message}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.profile.passport.label")}
              placeholder={t("onboarding.profile.passport.placeholder")}
              name="passportNo"
              errorText={errors.passportNo?.message}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.profile.phone.label")}
              placeholder={t("onboarding.profile.phone.placeholder")}
              name="phoneNumber"
              errorText={errors.phoneNumber?.message}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.profile.employmentId.label")}
              placeholder={t("onboarding.profile.employmentId.placeholder")}
              name="employmentId"
              errorText={errors.employmentId?.message}
            />
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default ProfileDetails;
