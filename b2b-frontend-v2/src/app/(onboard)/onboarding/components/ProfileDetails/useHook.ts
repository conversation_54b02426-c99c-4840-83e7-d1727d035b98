"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import {
  ProfileDetailsFormData,
  createProfileDetailsSchema,
} from "./validation";
import { IdTitleI, StringNullI } from "@/types/common/data";

const countries: IdTitleI[] = [
  { id: "1", title: "India" },
  { id: "2", title: "USA" },
  { id: "3", title: "UK" },
  { id: "4", title: "Canada" },
];

const useProfileDetails = () => {
  const t = useTranslations();

  const [country, setCountry] = useState<IdTitleI | null>(null);

  const methods = useForm<ProfileDetailsFormData>({
    resolver: zodResolver(createProfileDetailsSchema(t)),
    defaultValues: {
      firstName: "",
      lastName: "",
      country: "",
      passportNo: "",
      phoneNumber: "",
      employmentId: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const { setValue } = methods;

  const updateCountry = (id: StringNullI) => {
    const selectedCountry = id
      ? (countries.find((n) => n.id === id) ?? null)
      : null;
    setCountry(selectedCountry);
    setValue("country", selectedCountry?.id ?? "", {
      shouldValidate: true,
    });
  };

  return {
    methods,
    countries,
    country,
    updateCountry,
  };
};

export default useProfileDetails;
