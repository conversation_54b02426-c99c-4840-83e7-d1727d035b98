import { z } from "zod";
import { TFunctionI } from "@/types/common/data";

export const createProfileDetailsSchema = (t: TFunctionI) =>
  z.object({
    firstName: z
      .string()
      .min(1, {
        message: t("onboarding.profile.validation.firstName.required"),
      })
      .min(2, {
        message: t("onboarding.profile.validation.firstName.min", { min: 2 }),
      })
      .max(50, {
        message: t("onboarding.profile.validation.firstName.max", { max: 50 }),
      }),
    lastName: z
      .string()
      .min(1, { message: t("onboarding.profile.validation.lastName.required") })
      .min(2, {
        message: t("onboarding.profile.validation.lastName.min", { min: 2 }),
      })
      .max(50, {
        message: t("onboarding.profile.validation.lastName.max", { max: 50 }),
      }),
    country: z.string().min(1, {
      message: t("onboarding.profile.validation.country.required"),
    }),
    passportNo: z
      .string()
      .min(1, {
        message: t("onboarding.profile.validation.passport.required"),
      })
      .regex(/^[A-Z0-9]{6,9}$/, {
        message: t("onboarding.profile.validation.passport.invalid"),
      }),
    phoneNumber: z
      .string()
      .min(1, {
        message: t("onboarding.profile.validation.phone.required"),
      })
      .regex(/^\+?[1-9]\d{1,14}$/, {
        message: t("onboarding.profile.validation.phone.invalid"),
      }),
    employmentId: z
      .string()
      .min(1, {
        message: t("onboarding.profile.validation.employmentId.required"),
      })
      .regex(/^[A-Z0-9]{5,10}$/, {
        message: t("onboarding.profile.validation.employmentId.invalid"),
      }),
  });

export type ProfileDetailsFormData = z.infer<
  ReturnType<typeof createProfileDetailsSchema>
>;
