import { WorkspaceFormData } from "../AddWorkspace/validation";
import { AdminSeatFormData } from "../AdminSeat/validation";
import { OrganisationSetupFormData } from "../OrganisationSetup/validation";
import { ProfileDetailsFormData } from "../ProfileDetails/validation";

export type StepName =
  | "ProfileDetails"
  | "OrganisationSetup"
  | "AddWorkspace"
  | "AdminSeat";

export type StepFormData = {
  ProfileDetails: ProfileDetailsFormData;
  OrganisationSetup: OrganisationSetupFormData;
  AddWorkspace: WorkspaceFormData;
  AdminSeat: AdminSeatFormData;
};

export type OnboardingStepData = StepFormData[StepName];

export interface StepFormRef {
  triggerValidation: () => Promise<boolean>;
  getFormData: () => OnboardingStepData;
}

export interface StepComponentProps {
  ref?: React.Ref<StepFormRef>;
  formData: OnboardingDataUndefinedI;
}

export interface OnboardingDataUndefinedI {
  ProfileDetails?: ProfileDetailsFormData;
  OrganisationSetup?: OrganisationSetupFormData;
  AddWorkspace?: WorkspaceFormData;
  AdminSeat?: AdminSeatFormData;
}
