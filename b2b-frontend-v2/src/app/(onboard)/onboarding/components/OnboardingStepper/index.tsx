"use client";

import React, { useRef } from "react";
import { useTranslations } from "next-intl";
import { StepFormRef, StepName } from "./types";
import ProfileDetails from "../ProfileDetails";
import OrganisationSetup from "../OrganisationSetup";
import AddWorkspace from "../AddWorkspace";
import AdminSeat from "../AdminSeat";
import useOnboardingStepper from "./useHook";
import Button from "@/components/Button";

const STEP_COMPONENTS = {
  ProfileDetails,
  OrganisationSetup,
  AddWorkspace,
  AdminSeat,
};

const OnboardingStepper: React.FC = () => {
  const t = useTranslations();
  const formRef = useRef<StepFormRef>(null);

  const {
    currentStep,
    getCurrentStepName,
    getTotalSteps,
    canGoBack,
    goToNextStep,
    goToPreviousStep,
    isLastStep,
    isLoading,
    formData,
  } = useOnboardingStepper();

  const stepName = getCurrentStepName();
  const StepComponent = STEP_COMPONENTS[stepName as StepName];

  const handleNextClick = async () => {
    if (!formRef.current) return;

    try {
      const isValid = await formRef.current.triggerValidation();
      if (!isValid) return;

      const stepData = formRef.current.getFormData();
      await goToNextStep(stepData);
    } catch (error) {
      console.error("Form validation or submission failed:", error);
    }
  };

  const progressText = () =>
    t("onboarding.progress", {
      current: currentStep,
      total: getTotalSteps(),
    });

  const buttonText = () => {
    if (isLoading) return t("onboarding.loading");
    return isLastStep() ? t("onboarding.finishBtn") : t("onboarding.nextBtn");
  };

  return (
    <div className="flex flex-col max-w-[700px] mx-auto w-full px-2">
      <div className="flex-grow w-full">
        <StepComponent ref={formRef} formData={formData} />
      </div>
      <div className="flex flex-col sm:flex-row items-center justify-between w-full pt-5">
        <div className="text-sm font-medium text-neutral-500 mb-4 sm:mb-0">
          {progressText()}
        </div>
        <div className="flex gap-3 items-center w-full sm:w-auto">
          {canGoBack() && (
            <Button
              variant="ghost"
              onClick={goToPreviousStep}
              className="w-full sm:w-auto px-14"
            >
              {t("onboarding.backBtn")}
            </Button>
          )}
          <Button
            variant="primary"
            onClick={handleNextClick}
            isLoading={isLoading}
            className="w-full sm:w-auto px-6"
            spinnerProps={{
              colorClass: "text-white",
              size: 20,
              text: t("onboarding.loading"),
              textColorClass: "text-white",
            }}
          >
            {buttonText()}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default React.memo(OnboardingStepper);
