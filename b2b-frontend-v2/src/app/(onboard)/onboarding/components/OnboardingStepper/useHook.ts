import { useState } from "react";
import { useTranslations } from "next-intl";
import {
  OnboardingDataUndefinedI,
  StepName,
  OnboardingStepData,
} from "./types";

const steps: StepName[] = [
  "ProfileDetails",
  "OrganisationSetup",
  "AddWorkspace",
  "AdminSeat",
];

const initialFormData: OnboardingDataUndefinedI = {
  ProfileDetails: undefined,
  OrganisationSetup: undefined,
  AddWorkspace: undefined,
  AdminSeat: undefined,
};

const useOnboardingStepper = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] =
    useState<OnboardingDataUndefinedI>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const t = useTranslations();

  const getCurrentStepName = (): StepName => steps[currentStepIndex];
  const getTotalSteps = (): number => steps.length;
  const canGoBack = (): boolean => currentStepIndex > 0;
  const canGoNext = (): boolean => currentStepIndex < steps.length - 1;
  const isLastStep = (): boolean => currentStepIndex === steps.length - 1;

  const validateFormData = (data: OnboardingDataUndefinedI): boolean => {
    const isValid = Object.values(data).every((value) => value !== undefined);
    console.log("Form data validation result:", isValid);
    return isValid;
  };

  const onSubmit = async (
    finalData: OnboardingDataUndefinedI,
  ): Promise<void> => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    setIsLoading(true);
    try {
      if (!validateFormData(finalData)) {
        throw new Error("Missing required form data");
      }
    } catch (error) {
      console.log(error);
      setError(t("onboarding.error"));
    } finally {
      setIsLoading(false);
      setIsSubmitting(false);
    }
  };

  const goToNextStep = async (stepData: OnboardingStepData): Promise<void> => {
    setError(null);
    setIsLoading(true);

    try {
      const currentStepName = getCurrentStepName();
      const updatedFormData = {
        ...formData,
        [currentStepName]: stepData,
      };
      if (isLastStep()) {
        await onSubmit(updatedFormData);
      } else {
        setFormData(updatedFormData);
        setCurrentStepIndex((prev) => {
          const newIndex = prev + 1;
          return newIndex;
        });
      }
    } catch (error) {
      console.log(error);
      setError(t("onboarding.error"));
    } finally {
      setIsLoading(false);
    }
  };

  const goToPreviousStep = (): void => {
    if (canGoBack()) {
      setCurrentStepIndex((prev) => {
        const newIndex = prev - 1;
        return newIndex;
      });
      setError(null);
    }
  };

  return {
    currentStep: currentStepIndex + 1,
    getCurrentStepName,
    getTotalSteps,
    canGoBack,
    canGoNext,
    goToNextStep,
    goToPreviousStep,
    isLastStep,
    isLoading,
    error,
    formData,
  };
};

export default useOnboardingStepper;
