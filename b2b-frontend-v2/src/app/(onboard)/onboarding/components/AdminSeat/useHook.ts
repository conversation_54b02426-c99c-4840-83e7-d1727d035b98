"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import { AdminSeatFormData, createAdminSeatSchema } from "./validation";
import { IdTitleI, StringNullI } from "@/types/common/data";
import { zodResolver } from "@hookform/resolvers/zod";

const departments: IdTitleI[] = [
  { id: "1", title: "Operations" },
  { id: "2", title: "Procurement" },
  { id: "3", title: "Accounts" },
  { id: "4", title: "Quality HSE/SHQE" },
];

const positions: IdTitleI[] = [
  { id: "1", title: "Administrator" },
  { id: "2", title: "System Admin" },
  { id: "3", title: "Department Head" },
  { id: "4", title: "Manager" },
  { id: "5", title: "Supervisor" },
  { id: "6", title: "Team Lead" },
];

const useAdminSeat = () => {
  const t = useTranslations();

  const [selectedDepartment, setSelectedDepartment] = useState<IdTitleI | null>(
    null,
  );
  const [selectedPosition, setSelectedPosition] = useState<IdTitleI | null>(
    null,
  );

  const methods = useForm<AdminSeatFormData>({
    resolver: zodResolver(createAdminSeatSchema(t)),
    defaultValues: {
      seatId: "BHM 00001",
      department: "",
      position: "",
      user: "John Doe (You)",
      description: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const { setValue } = methods;

  const updateDepartment = (id: StringNullI) => {
    const department = id
      ? (departments.find((dept) => dept.id === id) ?? null)
      : null;
    setSelectedDepartment(department);
    setValue("department", department?.id ?? "", { shouldValidate: true });
  };

  const updatePosition = (id: StringNullI) => {
    const position = id
      ? (positions.find((pos) => pos.id === id) ?? null)
      : null;
    setSelectedPosition(position);
    setValue("position", position?.id ?? "", { shouldValidate: true });
  };

  return {
    methods,
    departments,
    positions,
    selectedDepartment,
    selectedPosition,
    updateDepartment,
    updatePosition,
  };
};

export default useAdminSeat;
