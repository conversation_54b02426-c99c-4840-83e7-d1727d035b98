"use client";

import { FormProvider } from "react-hook-form";
import { useTranslations } from "next-intl";
import { StepComponentProps } from "../OnboardingStepper/types";
import React, { useEffect, useImperativeHandle } from "react";
import Select from "@/components/Select";
import TextInput from "@/components/TextInput";
import useAdminSeat from "./useHook";
import FormHeading from "@/components/FormHeading";

const AdminSeat: React.FC<StepComponentProps> = ({ ref, formData }) => {
  const t = useTranslations();
  const {
    methods,
    departments,
    positions,
    selectedDepartment,
    selectedPosition,
    updateDepartment,
    updatePosition,
  } = useAdminSeat();

  const {
    formState: { errors },
    setValue,
  } = methods;

  useImperativeHandle(ref, () => ({
    triggerValidation: async () => {
      const result = await methods.trigger();
      return result;
    },
    getFormData: () => methods.getValues(),
  }));

  useEffect(() => {
    if (formData?.AdminSeat) {
      const { department, description, position, seatId, user } =
        formData.AdminSeat;

      if (seatId) setValue("seatId", seatId);
      if (user) setValue("user", user);
      if (description) setValue("description", description);
      if (department) updateDepartment(department);
      if (position) updatePosition(position);
    }
  }, [formData]);

  return (
    <div className="flex flex-col items-center w-full">
      <FormHeading
        title={t("onboarding.adminSeat.title")}
        subTitle={t("onboarding.adminSeat.subtitle")}
      />
      <FormProvider {...methods}>
        <form
          onSubmit={(e) => e.preventDefault()}
          className="grid grid-cols-1 md:grid-cols-2 gap-4 p-5 rounded-2xl w-full h-full bg-white"
        >
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.adminSeat.seatId.label")}
              placeholder="BHM-00001"
              name="seatId"
              disabled
            />
          </div>
          <div className="col-span-1">
            <Select
              handleChange={updateDepartment}
              label={t("onboarding.adminSeat.department.label")}
              name="department"
              options={departments}
              selected={selectedDepartment}
              placeholder={t("onboarding.adminSeat.department.placeholder")}
              errorText={errors.department?.message}
            />
          </div>
          <div className="col-span-1">
            <Select
              handleChange={updatePosition}
              label={t("onboarding.adminSeat.position.label")}
              name="position"
              options={positions}
              selected={selectedPosition}
              placeholder={t("onboarding.adminSeat.position.placeholder")}
              errorText={errors.position?.message}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.adminSeat.user.label")}
              placeholder="John Doe (You)"
              name="user"
              disabled
            />
          </div>
          <div className="col-span-1 md:col-span-2">
            <div className="w-full md:w-1/2">
              <TextInput
                label={t("onboarding.adminSeat.description.label")}
                placeholder={t("onboarding.adminSeat.description.placeholder")}
                name="description"
                type="textarea"
                errorText={errors.description?.message}
                rows={4}
                optional
              />
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default AdminSeat;
