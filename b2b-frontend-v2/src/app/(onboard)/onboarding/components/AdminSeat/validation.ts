import { z } from "zod";
import { TFunctionI } from "@/types/common/data";

export const createAdminSeatSchema = (t: TFunctionI) =>
  z.object({
    seatId: z.string(),
    department: z.string().min(1, {
      message: t("onboarding.adminSeat.validation.department.required"),
    }),
    position: z.string().min(1, {
      message: t("onboarding.adminSeat.validation.position.required"),
    }),
    user: z.string(),
    description: z
      .string()
      .max(500, {
        message: t("onboarding.adminSeat.validation.description.max", {
          max: 500,
        }),
      })
      .optional(),
  });

export type AdminSeatFormData = z.infer<
  ReturnType<typeof createAdminSeatSchema>
>;
