import { z } from "zod";
import { TFunctionI } from "@/types/common/data";

export const createOrganisationSetupSchema = (t: TFunctionI) =>
  z.object({
    organisationName: z
      .string()
      .min(1, {
        message: t("onboarding.org.validation.name.required"),
      })
      .min(2, {
        message: t("onboarding.org.validation.name.min", {
          min: 2,
        }),
      })
      .max(100, {
        message: t("onboarding.org.validation.name.max", {
          max: 100,
        }),
      }),
    contactEmail: z
      .string()
      .min(1, {
        message: t("onboarding.org.validation.email.required"),
      })
      .email({
        message: t("onboarding.org.validation.email.invalid"),
      }),
    phoneNumber: z
      .string()
      .min(1, {
        message: t("onboarding.org.validation.phone.required"),
      })
      .regex(/^\+?[1-9]\d{1,14}$/, {
        message: t("onboarding.org.validation.phone.invalid"),
      }),
    address: z
      .string()
      .min(1, {
        message: t("onboarding.org.validation.address.required"),
      })
      .min(5, {
        message: t("onboarding.org.validation.address.min", {
          min: 5,
        }),
      })
      .max(200, {
        message: t("onboarding.org.validation.address.max", {
          max: 200,
        }),
      }),
  });

export type OrganisationSetupFormData = z.infer<
  ReturnType<typeof createOrganisationSetupSchema>
>;
