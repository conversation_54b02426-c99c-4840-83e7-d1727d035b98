"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import {
  OrganisationSetupFormData,
  createOrganisationSetupSchema,
} from "./validation";

const useOrganisationSetup = () => {
  const t = useTranslations();
  const methods = useForm<OrganisationSetupFormData>({
    resolver: zodResolver(createOrganisationSetupSchema(t)),
    defaultValues: {
      organisationName: "",
      contactEmail: "",
      phoneNumber: "",
      address: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  return {
    methods,
  };
};

export default useOrganisationSetup;
