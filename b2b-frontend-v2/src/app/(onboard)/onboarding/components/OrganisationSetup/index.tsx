"use client";

import { FormProvider } from "react-hook-form";
import { useTranslations } from "next-intl";
import { StepComponentProps } from "../OnboardingStepper/types";
import React, { useImperativeHandle, useEffect } from "react";
import TextInput from "@/components/TextInput";
import useOrganisationSetup from "./useHook";
import FormHeading from "@/components/FormHeading";

const OrganisationSetup: React.FC<StepComponentProps> = ({ ref, formData }) => {
  const t = useTranslations();
  const { methods } = useOrganisationSetup();
  const {
    formState: { errors },
    setValue,
  } = methods;

  useImperativeHandle(ref, () => ({
    triggerValidation: async () => {
      const result = await methods.trigger();
      return result;
    },
    getFormData: () => methods.getValues(),
  }));

  useEffect(() => {
    if (formData?.OrganisationSetup) {
      const { organisationName, contactEmail, phoneNumber, address } =
        formData.OrganisationSetup;

      if (organisationName) setValue("organisationName", organisationName);
      if (contactEmail) setValue("contactEmail", contactEmail);
      if (phoneNumber) setValue("phoneNumber", phoneNumber);
      if (address) setValue("address", address);
    }
  }, [formData]);

  return (
    <div className="flex flex-col items-center w-full">
      <FormHeading
        title={t("onboarding.org.title")}
        subTitle={t("onboarding.org.subtitle")}
      />
      <FormProvider {...methods}>
        <form
          onSubmit={(e) => e.preventDefault()}
          className="grid grid-cols-1 md:grid-cols-2 gap-4 p-5 rounded-2xl w-full h-full bg-white"
        >
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.org.name.label")}
              placeholder={t("onboarding.org.name.placeholder")}
              name="organisationName"
              errorText={errors.organisationName?.message}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.org.email.label")}
              placeholder={t("onboarding.org.email.placeholder")}
              name="contactEmail"
              errorText={errors.contactEmail?.message}
            />
          </div>
          <div className="col-span-1">
            <TextInput
              label={t("onboarding.org.phone.label")}
              placeholder={t("onboarding.org.phone.placeholder")}
              name="phoneNumber"
              errorText={errors.phoneNumber?.message}
            />
          </div>
          <div className="col-span-1 md:col-span-2">
            <div className="w-full md:w-1/2">
              <TextInput
                label={t("onboarding.org.address.label")}
                placeholder={t("onboarding.org.address.placeholder")}
                name="address"
                type="textarea"
                errorText={errors.address?.message}
                rows={4}
              />
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default OrganisationSetup;
