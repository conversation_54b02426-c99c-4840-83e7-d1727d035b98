import { MetadataRoute } from "next";

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "Navicater Solutions",
    short_name: "Navicater",
    description:
      "Maritime AI tools and research-driven solutions for professional troubleshooting and insights",
    start_url: "/",
    orientation: "portrait",
    background_color: "#ffffff",
    theme_color: "#ffffff",
    categories: ["business", "productivity", "utilities", "maritime", "marine"],
    screenshots: [],
    icons: [
      {
        src: "/assets/images/logo/logo-48x48.png",
        sizes: "48x48",
        type: "image/png",
        purpose: "any",
      },
      {
        src: "/assets/images/logo/logo-72x72.png",
        sizes: "72x72",
        type: "image/png",
      },
      {
        src: "/assets/images/logo/logo-96x96.png",
        sizes: "96x96",
        type: "image/png",
      },
      {
        src: "/assets/images/logo/logo-128x128.png",
        sizes: "128x128",
        type: "image/png",
      },
      {
        src: "/assets/images/logo/logo-144x144.png",
        sizes: "144x144",
        type: "image/png",
      },
      {
        src: "/assets/images/logo/logo-152x152.png",
        sizes: "152x152",
        type: "image/png",
      },
      {
        src: "/assets/images/logo/logo-192x192.png",
        sizes: "192x192",
        type: "image/png",
      },
      {
        src: "/assets/images/logo/logo-384x384.png",
        sizes: "384x384",
        type: "image/png",
      },
      {
        src: "/assets/images/logo/logo-512x512.png",
        sizes: "512x512",
        type: "image/png",
      },
    ],
    shortcuts: [],
  };
}
