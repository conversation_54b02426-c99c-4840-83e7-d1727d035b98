import Sidebar from "@/components/Sidebar";
import { assistantSidebarItems } from "@/constants/assistant/data";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export const metadata: Metadata = {
  title: "AI",
  description:
    "Chat dashboard for managing Navicater Solutions' maritime AI tools and services.",
  keywords: ["Chat panel", "Maritime management"],
};

export default async function AILayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const t = await getTranslations();
  return (
    <div className="flex flex-1">
      <Sidebar
        title={t("chatSidebar.title")}
        items={assistantSidebarItems}
        level={2}
        className="bg-neutral-50"
      />
      <div className="flex-1 relative">
        <div className="absolute inset-0 overflow-auto">{children}</div>
      </div>
    </div>
  );
}
