import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { settingSidebarItems } from "@/constants/settings/data";
import Sidebar from "@/components/Sidebar";

export const metadata: Metadata = {
  title: "Settings",
  description:
    "Settings dashboard for managing Navicater Solutions' maritime AI tools and services settings.",
  keywords: ["Admin panel", "Maritime management"],
};

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default async function SettingsLayout({
  children,
}: SettingsLayoutProps) {
  const t = await getTranslations();

  return (
    <div className="flex flex-1">
      <Sidebar
        title={t("settingSidebar.title")}
        items={settingSidebarItems}
        level={2}
        className="bg-neutral-50"
      />
      <div className="flex-1 relative">
        <div className="absolute inset-0 overflow-auto">{children}</div>
      </div>
    </div>
  );
}
