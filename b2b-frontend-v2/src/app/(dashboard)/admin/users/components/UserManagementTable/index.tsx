"use client";

import { useTranslations } from "next-intl";
import { User } from "./types";
import Button from "@/components/Button";
import useUsers from "./useHook";
import Badge from "@/components/Badge";
import Table from "@/components/Table";
import ImportDialog from "../UserImportModal";
import AddUserDialog from "../UserAddModal";

const UserManagementTable = () => {
  const t = useTranslations();
  const {
    users,
    selectedUsers,
    handleSearch,
    handleSelectUser,
    handleSelectAll,
    handleAddUser,
    handleImportCSV,
    isImporting,
    isAdding,
    isAddModalOpen,
    isImportModalOpen,
    selectedFile,
    setIsAddModalOpen,
    setIsImportModalOpen,
    setSelectedFile,
  } = useUsers();

  const columns = [
    {
      key: "fullName",
      title: "Name & Email",
      render: (row: User) => (
        <div>
          <div className="text-sm font-medium text-neutral-900">
            {row.fullName}
          </div>
          <div className="text-sm text-neutral-600">{row.email}</div>
        </div>
      ),
    },
    {
      key: "phoneNumber",
      title: "Contact Number",
    },
    {
      key: "employmentId",
      title: "Employment ID",
    },
    {
      key: "status",
      title: "Status",
      width: "100px",
      render: (row: User) => (
        <Badge variant={row.status === "Active" ? "success" : "danger"}>
          {row.status}
        </Badge>
      ),
    },
  ];

  const handleFileSelect = (files: File[]) => {
    if (files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  const handleImport = async () => {
    if (selectedFile) {
      await handleImportCSV(selectedFile);
      setIsImportModalOpen(false);
      setSelectedFile(null);
    }
  };

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-neutral-900">
          {t("userManagement.users")}
        </h1>
        <div className="flex items-center gap-3">
          <Button variant="ghost" onClick={() => setIsImportModalOpen(true)}>
            Import
          </Button>
          <Button variant="primary" onClick={() => setIsAddModalOpen(true)}>
            Add
          </Button>
        </div>
      </div>
      <Table
        columns={columns}
        data={users}
        selectable
        searchable
        searchPlaceholder="Search"
        selectedRows={selectedUsers}
        onRowSelect={handleSelectUser}
        onSelectAll={handleSelectAll}
        onSearch={handleSearch}
      />
      <ImportDialog
        isOpen={isImportModalOpen}
        closeModal={() => setIsImportModalOpen(false)}
        handleFileSelect={handleFileSelect}
        handleImport={handleImport}
        isLoading={isImporting}
        selectedFile={selectedFile}
      />
      <AddUserDialog
        isOpen={isAddModalOpen}
        closeModal={() => setIsAddModalOpen(false)}
        handleAddUser={handleAddUser}
        isLoading={isAdding}
      />
    </>
  );
};

export default UserManagementTable;
