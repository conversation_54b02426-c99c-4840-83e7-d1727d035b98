"use client";

import { useState } from "react";
import { User } from "./types";
import { StringUndefinedI } from "@/types/common/data";
import { userImportSchema } from "./validation";
import { AddUserFormData } from "../UserAddModal/validation";
import Papa from "papaparse";

const generateMockUsers = () => {
  try {
    return Array.from({ length: 100 }, (_, i) => ({
      id: String(i + 1),
      fullName: `User${i + 1}`,
      email: `user${i + 1}@mail.com`,
      phoneNumber: `121212341${String(30 + i).padStart(2, "0")}`,
      country: "India",
      employmentId: `EMP${String(1000 + i)}`,
      status: i % 2 === 0 ? "Active" : "Inactive",
    }));
  } catch (error) {
    console.error("Error generating mock users:", error);
    return [];
  }
};

const mockUsers = generateMockUsers();

const useUsers = () => {
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const handleSearch = (query: StringUndefinedI) => {
    try {
      if (!query) {
        setUsers(mockUsers);
        return;
      }
      const filtered = mockUsers.filter((user) =>
        user.email.toLowerCase().includes(query.toLowerCase()),
      );
      setUsers(filtered);
    } catch (error) {
      console.error("Search error:", error);
      setUsers(mockUsers);
    }
  };

  const handleSelectUser = (selectedIds: string[]) => {
    try {
      setSelectedUsers(selectedIds);
    } catch (error) {
      console.error("Selection error:", error);
      setSelectedUsers([]);
    }
  };

  const handleSelectAll = (selected: boolean) => {
    try {
      setSelectedUsers(selected ? users.map((user) => user.id) : []);
    } catch (error) {
      console.error("Select all error:", error);
      setSelectedUsers([]);
    }
  };

  const handleAddUser = async (data: AddUserFormData): Promise<void> => {
    setIsAdding(true);
    try {
      const { assignments, firstName, lastName, ...userData } = data;
      console.log("Assignments", assignments);
      const newUser: User = {
        id: String(users.length + 1),
        ...userData,
        fullName: `${firstName} ${lastName.trim()}`.trim(),
        email: `${String(users.length + 1)}@example.com`,
        status: "Active",
        country: userData.country,
      };
      setUsers((prevUsers) => [newUser, ...prevUsers]);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.error("Add user error:", error);
      throw error;
    } finally {
      setIsAdding(false);
    }
  };

  const handleImportCSV = async (file: File): Promise<void> => {
    setIsImporting(true);
    try {
      const text = await file.text();
      return new Promise<void>((resolve, reject) => {
        Papa.parse<Record<string, unknown>>(text, {
          header: true,
          skipEmptyLines: true,
          complete: (results) => {
            try {
              const validUsers: User[] = [];
              const errors: string[] = [];
              results.data.forEach((row, index) => {
                try {
                  const validatedUser = userImportSchema.parse({ ...row });
                  validUsers.push({
                    id: String(users.length + validUsers.length + 1),
                    ...validatedUser,
                    email: validatedUser.email,
                    status: "Active",
                  });
                } catch (error) {
                  errors.push(
                    `Row ${index + 2}: ${error instanceof Error ? error.message : "Unknown error"}`,
                  );
                }
              });
              if (errors.length > 0) {
                reject(new Error(`Validation errors:\n${errors.join("\n")}`));
              } else {
                setUsers((prevUsers) => [...prevUsers, ...validUsers]);
                resolve();
              }
            } catch (error) {
              reject(error);
            }
          },
          error: (error: unknown) => {
            console.error("CSV parsing error:", error);
            reject(new Error("Failed to parse CSV file"));
          },
        });
      });
    } catch (error) {
      console.error("Import error:", error);
      throw error;
    } finally {
      setIsImporting(false);
    }
  };

  return {
    users,
    selectedUsers,
    handleSearch,
    handleSelectUser,
    handleSelectAll,
    handleImportCSV,
    handleAddUser,
    isImporting,
    isAdding,
    isImportModalOpen,
    isAddModalOpen,
    selectedFile,
    setIsImportModalOpen,
    setIsAddModalOpen,
    setSelectedFile,
  };
};

export default useUsers;
