"use client";

import { Dialog, DialogFooter } from "@/components/Dialog";
import { useDropzone } from "react-dropzone";
import { ImportDialogProps } from "./types";
import { useTranslations } from "next-intl";
import React from "react";
import Button from "@/components/Button";

const ImportDialog: React.FC<ImportDialogProps> = ({
  closeModal,
  handleFileSelect,
  handleImport,
  isLoading,
  isOpen,
  selectedFile,
}) => {
  const t = useTranslations();

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: handleFileSelect,
    accept: {
      "text/csv": [".csv"],
    },
    multiple: false,
  });

  return (
    <Dialog
      isOpen={isOpen}
      onClose={closeModal}
      showClose={false}
      title={t("userImportModal.title")}
      description={t("userImportModal.description")}
      size="md"
    >
      <div className="space-y-4">
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
            ${isDragActive ? "border-neutral-400 bg-neutral-50" : "border-neutral-200"}
            hover:border-neutral-400`}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center gap-2">
            <svg
              className="w-12 h-12 text-neutral-400"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V7a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <div className="text-sm text-neutral-600">
              {selectedFile ? (
                <span className="font-medium">{selectedFile.name}</span>
              ) : isDragActive ? (
                <span>{t("userImportModal.dropHere")}</span>
              ) : (
                <span>
                  {t("userImportModal.dragAndDrop")}{" "}
                  <span className="text-neutral-900 font-medium">
                    {t("userImportModal.browse")}
                  </span>
                </span>
              )}
            </div>
            <div className="text-xs text-neutral-400">
              {t("userImportModal.csvOnly")}
            </div>
          </div>
        </div>
        <div className="bg-neutral-50 rounded-lg p-4 text-sm">
          <div className="font-medium text-neutral-900 mb-2">
            {t("userImportModal.csvRequirements")}
          </div>
          <ul className="list-disc list-inside text-neutral-600 space-y-1">
            <li>{t("userImportModal.csvFormat")}</li>
            <li>{t("userImportModal.headerMatch")}</li>
            <li>{t("userImportModal.requiredFields")}</li>
          </ul>
        </div>
        <DialogFooter className="mt-6">
          <Button variant="ghost" onClick={closeModal} className="px-6">
            {t("userImportModal.cancel")}
          </Button>
          <Button
            variant="primary"
            onClick={handleImport}
            disabled={!selectedFile || isLoading}
            className="px-6"
          >
            {isLoading
              ? t("userImportModal.importing")
              : t("userImportModal.importUsers")}
          </Button>
        </DialogFooter>
      </div>
    </Dialog>
  );
};

export default ImportDialog;
