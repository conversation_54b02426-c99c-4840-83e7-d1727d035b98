"use client";

import { Form<PERSON>rovider } from "react-hook-form";
import { useTranslations } from "next-intl";
import { Dialog, DialogFooter } from "@/components/Dialog";
import { AddUserDialogProps } from "./types";
import { AddUserFormData } from "./validation";
import Button from "@/components/Button";
import TextInput from "@/components/TextInput";
import Select from "@/components/Select";
import React from "react";
import useAddUserModal from "./useHook";

const AddUserDialog: React.FC<AddUserDialogProps> = ({
  isOpen,
  closeModal,
  handleAddUser,
  isLoading,
}) => {
  const t = useTranslations();
  const { methods, fields, append, remove } = useAddUserModal();

  const {
    handleSubmit,
    formState: { errors },
  } = methods;

  const onSubmitForm = async (data: AddUserFormData) => {
    try {
      await handleAddUser(data);
      methods.reset();
      closeModal();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={closeModal}
      title={t("addusermodal.title")}
      size="2xl"
    >
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-8">
          <div>
            <h2 className="text-xl font-semibold text-neutral-900 mb-6">
              {t("addusermodal.userInformation")}
            </h2>

            <div className="grid grid-cols-2 gap-6">
              <TextInput
                label={t("addusermodal.firstName")}
                name="firstName"
                placeholder={t("addusermodal.firstNamePlaceholder")}
                errorText={errors.firstName?.message}
              />
              <TextInput
                label={t("addusermodal.lastName")}
                name="lastName"
                placeholder={t("addusermodal.lastNamePlaceholder")}
                errorText={errors.lastName?.message}
              />
            </div>

            <div className="grid grid-cols-2 gap-6 mt-6">
              <Select
                label={t("addusermodal.country")}
                name="country"
                handleChange={() => {}}
                placeholder={t("addusermodal.countryPlaceholder")}
                errorText={errors.country?.message}
                options={[
                  { id: "US", title: "United States" },
                  { id: "UK", title: "United Kingdom" },
                ]}
              />
              <TextInput
                label={t("addusermodal.passportNo")}
                name="passportNo"
                placeholder={t("addusermodal.passportNoPlaceholder")}
                errorText={errors.passportNo?.message}
              />
            </div>

            <div className="grid grid-cols-2 gap-6 mt-6">
              <TextInput
                label={t("addusermodal.phoneNumber")}
                name="phoneNumber"
                placeholder={t("addusermodal.phoneNumberPlaceholder")}
                errorText={errors.phoneNumber?.message}
              />
              <TextInput
                label={t("addusermodal.employmentId")}
                name="employmentId"
                placeholder={t("addusermodal.employmentIdPlaceholder")}
                errorText={errors.employmentId?.message}
              />
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold text-neutral-900 mb-2">
              {t("addusermodal.assignTitle")}
            </h2>
            <p className="text-sm text-neutral-600 mb-6">
              {t("addusermodal.assignDescription")}
            </p>

            <div className="bg-neutral-50 rounded-lg">
              <div className="p-6 space-y-6">
                {fields.map((field, index) => (
                  <div key={field.id} className="grid grid-cols-3 gap-4">
                    <Select
                      name={`assignments.${index}.workspace`}
                      placeholder={t("addusermodal.workspacePlaceholder")}
                      handleChange={() => {}}
                      errorText={
                        errors.assignments?.[index]?.workspace?.message
                      }
                      options={[
                        { id: "ws1", title: "Workspace 1" },
                        { id: "ws2", title: "Workspace 2" },
                      ]}
                    />
                    <Select
                      name={`assignments.${index}.seat`}
                      placeholder={t("addusermodal.seatPlaceholder")}
                      errorText={errors.assignments?.[index]?.seat?.message}
                      handleChange={() => {}}
                      options={[
                        { id: "s1", title: "Seat 1" },
                        { id: "s2", title: "Seat 2" },
                      ]}
                    />
                    <div className="flex items-center gap-2">
                      <Select
                        name={`assignments.${index}.role`}
                        placeholder={t("addusermodal.rolePlaceholder")}
                        errorText={errors.assignments?.[index]?.role?.message}
                        handleChange={() => {}}
                        options={[
                          { id: "admin", title: "Admin" },
                          { id: "user", title: "User" },
                        ]}
                      />
                      {fields.length > 1 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="self-center text-red-500 hover:text-red-600"
                        >
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                          >
                            <path
                              d="M13.3333 7.5L12.5 16.6667H7.5L6.66667 7.5M15 5H5M8.33333 5L8.58333 3.75C8.68333 3.31667 9.06667 3 9.51667 3H10.4833C10.9333 3 11.3167 3.31667 11.4167 3.75L11.6667 5"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              <div className="p-4 border-t border-neutral-200">
                <button
                  type="button"
                  onClick={() => append({ workspace: "", seat: "", role: "" })}
                  className="inline-flex items-center gap-2 text-sm font-medium text-green-700 hover:text-green-800"
                >
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path
                      d="M8 3.33334V12.6667M3.33334 8H12.6667"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  {t("addusermodal.addAssignment")}
                </button>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="ghost" onClick={closeModal}>
              {t("addusermodal.cancel")}
            </Button>
            <Button variant="primary" type="submit" disabled={isLoading}>
              {isLoading ? t("addusermodal.adding") : t("addusermodal.add")}
            </Button>
          </DialogFooter>
        </form>
      </FormProvider>
    </Dialog>
  );
};

export default AddUserDialog;
