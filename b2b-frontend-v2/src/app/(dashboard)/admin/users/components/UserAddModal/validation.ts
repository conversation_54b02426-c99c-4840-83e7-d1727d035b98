import * as z from "zod";
import { TFunctionI } from "@/types/common/data";

export const createAddUserSchema = (t: TFunctionI) => {
  return z.object({
    firstName: z
      .string()
      .min(1, t("addusermodal.validation.firstName.required")),
    lastName: z.string().min(1, t("addusermodal.validation.lastName.required")),
    country: z.string().min(1, t("addusermodal.validation.country.required")),
    passportNo: z
      .string()
      .min(1, t("addusermodal.validation.passport.required")),
    phoneNumber: z
      .string()
      .min(1, t("addusermodal.validation.phone.required"))
      .regex(/^[0-9+\-\s()]+$/, t("addusermodal.validation.phone.invalid")),
    employmentId: z
      .string()
      .min(1, t("addusermodal.validation.employmentId.required")),
    assignments: z
      .array(
        z.object({
          workspace: z
            .string()
            .min(1, t("addusermodal.validation.workspace.required")),
          seat: z.string().min(1, t("addusermodal.validation.seat.required")),
          role: z.string().min(1, t("addusermodal.validation.role.required")),
        }),
      )
      .min(1, t("addusermodal.validation.assignment.required")),
  });
};

export type AddUserFormData = z.infer<ReturnType<typeof createAddUserSchema>>;
