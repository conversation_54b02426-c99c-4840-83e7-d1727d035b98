"use client";

import { useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AddUserFormData, createAddUserSchema } from "./validation";
import { useTranslations } from "use-intl";

const useAddUserModal = () => {
  const t = useTranslations();

  const addUserSchema = createAddUserSchema(t);

  const methods = useForm<AddUserFormData>({
    resolver: zodResolver(addUserSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      country: "",
      passportNo: "",
      phoneNumber: "",
      employmentId: "",
      assignments: [{ workspace: "", seat: "", role: "" }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: "assignments",
  });

  const handleRemove = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  return {
    methods,
    fields,
    append,
    remove: handleRemove,
  };
};

export default useAddUserModal;
