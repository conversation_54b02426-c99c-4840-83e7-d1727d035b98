import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { adminSidebarItems } from "@/constants/admin/data";
import Sidebar from "@/components/Sidebar";

export const metadata: Metadata = {
  title: "Admin Panel",
  description:
    "Administrative dashboard for managing Navicater Solutions' maritime AI tools and services.",
  keywords: ["Admin panel", "Maritime management"],
};

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default async function AdminLayout({ children }: AdminLayoutProps) {
  const t = await getTranslations();

  return (
    <div className="flex flex-1">
      <Sidebar
        title={t("adminSidebar.title")}
        items={adminSidebarItems}
        level={2}
        className="bg-neutral-50"
      />
      <div className="flex-1 relative">
        <div className="absolute inset-0 overflow-auto">{children}</div>
      </div>
    </div>
  );
}
