import Sidebar from "@/components/Sidebar";
import { librarySidebarItems } from "@/constants/library/data";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export const metadata: Metadata = {
  title: "Library",
  description:
    "Library dashboard for managing Navicater Solutions' maritime AI tools and services.",
  keywords: ["Library panel", "Maritime management"],
};

export default async function LibraryLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const t = await getTranslations();
  return (
    <div className="flex flex-1">
      <Sidebar
        title={t("librarySidebar.title")}
        items={librarySidebarItems}
        level={2}
        className="bg-neutral-50"
      />
      <div className="flex-1 relative">
        <div className="absolute inset-0 overflow-auto">{children}</div>
      </div>
    </div>
  );
}
