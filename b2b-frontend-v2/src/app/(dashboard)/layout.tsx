import { Metadata, Viewport } from "next";
import { Inter, Manrope } from "next/font/google";
import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";
import { getLangDir } from "rtl-detect";
import { sidebarItems } from "@/constants/common/data";
import Sidebar from "@/components/Sidebar";
import "@/styles/globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
  fallback: ["system-ui", "arial"],
});

const manrope = Manrope({
  subsets: ["latin"],
  variable: "--font-manrope",
  display: "swap",
  fallback: ["system-ui", "arial"],
});

export const viewport: Viewport = {
  initialScale: 1,
  width: "device-width",
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
  themeColor: "#ffffff",
};

export const metadata: Metadata = {
  title: {
    template: "%s | Navicater Solutions",
    default: "Navicater Solutions",
  },
  description:
    "Navicater Solutions specializes in cutting-edge AI tools and research-driven solutions for the maritime industry, empowering professionals with advanced insights and troubleshooting capabilities.",
  authors: [{ name: "Navicater Solutions", url: "https://navicater.com" }],
  manifest: "/manifest.webmanifest",
  metadataBase: new URL("https://dashboard.navicater.com"),
  appleWebApp: {
    capable: true,
    title: "Navicater",
    statusBarStyle: "default",
  },
  icons: {
    icon: [
      { url: "/assets/images/logo/logo-48x48.png", sizes: "48x48" },
      { url: "/assets/images/logo/logo-72x72.png", sizes: "72x72" },
      { url: "/assets/images/logo/logo-192x192.png", sizes: "192x192" },
    ],
    apple: [{ url: "/assets/images/logo/logo-48x48.png" }],
  },
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default async function RootLayout({ children }: RootLayoutProps) {
  const locale = await getLocale();
  const messages = await getMessages();
  const direction = getLangDir(locale);

  return (
    <html lang={locale} dir={direction} suppressHydrationWarning>
      <head>
        <link
          rel="shortcut icon"
          href="/assets/images/logo/logo-48x48.png"
          type="image/x-icon"
        />
        <meta name="google" content="notranslate" />
        <meta name="format-detection" content="telephone=no" />
      </head>
      <body
        className={`${inter.variable} ${manrope.variable} font-inter antialiased`}
      >
        <NextIntlClientProvider messages={messages} locale={locale}>
          <main className="flex min-h-screen max-h-screen w-full overflow-hidden">
            <Sidebar
              items={sidebarItems}
              hasLogo
              level={1}
              collapsed
              className="flex-shrink-0"
            />
            <div className="flex-1">{children}</div>
          </main>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
