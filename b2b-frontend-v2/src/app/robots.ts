import { MetadataRoute } from "next";

const USER_AGENT = "*";
const DISALLOW_PATHS = ["/"];
const ALLOW_PATHS = ["/privacy-policy", "/login", "/terms-and-conditions"];

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: USER_AGENT,
      disallow: DISALLOW_PATHS,
      allow: ALLOW_PATHS,
    },
    sitemap: "https://dashboard.navicater.com/sitemap.xml",
  };
}
