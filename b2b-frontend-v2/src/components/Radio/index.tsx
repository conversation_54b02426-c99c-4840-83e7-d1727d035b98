import { forwardRef } from "react";
import { RadioProps } from "./types";
import { cn } from "@/utils/class-merge";

const Radio = forwardRef<HTMLInputElement, RadioProps>(
  (
    {
      id,
      name,
      value,
      checked = false,
      disabled = false,
      onChange,
      className,
      label,
    },
    ref,
  ) => {
    return (
      <div className={className}>
        <label
          htmlFor={id}
          className="relative flex items-center gap-2 cursor-pointer"
        >
          <input
            type="radio"
            id={id}
            name={name}
            value={value}
            checked={checked}
            disabled={disabled}
            onChange={(e) => onChange?.(e.target.checked)}
            className="sr-only peer"
            ref={ref}
          />
          <div
            className={cn(
              "w-4 h-4 rounded-full border-2",
              "transition-colors duration-200",
              checked ? "border-primary-600" : "border-neutral-400",
              disabled && "opacity-50 cursor-not-allowed",
            )}
          >
            <div
              className={cn(
                "w-full h-full rounded-full",
                "transition-transform duration-200 scale-0",
                checked && "bg-primary-600 scale-[0.6]",
              )}
            />
          </div>
          {label && (
            <span className={cn("text-sm", disabled && "opacity-50")}>
              {label}
            </span>
          )}
        </label>
      </div>
    );
  },
);

Radio.displayName = "Radio";

export default Radio;
