import React from "react";
import Button from "../Button";
import Select from "@/components/Select";
import { cn } from "@/utils/class-merge";
import { PaginationProps, ITEMS_PER_PAGE_OPTIONS } from "./types";

const ChevronLeftIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.0333 12.6667L5.36667 8.00004L10.0333 3.33337"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const ChevronRightIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.96667 12.6667L10.6333 8.00004L5.96667 3.33337"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const Pagination: React.FC<PaginationProps> = ({
  totalItems,
  itemsPerPage,
  currentPage,
  onPageChange,
  onItemsPerPageChange,
  className,
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const handlePrevPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handleItemsPerPageChange = (value: string | null) => {
    if (value) {
      onItemsPerPageChange(Number(value));
    }
  };

  return (
    <div
      className={cn(
        "flex items-center justify-between px-4 py-3 bg-white",
        className,
      )}
    >
      <div className="flex items-center gap-2">
        <span className="text-sm text-neutral-600">Show</span>
        <Select
          options={ITEMS_PER_PAGE_OPTIONS}
          selected={ITEMS_PER_PAGE_OPTIONS.find(
            (opt) => opt.id === String(itemsPerPage),
          )}
          handleChange={handleItemsPerPageChange}
          placeholder="Select entries"
        />
        <span className="text-sm text-neutral-600">entries</span>
      </div>

      <div className="flex items-center gap-4">
        <div className="text-sm text-neutral-600">
          Showing {startItem} to {endItem} of {totalItems} entries
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            onClick={handlePrevPage}
            disabled={currentPage === 1}
            className="px-2 py-1"
          >
            <ChevronLeftIcon />
          </Button>

          {Array.from({ length: totalPages }, (_, i) => i + 1)
            .filter((page) => {
              return (
                page === 1 ||
                page === totalPages ||
                Math.abs(currentPage - page) <= 1
              );
            })
            .map((page, index, array) => {
              const showEllipsis = index > 0 && page - array[index - 1] > 1;

              return (
                <React.Fragment key={page}>
                  {showEllipsis && (
                    <span className="px-2 text-neutral-600">...</span>
                  )}
                  <Button
                    variant={page === currentPage ? "primary" : "ghost"}
                    onClick={() => onPageChange(page)}
                    className={cn("px-3 py-1 min-w-[32px]")}
                  >
                    {page}
                  </Button>
                </React.Fragment>
              );
            })}

          <Button
            variant="ghost"
            onClick={handleNextPage}
            disabled={currentPage === totalPages}
            className="px-2 py-1"
          >
            <ChevronRightIcon />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Pagination;
