export interface PaginationProps {
  totalItems: number;
  itemsPerPage: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  className?: string;
}

export const ITEMS_PER_PAGE_OPTIONS = [
  { id: "5", title: "5" },
  { id: "10", title: "10" },
  { id: "20", title: "20" },
  { id: "50", title: "50" },
  { id: "100", title: "100" },
];
