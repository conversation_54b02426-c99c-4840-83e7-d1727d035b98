import React from "react";
import { BadgeProps } from "./types";
import { badgeVariants } from "./consts";
import { cn } from "@/utils/class-merge";

const Badge: React.FC<BadgeProps> = ({
  variant = "default",
  className = "",
  children,
  ...props
}) => {
  const variantStyles = badgeVariants[variant];

  return (
    <div
      className={cn(
        "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium",
        variantStyles.background,
        variantStyles.text,
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
};

Badge.displayName = "Badge";
export default Badge;
