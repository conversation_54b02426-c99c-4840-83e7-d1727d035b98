"use client";

import { usePathname } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { getLangDir } from "rtl-detect";
import { cn } from "@/utils/class-merge";
import { SidebarProps, SidebarItemProps } from "./types";
import { LanguageSupported } from "@/types/common/data";
import Logo from "../Logo";
import React from "react";
import Link from "next/link";
import Tooltip from "../Tooltip";

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  iconActive,
  label,
  href,
  level,
  locale,
  direction,
  collapsed,
}) => {
  const pathname = usePathname();
  const t = useTranslations();

  const pathWithoutLocale = pathname.replace(`/${locale}`, "");
  const targetPath = href.startsWith("/") ? href : `/${href}`;

  const isActive = (() => {
    const currentSegment = pathWithoutLocale.split("/")[1];
    const targetSegment = targetPath.split("/")[1];

    if (level === 1) {
      return currentSegment === targetSegment;
    }
    return pathWithoutLocale === targetPath;
  })();

  return (
    <Link
      href={href}
      className={cn(
        "flex items-center w-full transition-all overflow-hidden text-ellipsis duration-200 ease-in-out",
        collapsed ? "p-2 justify-center" : "px-3 py-2",
        !collapsed &&
          (direction === "rtl" ? "rounded-l-full" : "rounded-r-full"),
        collapsed && "rounded-full",
        isActive
          ? "bg-secondary-100 text-primary-600"
          : "text-neutral-700 hover:bg-neutral-100",
      )}
    >
      {collapsed ? (
        <Tooltip
          content={t(label)}
          position={direction === "rtl" ? "left" : "right"}
        >
          <div
            className={cn(
              "flex-shrink-0 transition-colors duration-200",
              isActive ? "fill-primary-600" : "fill-neutral-700",
            )}
          >
            {isActive ? iconActive : icon}
          </div>
        </Tooltip>
      ) : (
        <div
          className={cn(
            "flex items-center gap-3 w-full",
            direction === "rtl" && "flex-row-reverse",
          )}
        >
          <div
            className={cn(
              "flex-shrink-0 transition-colors duration-200",
              isActive ? "fill-primary-600" : "fill-neutral-700",
            )}
          >
            {isActive ? iconActive : icon}
          </div>
          <span
            className={cn(
              "text-sm whitespace-nowrap transition-all duration-200",
              isActive ? "font-medium" : "font-normal",
            )}
          >
            {t(label)}
          </span>
        </div>
      )}
      <span className="sr-only">{t(label)}</span>
    </Link>
  );
};

const Sidebar: React.FC<SidebarProps> = ({
  items,
  hasLogo,
  title,
  className,
  level,
  collapsed = false,
}) => {
  const locale = useLocale() as LanguageSupported;
  const direction = getLangDir(locale);

  const regularItems = items.filter((item) => !item.isBottom);
  const bottomItems = items.filter((item) => item.isBottom);

  return (
    <aside
      className={cn(
        "flex flex-col h-screen z-[60] bg-white drop-shadow",
        collapsed ? "w-16 p-3" : "w-64 pt-3 pb-3",
        !collapsed && (direction === "rtl" ? "pl-3" : "pr-3"),
        className,
      )}
    >
      {hasLogo && (
        <div className={cn("flex justify-center", !collapsed && "pl-3")}>
          <Logo compact={collapsed} imageSize={32} className="w-8 h-8" />
        </div>
      )}
      {!collapsed && title && (
        <span
          className={cn(
            "text-base font-medium text-neutral-950",
            direction === "rtl" ? "pr-3" : "pl-3",
          )}
        >
          {title}
        </span>
      )}
      <nav className="space-y-3 mt-4">
        {regularItems.map((item, index) => (
          <SidebarItem
            key={index}
            {...item}
            level={level}
            direction={direction}
            locale={locale}
            collapsed={collapsed}
          />
        ))}
      </nav>
      <nav className="mt-auto space-y-3">
        {bottomItems.map((item, index) => (
          <SidebarItem
            key={index}
            {...item}
            level={level}
            direction={direction}
            locale={locale}
            collapsed={collapsed}
          />
        ))}
      </nav>
    </aside>
  );
};

export default Sidebar;
