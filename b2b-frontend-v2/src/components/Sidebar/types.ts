import { ClassNameI, LanguageSupported } from "@/types/common/data";
import { ReactNode } from "react";

export interface SidebarItem {
  icon: ReactNode;
  iconActive: ReactNode;
  label: string;
  href: string;
  isExpanded?: boolean;
  isBottom?: boolean;
}

export interface SidebarProps {
  items: SidebarItem[];
  hasLogo?: boolean;
  title?: string;
  level: number;
  className?: ClassNameI;
  collapsed?: boolean;
}

export interface SidebarItemProps extends SidebarItem {
  direction: "rtl" | "ltr";
  locale: LanguageSupported;
  level: number;
  collapsed?: boolean;
}
