import { CouponProps } from "./types";
import { cn } from "@/utils/class-merge";

const Coupon = ({
  code,
  description,
  isApplied = false,
  onApply,
  onRemove,
  className,
}: CouponProps) => {
  return (
    <div className={className}>
      <div
        className={cn(
          "flex bg-neutral-100 rounded p-2 items-center justify-between mb-2",
        )}
      >
        <div className="flex items-center gap-2">
          <svg
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M13.2069 6.49903L7 0.292153C6.90748 0.198891 6.79734 0.124949 6.67599 0.0746279C6.55464 0.0243065 6.4245 -0.00139121 6.29313 -0.000971503H0.500005C0.367397 -0.000971503 0.24022 0.051707 0.146452 0.145475C0.0526836 0.239243 5.05934e-06 0.36642 5.05934e-06 0.499028V6.29215C-0.000414649 6.42352 0.025283 6.55367 0.0756045 6.67502C0.125926 6.79637 0.199867 6.90651 0.29313 6.99903L6.5 13.2059C6.59287 13.2988 6.70312 13.3725 6.82446 13.4227C6.9458 13.473 7.07585 13.4989 7.20719 13.4989C7.33853 13.4989 7.46859 13.473 7.58993 13.4227C7.71127 13.3725 7.82152 13.2988 7.91438 13.2059L13.2069 7.9134C13.2998 7.82054 13.3734 7.71029 13.4237 7.58895C13.474 7.46761 13.4999 7.33756 13.4999 7.20622C13.4999 7.07487 13.474 6.94482 13.4237 6.82348C13.3734 6.70214 13.2998 6.59189 13.2069 6.49903ZM3.25 3.99903C3.10167 3.99903 2.95666 3.95504 2.83333 3.87263C2.70999 3.79022 2.61386 3.67309 2.5571 3.53604C2.50033 3.399 2.48548 3.2482 2.51442 3.10271C2.54335 2.95722 2.61479 2.82359 2.71967 2.7187C2.82456 2.61381 2.9582 2.54238 3.10369 2.51344C3.24917 2.4845 3.39997 2.49935 3.53702 2.55612C3.67406 2.61288 3.7912 2.70901 3.87361 2.83235C3.95602 2.95569 4 3.10069 4 3.24903C4 3.44794 3.92099 3.63871 3.78033 3.77936C3.63968 3.92001 3.44892 3.99903 3.25 3.99903Z"
              fill="#737373"
            />
          </svg>
          <div className="flex items-center gap-2">
            <span className="text-xs text-neutral-950">{code}</span>
            {isApplied && (
              <span className="text-xs text-primary-600">Applied</span>
            )}
          </div>
        </div>
        <button
          type="button"
          onClick={() => (isApplied ? onRemove?.(code) : onApply?.(code))}
          className={cn(
            "font-medium text-xs transition-colors",
            isApplied ? "text-neutral-950" : "text-primary-600",
          )}
        >
          {isApplied ? "Remove" : "Apply"}
        </button>
      </div>
      <p className="text-neutral-500 text-xs">{description}</p>
    </div>
  );
};

Coupon.displayName = "Coupin";
export default Coupon;
