import React from "react";
import { FormHeadingPropsI } from "./types";
import TextView from "../TextView";
import Logo from "../Logo";
import { cn } from "@/utils/class-merge";

const FormHeading = ({
  title,
  subTitle,
  useCompactLogo = true,
}: FormHeadingPropsI) => {
  return (
    <div
      className={cn(
        "flex flex-col items-center text-center whitespace-pre-line",
      )}
    >
      <div className="mb-8">
        <Logo
          compact={useCompactLogo}
          imageSize={useCompactLogo ? 48 : 32}
          className={cn(useCompactLogo ? "w-12 h-12" : "w-8 h-8")}
        />
      </div>
      <TextView text={title} type="title" />
      <TextView text={subTitle} type="subTitle" className="mt-2 mb-6" />
    </div>
  );
};

FormHeading.displayName = "FormHeading";

export default FormHeading;
