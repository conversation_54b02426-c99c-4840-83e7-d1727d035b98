import { useState } from "react";
import { TabsProps, TabItem } from "./types";
import { cn } from "@/utils/class-merge";

const Tabs = ({ tabs: defaultTabs, onChange, className }: TabsProps) => {
  const [tabs, setTabs] = useState<TabItem[]>(defaultTabs);

  const handleTabClick = (selectedTab: TabItem) => {
    const updatedTabs = tabs.map((tab) => ({
      ...tab,
      isActive: tab.value === selectedTab.value,
    }));
    setTabs(updatedTabs);
    onChange?.(selectedTab.value);
  };

  return (
    <div className={className}>
      <div className="flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.value}
            onClick={() => handleTabClick(tab)}
            className={cn(
              "text-lg pb-2 relative",
              tab.isActive
                ? "text-primary-600 font-medium"
                : "text-neutral-700",
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div className="h-0.5 border border-b-neutral-300">
        <div
          className="h-full border-2 border-b-primary-600 transition-all duration-300"
          style={{
            width: `${100 / tabs.length}%`,
            transform: `translateX(${tabs.findIndex((tab) => tab.isActive) * 100}%)`,
          }}
        />
      </div>
    </div>
  );
};

Tabs.displayName = "Tabs";

export default Tabs;
