import React from "react";
import { useFormContext } from "react-hook-form";
import { TextInputProps } from "./types";
import { cn } from "@/utils/class-merge";

const EyeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
    <circle cx="12" cy="12" r="3" />
  </svg>
);

const EyeOffIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
    <line x1="1" y1="1" x2="23" y2="23" />
  </svg>
);

const TextInput: React.FC<TextInputProps> = ({
  type = "text",
  label,
  optional = false,
  name,
  placeholder,
  disabled,
  rows,
  resize,
  errorText,
  className,
  ...props
}) => {
  const formContext = useFormContext();
  const [showPassword, setShowPassword] = React.useState(false);

  const registerProps = name && formContext ? formContext.register(name) : {};
  const error =
    name && formContext
      ? (formContext.formState.errors[name]?.message as string)
      : errorText;

  return (
    <div className="flex flex-col w-full">
      {label && (
        <label
          className={cn(
            "text-sm font-medium mb-2",
            disabled ? "text-neutral-300" : "text-neutral-950",
          )}
          htmlFor={name}
        >
          {label}
          {optional && (
            <span className="text-neutral-500 ml-1">(optional)</span>
          )}
        </label>
      )}
      <div className="relative">
        {type === "textarea" ? (
          <textarea
            className={cn(
              "rounded-lg outline-none w-full px-3 py-2 text-base",
              disabled
                ? "text-neutral-300 border border-neutral-300 cursor-not-allowed"
                : error
                  ? "border border-red-700 bg-red-50 text-red-900 focus:border-red-700 focus:ring-red-200"
                  : "border border-neutral-400 text-neutral-950 placeholder:text-neutral-500",
              !resize && "resize-none",
              className,
            )}
            placeholder={placeholder}
            disabled={disabled}
            rows={rows}
            id={name}
            {...registerProps}
            {...props}
          />
        ) : (
          <>
            <input
              type={type === "password" && showPassword ? "text" : type}
              placeholder={placeholder}
              className={cn(
                "rounded-lg outline-none w-full px-3 py-2 text-base",
                disabled
                  ? "text-neutral-300 border border-neutral-300 cursor-not-allowed"
                  : error
                    ? "border border-red-700 bg-red-50 text-red-900 focus:border-red-700 focus:ring-red-200"
                    : "border border-neutral-400 text-neutral-950 placeholder:text-neutral-500",
                (type === "password" || error) && "pr-10",
                className,
              )}
              disabled={disabled}
              id={name}
              {...registerProps}
              {...props}
            />
            {type === "password" && (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className={cn(
                  "absolute right-3 top-1/2 -translate-y-1/2",
                  disabled
                    ? "text-neutral-300 cursor-not-allowed"
                    : error
                      ? "text-red-500 hover:text-red-700 cursor-pointer"
                      : "text-neutral-500 hover:text-neutral-700 cursor-pointer",
                )}
                disabled={disabled}
              >
                {showPassword ? <EyeOffIcon /> : <EyeIcon />}
              </button>
            )}
          </>
        )}
      </div>
      {error && <div className="mt-1 text-sm text-red-500">{error}</div>}
    </div>
  );
};

TextInput.displayName = "TextInput";

export default TextInput;
