import React, { JSX } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./types";
import { ALERT_VARIANTS } from "./consts";
import { cn } from "@/utils/class-merge";

const ALERT_ICONS: Record<AlertVariant, JSX.Element> = {
  info: (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 0.875C7.39303 0.875 5.82214 1.35152 4.486 2.24431C3.14985 3.1371 2.10844 4.40605 1.49348 5.8907C0.87852 7.37535 0.717618 9.00901 1.03112 10.5851C1.34463 12.1612 2.11846 13.6089 3.25476 14.7452C4.39106 15.8815 5.8388 16.6554 7.4149 16.9689C8.99099 17.2824 10.6247 17.1215 12.1093 16.5065C13.594 15.8916 14.8629 14.8502 15.7557 13.514C16.6485 12.1779 17.125 10.607 17.125 9C17.1227 6.84581 16.266 4.78051 14.7427 3.25727C13.2195 1.73403 11.1542 0.877275 9 0.875ZM8.6875 4.625C8.87292 4.625 9.05418 4.67998 9.20835 4.783C9.36252 4.88601 9.48268 5.03243 9.55364 5.20373C9.6246 5.37504 9.64316 5.56354 9.60699 5.7454C9.57082 5.92725 9.48153 6.0943 9.35042 6.22541C9.2193 6.35652 9.05226 6.44581 8.8704 6.48199C8.68854 6.51816 8.50004 6.49959 8.32874 6.42864C8.15743 6.35768 8.01101 6.23752 7.908 6.08335C7.80499 5.92918 7.75 5.74792 7.75 5.5625C7.75 5.31386 7.84878 5.0754 8.02459 4.89959C8.20041 4.72377 8.43886 4.625 8.6875 4.625ZM9.625 13.375C9.29348 13.375 8.97554 13.2433 8.74112 13.0089C8.5067 12.7745 8.375 12.4565 8.375 12.125V9C8.20924 9 8.05027 8.93415 7.93306 8.81694C7.81585 8.69973 7.75 8.54076 7.75 8.375C7.75 8.20924 7.81585 8.05027 7.93306 7.93306C8.05027 7.81585 8.20924 7.75 8.375 7.75C8.70652 7.75 9.02447 7.8817 9.25889 8.11612C9.49331 8.35054 9.625 8.66848 9.625 9V12.125C9.79076 12.125 9.94974 12.1908 10.0669 12.3081C10.1842 12.4253 10.25 12.5842 10.25 12.75C10.25 12.9158 10.1842 13.0747 10.0669 13.1919C9.94974 13.3092 9.79076 13.375 9.625 13.375Z"
        fill="#075985"
      />
    </svg>
  ),
  error: (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 0.873779C7.39303 0.873779 5.82214 1.3503 4.486 2.24309C3.14985 3.13588 2.10844 4.40483 1.49348 5.88948C0.87852 7.37413 0.717618 9.00779 1.03112 10.5839C1.34463 12.16 2.11846 13.6077 3.25476 14.744C4.39106 15.8803 5.8388 16.6542 7.4149 16.9677C8.99099 17.2812 10.6247 17.1203 12.1093 16.5053C13.594 15.8903 14.8629 14.8489 15.7557 13.5128C16.6485 12.1766 17.125 10.6058 17.125 8.99878C17.1227 6.84459 16.266 4.77929 14.7427 3.25605C13.2195 1.73281 11.1542 0.876054 9 0.873779ZM11.9422 11.0566C12.0003 11.1147 12.0463 11.1836 12.0777 11.2595C12.1092 11.3353 12.1254 11.4167 12.1254 11.4988C12.1254 11.5809 12.1092 11.6622 12.0777 11.7381C12.0463 11.814 12.0003 11.8829 11.9422 11.941C11.8841 11.999 11.8152 12.0451 11.7393 12.0765C11.6634 12.108 11.5821 12.1241 11.5 12.1241C11.4179 12.1241 11.3366 12.108 11.2607 12.0765C11.1848 12.0451 11.1159 11.999 11.0578 11.941L9 9.88237L6.94219 11.941C6.88412 11.999 6.81518 12.0451 6.73931 12.0765C6.66344 12.108 6.58213 12.1241 6.5 12.1241C6.41788 12.1241 6.33656 12.108 6.26069 12.0765C6.18482 12.0451 6.11588 11.999 6.05782 11.941C5.99975 11.8829 5.95368 11.814 5.92226 11.7381C5.89083 11.6622 5.87466 11.5809 5.87466 11.4988C5.87466 11.4167 5.89083 11.3353 5.92226 11.2595C5.95368 11.1836 5.99975 11.1147 6.05782 11.0566L8.11641 8.99878L6.05782 6.94097C5.94054 6.82369 5.87466 6.66463 5.87466 6.49878C5.87466 6.33293 5.94054 6.17387 6.05782 6.05659C6.17509 5.93932 6.33415 5.87343 6.5 5.87343C6.66586 5.87343 6.82492 5.93932 6.94219 6.05659L9 8.11519L11.0578 6.05659C11.1159 5.99852 11.1848 5.95246 11.2607 5.92103C11.3366 5.88961 11.4179 5.87343 11.5 5.87343C11.5821 5.87343 11.6634 5.88961 11.7393 5.92103C11.8152 5.95246 11.8841 5.99852 11.9422 6.05659C12.0003 6.11466 12.0463 6.1836 12.0777 6.25947C12.1092 6.33534 12.1254 6.41666 12.1254 6.49878C12.1254 6.5809 12.1092 6.66222 12.0777 6.73809C12.0463 6.81396 12.0003 6.8829 11.9422 6.94097L9.8836 8.99878L11.9422 11.0566Z"
        fill="#991B1B"
      />
    </svg>
  ),
  warning: (
    <svg
      width="18"
      height="17"
      viewBox="0 0 18 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.4999 13.6945L10.6678 1.82968C10.4971 1.53899 10.2534 1.29797 9.9608 1.13051C9.66823 0.963039 9.33697 0.874939 8.99986 0.874939C8.66275 0.874939 8.33149 0.963039 8.03892 1.13051C7.74635 1.29797 7.50262 1.53899 7.33189 1.82968L0.499862 13.6945C0.335593 13.9757 0.249023 14.2955 0.249023 14.6211C0.249023 14.9467 0.335593 15.2665 0.499862 15.5476C0.668401 15.8401 0.911713 16.0824 1.20483 16.2498C1.49795 16.4171 1.83032 16.5035 2.16783 16.5H15.8319C16.1691 16.5032 16.5012 16.4167 16.794 16.2494C17.0868 16.082 17.3299 15.8399 17.4983 15.5476C17.6628 15.2666 17.7496 14.9469 17.7499 14.6213C17.7502 14.2957 17.6639 13.9758 17.4999 13.6945ZM8.37486 7.12499C8.37486 6.95923 8.44071 6.80026 8.55792 6.68305C8.67513 6.56584 8.8341 6.49999 8.99986 6.49999C9.16562 6.49999 9.32459 6.56584 9.4418 6.68305C9.55901 6.80026 9.62486 6.95923 9.62486 7.12499V10.25C9.62486 10.4158 9.55901 10.5747 9.4418 10.6919C9.32459 10.8091 9.16562 10.875 8.99986 10.875C8.8341 10.875 8.67513 10.8091 8.55792 10.6919C8.44071 10.5747 8.37486 10.4158 8.37486 10.25V7.12499ZM8.99986 14C8.81444 14 8.63319 13.945 8.47901 13.842C8.32484 13.739 8.20468 13.5926 8.13372 13.4213C8.06277 13.2499 8.0442 13.0615 8.08038 12.8796C8.11655 12.6977 8.20584 12.5307 8.33695 12.3996C8.46806 12.2685 8.63511 12.1792 8.81696 12.143C8.99882 12.1068 9.18732 12.1254 9.35863 12.1964C9.52993 12.2673 9.67635 12.3875 9.77936 12.5416C9.88238 12.6958 9.93736 12.8771 9.93736 13.0625C9.93736 13.3111 9.83859 13.5496 9.66277 13.7254C9.48696 13.9012 9.2485 14 8.99986 14Z"
        fill="#854D0E"
      />
    </svg>
  ),
  success: (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 0.875C7.39303 0.875 5.82214 1.35152 4.486 2.24431C3.14985 3.1371 2.10844 4.40605 1.49348 5.8907C0.87852 7.37535 0.717618 9.00901 1.03112 10.5851C1.34463 12.1612 2.11846 13.6089 3.25476 14.7452C4.39106 15.8815 5.8388 16.6554 7.4149 16.9689C8.99099 17.2824 10.6247 17.1215 12.1093 16.5065C13.594 15.8916 14.8629 14.8502 15.7557 13.514C16.6485 12.1779 17.125 10.607 17.125 9C17.1227 6.84581 16.266 4.78051 14.7427 3.25727C13.2195 1.73403 11.1542 0.877275 9 0.875ZM12.5672 7.56719L8.19219 11.9422C8.13415 12.0003 8.06522 12.0464 7.98934 12.0779C7.91347 12.1093 7.83214 12.1255 7.75 12.1255C7.66787 12.1255 7.58654 12.1093 7.51067 12.0779C7.43479 12.0464 7.36586 12.0003 7.30782 11.9422L5.43282 10.0672C5.31554 9.94991 5.24966 9.79085 5.24966 9.625C5.24966 9.45915 5.31554 9.30009 5.43282 9.18281C5.55009 9.06554 5.70915 8.99965 5.875 8.99965C6.04086 8.99965 6.19992 9.06554 6.31719 9.18281L7.75 10.6164L11.6828 6.68281C11.7409 6.62474 11.8098 6.57868 11.8857 6.54725C11.9616 6.51583 12.0429 6.49965 12.125 6.49965C12.2071 6.49965 12.2884 6.51583 12.3643 6.54725C12.4402 6.57868 12.5091 6.62474 12.5672 6.68281C12.6253 6.74088 12.6713 6.80982 12.7027 6.88569C12.7342 6.96156 12.7504 7.04288 12.7504 7.125C12.7504 7.20712 12.7342 7.28844 12.7027 7.36431C12.6713 7.44018 12.6253 7.50912 12.5672 7.56719Z"
        fill="#166534"
      />
    </svg>
  ),
};

export const Alert: React.FC<AlertProps> = ({
  variant = "info",
  message,
  className = "",
}) => {
  return (
    <div
      className={cn(
        "flex items-center gap-2 w-full p-2 rounded-md",
        ALERT_VARIANTS[variant],
        className,
      )}
    >
      {ALERT_ICONS[variant]}
      <span className="text-sm">{message}</span>
    </div>
  );
};

Alert.displayName = "Alert";

export default Alert;
