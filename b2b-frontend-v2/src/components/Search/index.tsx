import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { SearchProps } from "./types";
import { SearchFormData, searchSchema } from "./validation";
import TextInput from "../TextInput";
import React from "react";

const Search: React.FC<SearchProps> = ({
  onSearch,
  searchPlaceholder = "Search...",
  className = "",
}) => {
  const methods = useForm<SearchFormData>({
    resolver: zodResolver(searchSchema),
    defaultValues: {
      search: "",
    },
  });

  const handleSearch = (data: SearchFormData) => {
    if (data.search) {
      onSearch?.(data.search);
    }
  };

  return (
    <FormProvider {...methods}>
      <form
        onChange={methods.handleSubmit(handleSearch)}
        className={`relative max-w-[320px] w-full ${className}`}
      >
        <TextInput
          name="search"
          placeholder={searchPlaceholder}
          className="bg-white border border-neutral-200 rounded-lg"
        />
      </form>
    </FormProvider>
  );
};

export default Search;
