import React from "react";
import Image from "next/image";
import { LogoProps } from "./types";
import { cn } from "@/utils/class-merge";

const Logo = ({
  compact = true,
  imageSize = 32,
  className = "",
}: LogoProps) => {
  if (compact) {
    return (
      <Image
        src="/assets/images/logo/logo-96x96.png"
        alt="Navicater"
        width={imageSize}
        height={imageSize}
        className={className}
      />
    );
  }

  return (
    <div className="flex items-center gap-1">
      <Image
        src="/assets/images/logo/logo-96x96.png"
        alt="Navicater"
        width={imageSize}
        height={imageSize}
        className={className}
      />
      <div
        className={cn(
          "uppercase tracking-widest text-2xl text-primary-900 font-manrope",
        )}
      >
        Navi<span className="font-extrabold">Cater</span>
      </div>
    </div>
  );
};

Logo.displayName = "Logo";

export default Logo;
