import { DialogSize } from "./types";

export const DIALOG_SIZES: Record<DialogSize, string> = {
  sm: "max-w-sm",
  md: "max-w-md",
  lg: "max-w-lg",
  xl: "max-w-xl",
  "2xl": "max-w-2xl",
};

export const OVERLAY_STYLES =
  "fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm";

export const CONTENT_BASE_STYLES =
  "fixed inset-0 m-auto w-fit h-fit rounded-lg shadow-xl";

export const HEADER_STYLES = "flex items-start justify-between";

export const FOOTER_STYLES = "flex justify-end gap-3";

export const CLOSE_BUTTON_STYLES =
  "rounded-full p-1 hover:bg-neutral-100 transition-colors";
