import { HTMLAttributes, ReactNode } from "react";

export type DialogSize = "sm" | "md" | "lg" | "xl" | "2xl";

export interface DialogPropsI
  extends Omit<HTMLAttributes<HTMLDivElement>, "title"> {
  isOpen: boolean;
  onClose: () => void;
  title?: ReactNode;
  description?: ReactNode;
  children?: ReactNode;
  size?: DialogSize;
  showClose?: boolean;
  overlayClassName?: string;
  contentClassName?: string;
}

export interface DialogHeaderPropsI extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
  className?: string;
}

export interface DialogFooterPropsI extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
  className?: string;
}
