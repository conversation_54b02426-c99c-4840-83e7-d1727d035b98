import type React from "react";
import { useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import type {
  DialogFooterPropsI,
  DialogHeaderPropsI,
  DialogPropsI,
} from "./types";
import {
  CLOSE_BUTTON_STYLES,
  CONTENT_BASE_STYLES,
  DIALOG_SIZES,
  FOOTER_STYLES,
  HEADER_STYLES,
  OVERLAY_STYLES,
} from "./consts";
import { cn } from "@/utils/class-merge";

export const Dialog: React.FC<DialogPropsI> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  size = "md",
  showClose = true,
  overlayClassName = "",
  contentClassName = "",
  ...props
}) => {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") onClose();
    };
    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose]);

  useEffect(() => {
    if (!isOpen) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (contentRef.current?.contains(event.target as Node)) return;
      onClose();
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const dialogContent = (
    <div
      className={cn(
        OVERLAY_STYLES,
        "z-[9999] transition-opacity duration-200",
        isOpen ? "opacity-100" : "opacity-0",
        overlayClassName,
      )}
    >
      <div
        ref={contentRef}
        className={cn(
          CONTENT_BASE_STYLES,
          DIALOG_SIZES[size],
          "bg-white p-4",
          "transition-all duration-200",
          "max-h-[90vh] w-[90vw] sm:w-auto overflow-auto",
          isOpen ? "opacity-100 scale-100" : "opacity-0 scale-95",
          contentClassName,
        )}
        {...props}
      >
        {(title || showClose) && (
          <div className={cn(HEADER_STYLES, "mb-4")}>
            <div className="flex-grow">
              {title && (
                <h2 className="text-2xl font-medium text-neutral-950">
                  {title}
                </h2>
              )}
              {description && (
                <div className="mt-1 text-sm text-neutral-400">
                  {description}
                </div>
              )}
            </div>
            {showClose && (
              <button
                onClick={onClose}
                className={cn(CLOSE_BUTTON_STYLES, "-mr-3 -mt-3 ml-auto")}
                aria-label="Close dialog"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-neutral-400 hover:text-neutral-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            )}
          </div>
        )}
        {children}
      </div>
    </div>
  );

  return createPortal(dialogContent, document.body);
};

export const DialogHeader: React.FC<DialogHeaderPropsI> = ({
  children,
  className = "",
  ...props
}) => (
  <div className={cn(HEADER_STYLES, className)} {...props}>
    {children}
  </div>
);

export const DialogFooter: React.FC<DialogFooterPropsI> = ({
  children,
  className = "",
  ...props
}) => (
  <div className={cn(FOOTER_STYLES, className)} {...props}>
    {children}
  </div>
);

export default Dialog;
