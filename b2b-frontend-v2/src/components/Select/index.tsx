"use client";

import React, { useEffect, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";
import { NumberNullI } from "@/types/common/data";
import { DropdownPropsI } from "./types";
import { cn } from "@/utils/class-merge";

const ChevronIcon = () => (
  <svg
    className="transition-transform duration-200"
    xmlns="http://www.w3.org/2000/svg"
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
  >
    <path
      d="M18.3618 8.73642L11.4868 15.6114C11.4229 15.6753 11.3471 15.7261 11.2637 15.7606C11.1802 15.7952 11.0907 15.8131 11.0004 15.8131C10.91 15.8131 10.8206 15.7952 10.7371 15.7606C10.6536 15.7261 10.5778 15.6753 10.514 15.6114L3.63898 8.73642C3.50997 8.60742 3.4375 8.43245 3.4375 8.25002C3.4375 8.06758 3.50997 7.89261 3.63898 7.76361C3.76798 7.63461 3.94294 7.56213 4.12538 7.56213C4.30782 7.56213 4.48279 7.63461 4.61179 7.76361L11.0004 14.1531L17.389 7.76361C17.4529 7.69973 17.5287 7.64906 17.6121 7.6145C17.6956 7.57993 17.785 7.56213 17.8754 7.56213C17.9657 7.56213 18.0552 7.57993 18.1386 7.6145C18.2221 7.64906 18.2979 7.69973 18.3618 7.76361C18.4257 7.82749 18.4763 7.90332 18.5109 7.98677C18.5455 8.07023 18.5633 8.15968 18.5633 8.25002C18.5633 8.34035 18.5455 8.4298 18.5109 8.51326C18.4763 8.59671 18.4257 8.67255 18.3618 8.73642Z"
      fill="currentColor"
    />
  </svg>
);

const Select = ({
  handleChange,
  options,
  name,
  label,
  disabled,
  selected,
  optional = false,
  placeholder,
  errorText,
}: DropdownPropsI) => {
  const [hoverIndex, setHoverIndex] = useState<NumberNullI>(-1);
  const [open, setOpen] = useState<boolean>(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const formContext = useFormContext();

  const toggleDropdown = () => !disabled && setOpen((prev) => !prev);

  const handleClick = (id: string) => {
    if (!disabled) {
      handleChange(id === "select" ? null : id);
      setOpen(false);
    }
  };

  const updateHoverIndex = (index: NumberNullI) =>
    !disabled && setHoverIndex(index);

  const error =
    name && formContext
      ? (formContext.formState.errors[name]?.message as string)
      : errorText;

  useEffect(() => {
    setHoverIndex(options.findIndex((option) => option.id === selected?.id));
  }, [options, selected]);

  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleOutsideClick);
    return () => document.removeEventListener("mousedown", handleOutsideClick);
  }, []);

  return (
    <div className="flex flex-1 flex-col">
      {label && (
        <label
          className={cn(
            "text-sm font-medium mb-2",
            disabled ? "text-neutral-300" : "text-neutral-950",
          )}
          htmlFor={name}
        >
          {label}{" "}
          {optional && (
            <span
              className={cn(disabled ? "text-neutral-300" : "text-neutral-500")}
            >
              (optional)
            </span>
          )}
        </label>
      )}
      <div ref={dropdownRef} className="relative">
        <button
          onClick={toggleDropdown}
          disabled={disabled}
          className={cn(
            "w-full flex justify-between items-center border rounded-lg p-2",
            disabled
              ? "border-neutral-300 cursor-not-allowed"
              : error
                ? "border-red-500 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-200"
                : "border-neutral-400 cursor-pointer",
          )}
        >
          <span
            className={cn(
              "truncate",
              selected ? "text-neutral-950" : "text-neutral-500",
            )}
          >
            {selected ? selected.title : placeholder}
          </span>
          <div
            className={cn(
              "transition-transform duration-200",
              open && "transform rotate-180",
              disabled
                ? "text-neutral-300"
                : error
                  ? "text-red-500"
                  : "text-gray-400",
            )}
          >
            <ChevronIcon />
          </div>
        </button>
        {open && !disabled && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-neutral-300 rounded-md shadow-lg max-h-60 overflow-auto">
            <ul className="py-1">
              <li
                className={cn(
                  "px-3 py-2 text-neutral-500 hover:bg-neutral-100 cursor-pointer transition-colors duration-150",
                )}
                onClick={() => handleClick("select")}
                onMouseEnter={() => updateHoverIndex(-1)}
                onMouseLeave={() => updateHoverIndex(null)}
              >
                {placeholder}
              </li>
              {options.map((optionItem, index) => (
                <li
                  className={cn(
                    "px-3 py-2 text-neutral-950 hover:bg-neutral-100 cursor-pointer transition-colors duration-150",
                    hoverIndex === index &&
                      optionItem.id === selected?.id &&
                      "bg-neutral-100",
                  )}
                  key={optionItem.id}
                  onClick={() => handleClick(optionItem.id)}
                  onMouseEnter={() => updateHoverIndex(index)}
                  onMouseLeave={() => updateHoverIndex(null)}
                >
                  {optionItem.title}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      {error && <div className="mt-1 text-sm text-red-500">{error}</div>}
    </div>
  );
};

Select.displayName = "Select";

export default Select;
