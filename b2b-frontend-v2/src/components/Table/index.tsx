"use client";

import { cn } from "@/utils/class-merge";
import { TableProps } from "./types";
import Checkbox from "../Checkbox";
import Search from "../Search";
import Pagination from "../Pagination";
import { useState } from "react";

const Table = <T extends { id: string }>({
  columns,
  data,
  selectable = false,
  selectedRows = [],
  onRowSelect,
  onSelectAll,
  rowKey = "id",
  className,
  searchable = false,
  searchPlaceholder = "Search",
  onSearch,
  actions,
}: TableProps<T>) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const handleSelectRow = (id: string, selected: boolean) => {
    if (onRowSelect) {
      const newSelectedRows = selected
        ? [...selectedRows, id]
        : selectedRows.filter((rowId) => rowId !== id);
      onRowSelect(newSelectedRows);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    onSelectAll?.(checked);
  };

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = data.slice(startIndex, endIndex);
  const allSelected =
    paginatedData.length > 0 &&
    paginatedData.every((row) =>
      selectedRows.includes(String(row[rowKey as keyof T])),
    );

  return (
    <div className={cn("flex flex-col rounded-lg bg-white", className)}>
      {(searchable || actions) && (
        <div className="flex items-center justify-between p-4 border-b border-neutral-200">
          {searchable && (
            <Search searchPlaceholder={searchPlaceholder} onSearch={onSearch} />
          )}
          {actions && <div className="flex items-center gap-2">{actions}</div>}
        </div>
      )}
      <div className="max-h-[700px] overflow-auto">
        <table className="w-full">
          <thead className="sticky top-0 z-10 bg-neutral-50">
            <tr>
              {selectable && (
                <th className="w-[48px] p-4 border-y border-neutral-200">
                  <Checkbox
                    id="select-all"
                    name="select-all"
                    checked={allSelected}
                    disabled={false}
                    onChange={handleSelectAll}
                    className="flex items-center justify-center"
                    label=""
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    "p-4 text-left text-xs font-medium text-neutral-600 border-y border-neutral-200",
                    column.width && `w-[${column.width}]`,
                    "whitespace-nowrap uppercase tracking-wide",
                  )}
                  title={column.title}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((row, idx) => {
              const id = String(row[rowKey as keyof T]);
              const isSelected = selectedRows.includes(id);

              return (
                <tr
                  key={id}
                  className={cn(
                    "border-b border-neutral-200",
                    idx % 2 === 0 ? "bg-white" : "bg-neutral-50/50",
                    "hover:bg-neutral-100/50 transition-colors",
                  )}
                >
                  {selectable && (
                    <td className="w-[48px] p-4">
                      <Checkbox
                        id={`checkbox-${id}`}
                        name={`checkbox-${id}`}
                        checked={isSelected}
                        disabled={false}
                        onChange={(checked) => handleSelectRow(id, checked)}
                        className="flex items-center justify-center"
                        label=""
                      />
                    </td>
                  )}
                  {columns.map((column) => (
                    <td
                      key={`${id}-${column.key}`}
                      className={cn(
                        "p-4",
                        column.key === "status" && "w-[100px]",
                        column.key === "actions" && "w-[48px]",
                      )}
                    >
                      {column.render ? (
                        column.render(row)
                      ) : (
                        <div className="text-sm text-neutral-900">
                          {String(row[column.key as keyof T])}
                        </div>
                      )}
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      <Pagination
        totalItems={data.length}
        itemsPerPage={itemsPerPage}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        onItemsPerPageChange={setItemsPerPage}
        className="border-t border-neutral-200"
      />
    </div>
  );
};

export default Table;
