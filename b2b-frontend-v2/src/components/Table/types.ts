import { StringUndefinedI } from "@/types/common/data";
import React from "react";

export interface TableColumn<T> {
  key: string;
  title: string;
  width?: string;
  render?: (row: T) => React.ReactNode;
}

export interface TableProps<T> {
  columns: TableColumn<T>[];
  data: T[];
  selectable?: boolean;
  selectedRows?: string[];
  onRowSelect?: (selectedIds: string[]) => void;
  onSelectAll?: (selected: boolean) => void;
  rowKey?: string;
  className?: string;
  searchable?: boolean;
  searchPlaceholder?: string;
  onSearch?: (value: StringUndefinedI) => void;
  actions?: React.ReactNode;
}
