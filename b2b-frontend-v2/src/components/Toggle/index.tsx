import { forwardRef } from "react";
import { ToggleProps } from "./types";
import { cn } from "@/utils/class-merge";

const Toggle = forwardRef<HTMLInputElement, ToggleProps>(
  (
    { id, name, checked = false, disabled = false, onChange, className, label },
    ref,
  ) => {
    return (
      <div className={className}>
        <label
          htmlFor={id}
          className={cn("relative flex items-center gap-2 cursor-pointer")}
        >
          <input
            type="checkbox"
            role="switch"
            id={id}
            name={name}
            checked={checked}
            disabled={disabled}
            onChange={(e) => onChange?.(e.target.checked)}
            className="sr-only peer"
            ref={ref}
          />
          <div
            className={cn(
              "relative w-5 h-5 rounded-full",
              "transition-colors duration-200",
              checked ? "bg-primary-600" : "bg-neutral-400",
              disabled && "opacity-50 cursor-not-allowed",
            )}
          >
            <div
              className={cn(
                "absolute left-1 top-1",
                "w-4 h-4 bg-white rounded-full",
                "transition-transform duration-200",
                checked ? "translate-x-5" : "translate-x-0",
              )}
            />
          </div>
          {label && (
            <span className={cn("text-sm", disabled && "opacity-50")}>
              {label}
            </span>
          )}
        </label>
      </div>
    );
  },
);

Toggle.displayName = "Toggle";

export default Toggle;
