import { CardProps, CardRowProps } from "./types";
import { cn } from "@/utils/class-merge";

const CardRow = ({ label, value, className }: CardRowProps) => {
  return (
    <div className={cn("flex justify-between items-center py-2", className)}>
      <span className="text-neutral-600 text-base font-normal">{label}</span>
      <span className="text-neutral-900 text-base font-normal">{value}</span>
    </div>
  );
};

const Card = ({ fields, className, children }: CardProps) => {
  return (
    <div
      className={cn(
        "bg-white border border-neutral-300 rounded-xl p-6",
        className,
      )}
    >
      <div className="flex flex-col gap-2">
        {fields.map((field, index) => (
          <CardRow key={index} label={field.label} value={field.value} />
        ))}
        {children}
      </div>
    </div>
  );
};

export default Card;
