import { forwardRef } from "react";
import { CheckboxProps } from "./types";
import { cn } from "@/utils/class-merge";

const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  (
    { id, name, checked = false, disabled = false, onChange, className, label },
    ref,
  ) => {
    return (
      <div className={className}>
        <label
          htmlFor={id}
          className="relative flex items-center gap-2 cursor-pointer"
        >
          <input
            type="checkbox"
            id={id}
            name={name}
            checked={checked}
            disabled={disabled}
            onChange={(e) => onChange?.(e.target.checked)}
            className="sr-only peer"
            ref={ref}
            aria-label={label || name}
          />
          <div
            className={cn(
              "w-5 h-5 border rounded",
              checked
                ? "bg-primary-600 border-none"
                : "bg-white border-neutral-600 border-2",
              disabled && "opacity-50 cursor-not-allowed",
            )}
          >
            {checked && (
              <svg
                viewBox="0 0 14 14"
                fill="none"
                className="w-full h-full text-white"
              >
                <path
                  d="M11.6666 3.5L5.24992 9.91667L2.33325 7"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </div>
          {label && (
            <span className={cn("text-sm", disabled && "opacity-50")}>
              {label}
            </span>
          )}
        </label>
      </div>
    );
  },
);

Checkbox.displayName = "Checkbox";

export default Checkbox;
