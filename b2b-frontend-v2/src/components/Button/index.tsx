import React from "react";
import { ButtonPropsI } from "./types";
import { BUTTON_VARIANTS } from "./consts";
import { cn } from "@/utils/class-merge";
import Spinner from "../Spinner";

const Button: React.FC<ButtonPropsI> = ({
  children,
  variant = "primary",
  className = "",
  isLoading = false,
  spinnerProps,
  disabled,
  ...props
}) => {
  const variantClasses = BUTTON_VARIANTS[variant];

  return (
    <button
      className={cn(
        "relative inline-flex w-full items-center justify-center px-6 py-3 text-base font-medium rounded-full transition-all duration-200 ease-in-out",
        variantClasses,
        isLoading && "cursor-not-allowed",
        className,
      )}
      disabled={isLoading || disabled}
      {...props}
    >
      <span className={cn("flex items-center gap-2", isLoading && "opacity-0")}>
        {children}
      </span>{" "}
      {isLoading && (
        <span className="absolute inset-0 flex items-center justify-center">
          <Spinner
            size={24}
            colorClass={
              variant === "primary" ? "text-white" : "text-primary-600"
            }
            {...spinnerProps}
          />
        </span>
      )}
    </button>
  );
};

export default Button;
