{"name": "web-portal-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "prepare": "npx husky", "lint": "next lint", "lint:fix": "eslint . --fix", "format:fix": "prettier --write .", "format": "prettier --check .", "setup:hooks": "sh ./scripts/setup-hooks.sh"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@xyflow/react": "^12.4.2", "clsx": "^2.1.1", "next": "15.1.4", "next-intl": "^3.26.3", "papaparse": "^5.5.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "rtl-detect": "^1.1.2", "tailwind-merge": "^2.6.0", "zod": "^3.24.1"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@commitlint/format": "^19.5.0", "@commitlint/types": "^19.5.0", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/papaparse": "^5.3.15", "@types/react": "^19", "@types/react-dom": "^19", "@types/rtl-detect": "^1.0.3", "conventional-changelog-atom": "^5.0.0", "eslint": "^9.18.0", "eslint-config-next": "15.1.4", "husky": "^9.1.7", "lint-staged": "^15.3.0", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml}": ["prettier --write"]}}