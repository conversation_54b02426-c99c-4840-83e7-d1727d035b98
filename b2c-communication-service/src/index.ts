import '@utils/common/loadEnv';
import Fastify, { FastifyInstance } from 'fastify';
import fastifyHelmet from '@fastify/helmet';
import fastifyCors from '@fastify/cors';
import fastifySwagger from '@fastify/swagger';
import fastifySwaggerUi from '@fastify/swagger-ui';
import fastifyHealthcheck from 'fastify-healthcheck';
import routes from 'routes/index';
import { z } from 'zod';
import { ZodTypeProvider } from 'fastify-type-provider-zod';
import { ENV } from '@consts/common/env';
import { errorMiddleware } from 'middlewares/errorMiddleware';
import KafkaService from '@services/kafka';
import KafkaModule from '@modules/kafka';

const PORT = ENV.COMMUNICATION_PORT;
declare module 'fastify' {
  interface FastifyRequest {
    startTime: number;
  }
}
const setupServer = async (): Promise<FastifyInstance> => {
  const fastify: FastifyInstance = Fastify({
    logger: ENV.NODE_ENV !== 'production',
    trustProxy: true,
    ignoreTrailingSlash: true,
  }).withTypeProvider<ZodTypeProvider>();
  await fastify.register(fastifySwagger, {
    swagger: {
      info: {
        title: 'B2C Social Network API',
        description: 'Social Network API',
        version: '1.0.0',
      },
      host: `localhost:${PORT}`,
      schemes: ['http', 'https'],
      consumes: ['application/json'],
      produces: ['application/json'],

      securityDefinitions: {
        bearerAuth: {
          type: 'apiKey',
          name: 'Authorization',
          in: 'header',
        },
      },
    },
  });

  await fastify.register(fastifySwaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: true,
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
  });

  fastify.setValidatorCompiler((opts) => (data) => {
    const { schema } = opts;
    if (schema instanceof z.ZodType) {
      const result = schema.safeParse(data);
      if (!result.success) {
        return {
          error: new Error('Validation failed'),
          value: {
            message: 'Validation failed',
            issues: result.error.errors.map((err) => ({
              message: err.message,
            })),
          },
        };
      }
      return { value: result.data };
    }
    return { value: data };
  });
  fastify
    .register(routes, {})
    .register(fastifyHelmet)
    .register(fastifyCors, {
      origin: '*',
      credentials: true,
    })
    .register(fastifyHealthcheck, {
      healthcheckUrl: '/health',
      exposeUptime: true,
      logLevel: 'info',
      underPressureOptions: {
        maxEventLoopDelay: 1000,
        maxHeapUsedBytes: 100000000,
        maxRssBytes: 100000000,
        maxEventLoopUtilization: 0.98,
      },
    })
    .setErrorHandler(errorMiddleware);

  const gracefulShutdown = async (_signal: string) => {
    await Promise.allSettled([fastify.close(), KafkaService.instance.stop()]);
    process.exit();
  };

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  return fastify;
};
const setupServices = async () => {
  await KafkaService.instance.start(KafkaModule.crudMessage);
};
const startServer = async () => {
  const fastify = await setupServer();
  setupServices();
  fastify.listen({ port: PORT, host: '0.0.0.0' }, (err, address) => {
    if (err) {
      process.exit();
    }
    console.info(`B2C Communication Service running on ${address}`);
  });
};

startServer();
