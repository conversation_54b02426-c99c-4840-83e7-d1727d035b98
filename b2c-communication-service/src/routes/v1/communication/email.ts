import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import CommunicationModule from '@modules/communication';
import { CommunicationEmailSendOneSchema, CommunicationEmailVerifySchema } from '@schemas/communication/email';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const emailRoutes = (fastify: FastifyInstance): void => {
  fastify.post(
    '/communication/api/v1/communication/email/single',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { data, error } = CommunicationEmailSendOneSchema.safeParse(request.body);
      if (error) {
        throw new AppError('CMEML003');
      }
      await CommunicationModule.CommunicationEmailModule.sendOne(data);
      reply.status(HttpStatus.CREATED);
    },
  );
  fastify.patch(
    '/communication/api/v1/communication/email/single',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { data, error } = CommunicationEmailVerifySchema.safeParse(request.body);
      if (error) {
        throw new AppError('CMEML003');
      }
      await CommunicationModule.CommunicationEmailModule.verify(data);
      reply.status(HttpStatus.OK);
    },
  );
};

export default emailRoutes;
