import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import NotificationModule from '@modules/notification';
import { NotificationFetchManySchema, NotificationUpdateReadSchema } from '@schemas/notification/notification';
import { FastifyInstance, FastifyReply } from 'fastify';

const coreNotificationRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/communication/api/v1/notifications', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = NotificationFetchManySchema.safeParse(request.query);
    if (error) {
      throw new AppError('NF010');
    }
    const result = await NotificationModule.CoreNotificationModule.fetchMany(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.patch(
    '/communication/api/v1/notifications/read',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data, error } = NotificationUpdateReadSchema.safeParse(request.body);
      if (error) {
        throw new AppError('NF011');
      }
      const result = await NotificationModule.CoreNotificationModule.updateReads(request, data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default coreNotificationRoutes;
