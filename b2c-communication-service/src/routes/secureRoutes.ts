import { FastifyInstance } from 'fastify';
import { authTokenMiddleware } from 'middlewares/authMiddleware';
import notificationRoutes from './v1/notification';
import communicationRoutes from './v1/communication';

const secureRoutes = (fastify: FastifyInstance): void => {
  fastify.addHook('preHandler', authTokenMiddleware);
  fastify.register(communicationRoutes);
  fastify.register(notificationRoutes);
};

export default secureRoutes;
