import type { KafkaTopicI } from '@consts/kafka';
import type { ObjUnknownI } from '@interfaces/common/data';
import CommunicationModule from '@modules/communication';
import SessionModule from '@modules/session';
const KafkaModule = {
  crudMessage: async ({ data, topic }: ObjUnknownI): Promise<void> => {
    switch (topic as KafkaTopicI) {
      case 'communication_topic': {
        await CommunicationModule.CoreCommunicationModule.upsert(data);
        break;
      }
      case 'session_topic': {
        await SessionModule.delsertSession(data);
        break;
      }
      default:
        break;
    }
  },
};
export default KafkaModule;
