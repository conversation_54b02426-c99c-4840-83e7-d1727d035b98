import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import { Session } from '@prisma/mongodb';
import { UUIDI } from '@schemas/common/common';
import { SessionDelsertMessageI, SessionCreateOneSchema, SessionUpdateOneSchema } from '@schemas/session/session';

const SessionModule = {
  fetchOne: async (profileId: UUIDI): Promise<Pick<Session, 'sessionId' | 'deviceToken'>> => {
    const sessionResult = await prismaMG.session.findMany({
      select: { sessionId: true, deviceToken: true },
      where: {
        profileId,
        isActive: true,
      },
    });
    if (!sessionResult?.length) {
      throw new AppError('SESS008');
    }
    return sessionResult[0];
  },
  fetchMany: async (profileId: UUIDI): Promise<Pick<Session, 'sessionId' | 'deviceToken'>[]> => {
    const sessionResult = await prismaMG.session.findMany({
      select: { sessionId: true, deviceToken: true },
      where: {
        profileId,
        isActive: true,
      },
    });
    if (!sessionResult?.length) {
      throw new AppError('SESS008');
    }
    return sessionResult;
  },
  delsertSession: async (params: SessionDelsertMessageI) => {
    switch (params.opr) {
      case 'CREATE': {
        const { error, data } = SessionCreateOneSchema.safeParse(params);
        if (error) {
          throw new AppError('SESS005');
        }
        const existingSessionResult = await prismaMG.session.findUnique({
          select: { id: true },
          where: { sessionId: data.sessionId, profileId: data.profileId },
        });
        if (existingSessionResult) {
          throw new AppError('SESS004');
        }
        const sessionResult = await prismaMG.session.create({
          data: { profileId: data.profileId, sessionId: data.sessionId, deviceToken: data.deviceToken },
        });
        if (!sessionResult) {
          throw new AppError('SESS005');
        }
        break;
      }
      case 'DELETE': {
        const { error, data } = await SessionUpdateOneSchema.safeParse(params);
        if (error) {
          throw new AppError('SESS006');
        }
        const existingSessionResult = await prismaMG.session.findUnique({
          select: { id: true },
          where: { sessionId: data.sessionId },
        });
        if (!existingSessionResult) {
          throw new AppError('SESS008');
        }
        const sessionResult = await prismaMG.session.delete({
          select: { id: true },
          where: {
            id: existingSessionResult.id,
          },
        });
        if (!sessionResult) {
          throw new AppError('SESS007');
        }
        break;
      }
      default: {
        throw new AppError('SESS003');
      }
    }
  },
};

export default SessionModule;
