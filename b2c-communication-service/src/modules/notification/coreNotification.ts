import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import { CommunicationNotificationI, CommunicationNotificationSchema } from '@schemas/common/communication';
import {
  NotificationCommentSchema,
  NotificationFetchManyI,
  NotificationFollowerSchema,
  NotificationLikeSchema,
  NotificationReplySchema,
  NotificationRequestAcceptedSchema,
  NotificationRequestReceivedSchema,
  NotificationUpdateReadI,
} from '@schemas/notification/notification';
import NotificationTemplateModule from './notificationTemplate';
import { toAny } from '@utils/data/object';
import VendorModule from '@modules/vendor';
import SessionModule from '@modules/session';
import Firebase, { FirebaseFCMSendFCMNotificationModReqI } from '@navicater/vendor-firebase';
import { NotificationTypeI } from '@consts/notification/notification';
import { NotificationStatusE, Prisma } from '@prisma/mongodb';
import { FastifyStateI } from '@interfaces/common/declaration';
import { extractVars } from '@utils/notification/notification';
import { RunCommandRawI } from '@interfaces/common/db';
import { NotificationDBResultI, NotificationFetchManyQueryI } from '@interfaces/notification/notification';
import { isNull } from '@utils/data/data';

const CoreNotificationModule = {
  updateReads: async (state: FastifyStateI, { ids }: NotificationUpdateReadI): Promise<void> => {
    const notificationsResult = await prismaMG.notification.updateMany({
      data: {
        isRead: true,
      },
      where: {
        receiverProfileId: state.profileId,
        isRead: false,
        id: {
          in: ids,
        },
      },
    });
    if (!notificationsResult.count) {
      throw new AppError('NF009');
    }
    return;
  },
  fetchMany: async (
    state: FastifyStateI,
    { cursorId, pageSize }: NotificationFetchManyI,
  ): Promise<{ notifications: NotificationFetchManyQueryI[]; nextCursor: string | null }> => {
    const filter = {
      receiverProfileId: state.profileId,
      status: { $in: ['PARTIAL_SUCCESS', 'SUCCESS'] },
    };

    if (!isNull(cursorId)) {
      filter['_id'] = {
        $lt: cursorId,
      };
    }

    const notificationsDBResult = (await prismaMG.$runCommandRaw({
      aggregate: 'Notification',
      pipeline: [
        {
          $match: filter,
        },
        {
          $sort: {
            _id: -1,
          },
        },
        {
          $limit: pageSize,
        },
        {
          $project: {
            _id: 1,
            type: 1,
            data: { $ifNull: ['$variables.data', {}] },
            isRead: 1,
            createdAt: 1,
          },
        },
      ],
      cursor: { batchSize: pageSize },
    })) as unknown as RunCommandRawI<NotificationDBResultI>;

    if (!notificationsDBResult?.cursor?.firstBatch) {
      throw new AppError('NF012');
    }

    const notificationsResult: NotificationFetchManyQueryI[] = [];
    let nextCursor: string | null = null;

    if (notificationsDBResult?.cursor?.firstBatch?.length) {
      notificationsResult.push(
        ...notificationsDBResult.cursor.firstBatch?.map(
          (element) =>
            ({
              id: element._id.$oid,
              type: element.type,
              isRead: element.isRead,
              data: element.data,
              createdAt: new Date(element.createdAt.$timestamp.t * 1000),
            }) as NotificationFetchManyQueryI,
        ),
      );

      if (notificationsResult.length > 0) {
        const lastNotification = notificationsResult[notificationsResult.length - 1];
        nextCursor = lastNotification.id;
      }
    }

    return {
      notifications: notificationsResult,
      nextCursor,
    };
  },
  createOne: async ({
    dataParams,
    firebase,
    receiverProfileId,
    templateContent,
    templateId,
    type,
  }: {
    dataParams;
    firebase: Firebase;
    receiverProfileId;
    templateContent;
    templateId: string;
    type: NotificationTypeI;
  }): Promise<NotificationStatusE> => {
    const rawNotification = JSON.parse(
      Object.keys(dataParams).reduce((acc, key) => {
        return acc.replaceAll(`{{${key}}}`, dataParams[key]);
      }, JSON.stringify(templateContent)),
    );
    const variables = extractVars({
      content: templateContent,
      dataParams,
    });
    const [notificationResult, sessionsResult] = await Promise.all([
      prismaMG.notification.create({
        data: {
          variables,
          templateId,
          receiverProfileId,
          type,
        },
        select: { id: true },
      }),
      SessionModule.fetchMany(receiverProfileId),
    ]);

    if (!notificationResult) {
      throw new AppError('NF008');
    }

    const notificationModReqParams: FirebaseFCMSendFCMNotificationModReqI = {
      notification: {
        title: rawNotification.title,
        body: rawNotification.body,
      },
      data: rawNotification?.data,
    };
    const toUpdateInputParams: Prisma.NotificationUncheckedUpdateInput = {};

    try {
      if (sessionsResult?.length === 1) {
        const sendToDeviceResult = await firebase.sendToDevice({
          notification: notificationModReqParams.notification,
          data: notificationModReqParams?.data,
          token: sessionsResult[0].deviceToken,
          priority: 'high',
        });
        toUpdateInputParams.status = sendToDeviceResult?.messageId ? 'SUCCESS' : 'FAILED';
      } else if (sessionsResult?.length > 0) {
        const sendToDevicesResult = await firebase.sendToDevices({
          notification: notificationModReqParams.notification,
          data: notificationModReqParams?.data,
          tokens: sessionsResult.map((sessionItem) => sessionItem.deviceToken),
          priority: 'high',
        });
        let isSuccess: boolean = false;
        let isFailed: boolean = false;
        for (const sendToDeviceItem of sendToDevicesResult) {
          if (sendToDeviceItem?.error) {
            isFailed = true;
          } else if (sendToDeviceItem?.messageId) {
            isSuccess = true;
          }
          if (isSuccess && isFailed) {
            break;
          }
        }
        toUpdateInputParams.status = isSuccess && isFailed ? 'PARTIAL_SUCCESS' : isSuccess ? 'SUCCESS' : 'FAILED';
      } else {
        toUpdateInputParams.status = 'FAILED';
      }
    } catch (_error) {
      toUpdateInputParams.status = 'FAILED';
    }
    await prismaMG.notification.update({
      data: toUpdateInputParams,
      where: { id: notificationResult.id },
      select: { id: true },
    });
    return toUpdateInputParams.status;
  },
  create: async (paramsP: CommunicationNotificationI) => {
    try {
      const { data: params, error } = CommunicationNotificationSchema.safeParse(paramsP);

      if (error) {
        throw new AppError('NF001');
      }
      let dataParams;
      switch (params.notification.type as NotificationTypeI) {
        case 'MESSAGE': {
          const { error } = NotificationLikeSchema.safeParse(params.notification);
          if (error) {
            throw new AppError('NF002');
          }
          break;
        }
        case 'LIKE': {
          const { data, error } = NotificationLikeSchema.safeParse(params.notification);
          if (error) {
            throw new AppError('NF006');
          }
          dataParams = data;
          break;
        }
        case 'COMMENT': {
          const { data, error } = NotificationCommentSchema.safeParse(params.notification);
          if (error) {
            throw new AppError('NF003');
          }
          dataParams = data;
          break;
        }
        case 'REPLY': {
          const { data, error } = NotificationReplySchema.safeParse(params.notification);
          if (error) {
            throw new AppError('NF004');
          }
          dataParams = data;
          break;
        }

        case 'REQUEST_ACCEPTED': {
          const { data, error } = NotificationRequestAcceptedSchema.safeParse(params.notification);
          if (error) {
            throw new AppError('NF004');
          }
          dataParams = data;
          break;
        }
        case 'REQUEST_RECEIVED': {
          const { data, error } = NotificationRequestReceivedSchema.safeParse(params.notification);
          if (error) {
            throw new AppError('NF004');
          }
          dataParams = data;
          break;
        }
        case 'FOLLOWER': {
          const { data, error } = NotificationFollowerSchema.safeParse(params.notification);
          if (error) {
            throw new AppError('NF004');
          }
          dataParams = data;
          break;
        }
        case 'PUBLIC': {
          const { data, error } = NotificationLikeSchema.safeParse(params.notification);
          if (error) {
            throw new AppError('NF005');
          }
          dataParams = data;
          break;
        }
        default: {
          throw new AppError('NF007');
        }
      }
      const [notificationTemplateResult, { instance }] = await Promise.all([
        NotificationTemplateModule.fetchOne({ type: params.notification.type }),
        VendorModule.CoreVendorModule.getVendor('FIREBASE'),
      ]);
      const firebase = instance as Firebase;
      const templateContent = toAny(notificationTemplateResult.content);

      const profileIds = params?.profileIds ? params?.profileIds : [params?.profileId];
      await Promise.allSettled(
        profileIds.map((receiverProfileId) =>
          CoreNotificationModule.createOne({
            dataParams,
            firebase,
            receiverProfileId,
            templateContent,
            templateId: notificationTemplateResult.id,
            type: params.notification.type,
          }),
        ),
      );
    } catch (_error) {
      //
    }
  },
};
export default CoreNotificationModule;
