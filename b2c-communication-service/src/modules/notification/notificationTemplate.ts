import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import type { Prisma } from '@prisma/mongodb';

const NotificationTemplateModule = {
  fetchOne: async ({ type }: Pick<Prisma.NotificationTemplateWhereInput, 'type'>) => {
    const notificationTemplateResult = await prismaMG.notificationTemplate.findFirst({
      select: {
        id: true,
        content: true,
      },
      where: {
        type,
      },
    });
    if (!notificationTemplateResult) {
      throw new AppError('NFTMP001');
    }
    return notificationTemplateResult;
  },
};

export default NotificationTemplateModule;
