import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import type { CommunicationVerification } from '@prisma/mongodb';
import type { MongoTxnI } from '@interfaces/common/db';
import type {
  CommunicationVerificationCreateOneI,
  CommunicationVerificationDeactivateI,
} from '@schemas/communication/verification';
import { addMsToDate } from '@utils/data/date';

const CommunicationVerificationModule = {
  deactivate: async (
    { mode, type }: CommunicationVerificationDeactivateI,
    txn: MongoTxnI = prismaMG,
    throwsError: boolean = true,
  ): Promise<number> => {
    const verificationResult = await txn.communicationVerification.updateMany({
      data: { status: 'EXPIRED' },
      where: {
        status: { not: 'EXPIRED' },
        Communication: {
          mode,
          type,
        },
      },
    });
    if (throwsError && !verificationResult?.count) {
      throw new AppError('CMVFN001');
    }
    return verificationResult?.count;
  },
  createOne: async (
    { communicationId, data, expiry, profileId }: CommunicationVerificationCreateOneI,
    txn: MongoTxnI = prismaMG,
  ): Promise<Pick<CommunicationVerification, 'id'>> => {
    const expiryDate = addMsToDate({ ms: expiry });
    const verificationResult = await txn.communicationVerification.create({
      data: {
        communicationId,
        data,
        expiryDate,
        profileId,
      },
      select: { id: true },
    });
    if (!verificationResult) {
      throw new AppError('CMVFN002');
    }
    return verificationResult;
  },
};
export default CommunicationVerificationModule;
