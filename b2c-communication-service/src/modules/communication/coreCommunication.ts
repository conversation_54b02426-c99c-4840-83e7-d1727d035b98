import AppError from '@classes/AppError';
import type { CommunicationUpsertMessageI } from '@schemas/common/communication';
import NotificationModule from '../notification/coreNotification';
import { CommunicationCreateOneI } from '@schemas/communication/communication';
import { MongoTxnI } from '@interfaces/common/db';
import { prismaMG } from '@config/db';

const CoreCommunicationModule = {
  createOne: async (
    { mode, type, templateId, profileId, sender, receiver, params }: CommunicationCreateOneI,
    txn: MongoTxnI = prismaMG,
  ) => {
    const communicationResult = await txn.communication.create({
      data: {
        mode,
        type,
        templateId,
        profileId,
        sender,
        receiver,
        params,
      },
    });
    if (!communicationResult) {
      throw new AppError('CMN003');
    }
    return communicationResult;
  },
  upsert: async (params: CommunicationUpsertMessageI) => {
    switch (params?.category) {
      case 'NOTIFICATION': {
        await NotificationModule.create(params);
        break;
      }
      default: {
        throw new AppError('CMN001');
      }
    }
  },
};
export default CoreCommunicationModule;
