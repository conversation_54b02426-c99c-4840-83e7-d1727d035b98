import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import type { CommunicationTemplate } from '@prisma/mongodb';
import type { CommunicationTemplateDataI } from '@interfaces/communication/communicationTemplate';
import type { CommunicationTemplateFetchOneI } from '@schemas/communication/template';

const CommunicationTemplateModule = {
  fetchOne: async ({
    mode,
    type,
  }: CommunicationTemplateFetchOneI): Promise<Pick<CommunicationTemplate, 'id' | 'data'>> => {
    const communicationTemplateResult = await prismaMG.communicationTemplate.findUnique({
      select: {
        id: true,
        data: true,
      },
      where: { mode_type: { mode, type } },
    });
    if (!communicationTemplateResult) {
      throw new AppError('CMTMP001');
    }
    return communicationTemplateResult;
  },
};
export default CommunicationTemplateModule;
