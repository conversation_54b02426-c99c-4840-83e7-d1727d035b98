import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import type { CommunicationEmailSendOneI, CommunicationEmailVerifyI } from '@schemas/communication/email';
import CommunicationTemplateModule from './template1';
import type { CommunicationTemplateEmailDataI } from '@interfaces/communication/communicationTemplate';
import VendorModule from '@modules/vendor';
import Brevo from '@navicater/vendor-brevo';
import AppConfig from '@modules/appConfig';
import type { CommunicationConfigI } from '@interfaces/appConfig/appConfig';
import CommunicationVerificationModule from './verification';
import { generate6DigitOTP } from '@utils/cryptography/verification';
import { CommunicationModeI } from '@consts/communication/common';
import { CommunicationTemplate, Prisma } from '@prisma/mongodb';
import CoreCommunicationModule from './coreCommunication';

const CommunicationEmailModule = {
  sendOne: async ({
    email: receiverEmail,
    name: receiverName,
    profileId,
    type,
  }: CommunicationEmailSendOneI): Promise<void> => {
    try {
      const mode: CommunicationModeI = 'EMAIL';
      switch (type) {
        case 'EMAIL_ID_VERIFICATION':
        case 'EMAIL_ID_PASSWORD_RESET': {
          const [templateResult, { instance: brevo }, configResult] = (await Promise.all([
            CommunicationTemplateModule.fetchOne({
              mode,
              type,
            }),
            VendorModule.CoreVendorModule.getVendor('BREVO'),
            AppConfig.AppConfigModule.fetchById({ module: 'COMMUNICATION', subModule: 'COMMUNICATION' }),
          ])) as [Pick<CommunicationTemplate, 'id' | 'data'>, { instance: Brevo }, CommunicationConfigI];
          const otp = generate6DigitOTP();
          const senderEmail = configResult.verification.email.emailId;
          const senderName = configResult.name;

          const expiry = configResult.verification.email.otp.expiry;
          const [_deactivateResult, _communicationResult, _verificationResult] = await prismaMG.$transaction(
            async (txn) => {
              const [deactivateResult, communicationResult] = await Promise.all([
                CommunicationVerificationModule.deactivate({ mode, type }, txn, false),
                CoreCommunicationModule.createOne(
                  {
                    mode,
                    type,
                    templateId: templateResult.id,
                    profileId,
                    sender: senderEmail,
                    receiver: receiverEmail,
                    params: {
                      otp,
                    },
                  },
                  txn,
                ),
              ]);
              const verificationResult = await CommunicationVerificationModule.createOne({
                communicationId: communicationResult.id,
                profileId,
                data: otp,
                expiry,
              });
              return [deactivateResult, communicationResult, verificationResult];
            },
          );
          const templateData = templateResult.data as CommunicationTemplateEmailDataI;
          const toUpdateCommunication: Prisma.CommunicationUncheckedUpdateInput = {};
          const toUpdateVerification: Prisma.CommunicationVerificationUncheckedUpdateInput = {};
          try {
            await prismaMG.communication.update({
              data: { status: 'PENDING' },
              where: { id: _communicationResult.id },
              select: { id: true, status: true },
            });
            const emailResult = await brevo.sendSingleEmail({
              sender: { email: senderEmail, name: senderName },
              to: { email: receiverEmail, name: receiverName },
              htmlContent: templateData.content,
              params: { otp },
              subject: templateData.subject,
            });
            if (emailResult?.messageId) {
              toUpdateCommunication.status = 'SUCCESS';
            } else {
              toUpdateCommunication.status = 'FAILED';
              toUpdateVerification.status = 'DELIVERY_FAILURE';
            }
          } catch (_error) {
            toUpdateCommunication.status = 'FAILED';
            toUpdateVerification.status = 'DELIVERY_FAILURE';
          }
          const [communicationResult, _updatedVerificationResult] = await Promise.all([
            prismaMG.communication.update({
              data: toUpdateCommunication,
              where: { id: _communicationResult.id },
              select: { id: true, status: true },
            }),
            prismaMG.communicationVerification.update({
              data: toUpdateVerification,
              where: { id: _verificationResult.id },
            }),
          ]);
          if (communicationResult?.status !== 'SUCCESS') {
            throw new AppError('CMEML002');
          }
          return;
        }
        default: {
          throw new AppError('CMEML001');
        }
      }
    } catch (error) {
      throw error;
    }
  },
  verify: async ({ otp, profileId, type }: CommunicationEmailVerifyI) => {
    const mode: CommunicationModeI = 'EMAIL';
    switch (type) {
      case 'EMAIL_ID_VERIFICATION':
      case 'EMAIL_ID_PASSWORD_RESET': {
        const communicationResult = await prismaMG.communication.findFirst({
          where: { mode, type, profileId, status: 'SUCCESS' },
          orderBy: {
            createdAt: 'desc',
          },
        });
        if (!communicationResult) {
          throw new AppError('CMN004');
        }
        const verificationResult = await prismaMG.communicationVerification.findUnique({
          select: { id: true, data: true },
          where: { communicationId: communicationResult.id },
        });
        if (!verificationResult) {
          throw new AppError('CMVFN003');
        }
        if (verificationResult?.data !== otp) {
          throw new AppError('CMVFN004');
        }
        const _updatedVerificationResult = await prismaMG.communicationVerification.update({
          data: {
            status: 'VERIFIED',
          },
          select: { id: true },
          where: { id: verificationResult.id },
        });
        return;
      }
      default: {
        throw new AppError('CMEML001');
      }
    }
  },
};
export default CommunicationEmailModule;
