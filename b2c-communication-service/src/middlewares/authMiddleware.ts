import jwt from 'jsonwebtoken';
import AppError from '@classes/AppError';
import { PlatformE, type PlatformI } from '@consts/app/platform';
import { ENV } from '@consts/common/env';
import type { HeadersI } from '@interfaces/common/api/middleware';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { UUIDSchema, VersionNoSchema } from '@schemas/common/common';
import { FastifyReply } from 'fastify';

export const authMiddleware = (headers: HeadersI): void => {
  if (!headers['x-api-key']) {
    throw new AppError('AUTH011');
  }
  if (headers['x-api-key'] !== ENV.API_KEY) {
    throw new AppError('AUTH012');
  }
  if (!headers['x-platform']) {
    throw new AppError('AUTH013');
  }
  if (!(['android', 'ios', 'web_app'] as PlatformI[]).includes(headers['x-platform'])) {
    throw new AppError('AUTH014');
  }
  if (!headers['x-device-id']) {
    throw new AppError('AUTH015');
  }
  if (!headers['x-version-no']) {
    throw new AppError('AUTH017');
  }
  const { error: versionNoError } = VersionNoSchema.safeParse(headers['x-version-no']);
  if (versionNoError) {
    throw new AppError('AUTH018', versionNoError);
  }
  const { error: platformError } = PlatformE.safeParse(headers['x-platform']);
  if (platformError) {
    throw new AppError('AUTH014');
  }
  const { error: deviceIdError } = UUIDSchema.safeParse(headers['x-device-id']);
  if (deviceIdError) {
    throw new AppError('AUTH016');
  }
  return;
};
export const isMicroserviceRequest = (headers: HeadersI): boolean => {
  if (headers?.['x-api-microservice-key']) {
    if (headers['x-api-microservice-key'] !== ENV.API_MICROSERVICE_KEY) {
      throw new AppError('AUTH012');
    }
    return true;
  }
  return false;
};
export const authNoTokenMiddleware = async (request: FastifyRequestI, _reply: FastifyReply): Promise<void> => {
  const headers: HeadersI = request.headers as HeadersI;
  if (isMicroserviceRequest(headers)) {
    return;
  } else {
    authMiddleware(headers);
  }
};

export const authTokenMiddleware = async (request: FastifyRequestI, _reply: FastifyReply): Promise<void> => {
  try {
    const headers: HeadersI = request.headers as HeadersI;
    if (isMicroserviceRequest(headers)) {
      return;
    } else {
      authMiddleware(headers);
    }
    const authorization = request.headers?.authorization;
    if (!authorization) {
      throw new AppError('AUTH019');
    }
    const token = authorization.split(' ')[1];
    const decodedToken = jwt.verify(token, ENV.JWT_SECRET) as jwt.JwtPayload;

    if (decodedToken?.exp && decodedToken?.exp < Math.floor(Date.now() / 1000)) {
      throw new AppError('AUTH021');
    }
    request.profileId = decodedToken.profileId;
    return;
  } catch (_error) {
    if (_error instanceof jwt.JsonWebTokenError) {
      throw new AppError('AUTH020');
    } else if (_error instanceof jwt.TokenExpiredError) {
      throw new AppError('AUTH021');
    }
    throw new AppError('AUTH020');
  }
};
