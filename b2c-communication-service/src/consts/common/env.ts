import { EnvVariablesI } from '@interfaces/common/env';

export const ENV: EnvVariablesI = {
  API_KEY: process.env.API_KEY as string,
  API_MICROSERVICE_KEY: process.env.API_MICROSERVICE_KEY as string,
  COMMUNICATION_PORT: parseInt(process.env.COMMUNICATION_PORT),
  ENCRYPTION_SECRET_KEY: process.env.ENCRPTION_SECRET_KEY,
  JWT_SECRET: process.env.JWT_SECRET,
  KAFKA_BACKEND_BROKER: process.env.KAFKA_BACKEND_BROKER,
  KAFKA_COMMUNICATION_CLIENT_ID: process.env.KAF<PERSON>_COMMUNICATION_CLIENT_ID,
  KAFKA_COMMUNICATION_GROUP_ID: process.env.KAFKA_COMMUNICATION_GROUP_ID,
  MONGO_DATABASE_URL: process.env.MONGO_DATABASE_URL,
  NODE_ENV: process.env.NODE_ENV as 'development' | 'production' | 'test',
} as EnvVariablesI;
