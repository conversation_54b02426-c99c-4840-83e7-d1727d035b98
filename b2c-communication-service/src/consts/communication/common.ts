import { z } from 'zod';
import { CommunicationTypeE as CommunicationType, CommunicationModeE as CommunicationMode } from '@prisma/mongodb';

export const CommunicationTypeE = z.enum([
  CommunicationType.EMAIL_ID_PASSWORD_RESET,
  CommunicationType.EMAIL_ID_VERIFICATION,
  CommunicationType.PHONE_VERIFICATION,
]);

export type CommunicationTypeI = z.infer<typeof CommunicationTypeE>;

export const CommunicationModeE = z.enum([CommunicationMode.EMAIL, CommunicationMode.SMS]);

export type CommunicationModeI = z.infer<typeof CommunicationModeE>;
