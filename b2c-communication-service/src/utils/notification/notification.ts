import { VariableR } from '@consts/common/regex/regex';
import { ObjStrI } from '@interfaces/common/data';

export const extractVars = ({
  content,
  dataParams,
}: {
  content;
  dataParams;
}): { title: ObjStrI; body: ObjStrI; data: ObjStrI } => {
  const extractVarsFromObj = (obj) => {
    const stringifiedObj = JSON.stringify(obj);

    const matches = stringifiedObj.matchAll(VariableR);

    const variables: ObjStrI = {};
    for (const match of matches) {
      variables[match[1]] = dataParams[match[1]];
    }
    return variables;
  };

  const title: ObjStrI = extractVarsFromObj(content.title);
  const body: ObjStrI = extractVarsFromObj(content.body);
  const data: ObjStrI = extractVarsFromObj(content.data);
  return { title, body, data };
};
