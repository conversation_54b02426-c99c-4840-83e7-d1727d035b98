import { ENV } from '@consts/common/env';
import { KafkaTopicI } from '@consts/kafka';
import { delay } from '@utils/fn/process';
import { Kafka, Consumer } from 'kafkajs';

class KafkaService {
  public static readonly instance: KafkaService = new KafkaService();
  public consumer: Consumer;
  private kafka: Kafka;
  private isSubscribed: boolean = false;
  private isConsumerConnected: boolean = false;
  private consumerConnectAttempts: number = 0;
  private readonly maxAttempts: number = 3;
  private readonly reconnectDelay: number = 5000;
  private readonly topics: KafkaTopicI[] = ['communication_topic', 'session_topic'];

  private constructor() {
    if (!this.kafka) {
      this.kafka = new Kafka({
        clientId: ENV.KAFKA_COMMUNICATION_CLIENT_ID,
        brokers: [ENV.KAFKA_BACKEND_BROKER],
        connectionTimeout: 10000,
        requestTimeout: 30000,
        retry: {
          initialRetryTime: 300,
          maxRetryTime: 30000,
          retries: 10,
        },
      });
      this.consumer = this.kafka.consumer({
        groupId: Date.now().toString(),
        sessionTimeout: 30000,
        heartbeatInterval: 3000,
      });
      this.setupEventListeners();
    }
  }

  private setupEventListeners = async (): Promise<void> => {
    this.consumer.on('consumer.disconnect', () => {
      this.isConsumerConnected = false;
      this.connectConsumer();
    });
    this.consumer.on('consumer.crash', () => {
      this.isConsumerConnected = false;
      this.connectConsumer();
    });
    this.consumer.on('consumer.connect', () => {
      this.isConsumerConnected = true;
      this.consumerConnectAttempts = 0;
    });
  };

  private subscribeToTopics = async (): Promise<void> => {
    if (!this.isSubscribed) {
      await this.consumer.subscribe({ topics: this.topics });
      this.isSubscribed = true;
    }
  };

  private connectConsumer = async (): Promise<void> => {
    if (this.isConsumerConnected || this.consumerConnectAttempts > this.maxAttempts) {
      return;
    }
    try {
      await this.consumer.connect();
      await this.subscribeToTopics();
      this.isConsumerConnected = true;
    } catch (_error) {
      ++this.consumerConnectAttempts;
      await delay(this.reconnectDelay);
      await this.connectConsumer();
    }
    return;
  };

  private disconnectConsumer = async (): Promise<void> => {
    if (this.isConsumerConnected) {
      try {
        await this.consumer.disconnect();
        this.isConsumerConnected = false;
      } catch (_error) {
        //
      }
    }
  };

  public start = async (callbackFn): Promise<void> => {
    await this.connectConsumer();
    this.run(callbackFn);
  };

  private run = async (callbackFn): Promise<void> => {
    await this.consumer.run({
      eachMessage: async ({ message, topic }) => {
        try {
          console.log('1 cs kafka eachMessage');
          const data = JSON.parse(message.value.toString());
          console.log(data);

          await callbackFn({ data, topic });
        } catch (_error) {
          console.log('1 cs kafka message');
          console.log(_error);
        }
      },
    });
  };

  public stop = async (): Promise<void> => {
    await this.disconnectConsumer();
  };
}

export default KafkaService;
