import { ObjUnknownI } from '@interfaces/common/data';
import Brevo, { BrevoConfigI } from '@navicater/vendor-brevo';
import Firebase, { type FirebaseConfigI } from '@navicater/vendor-firebase';
import type { Vendor, VendorNamesE } from '@prisma/mongodb';
export type VendorI = Pick<Vendor, 'name' | 'type'> & {
  config: ObjUnknownI;
};
export type GetVendorResultI = { name: VendorNamesE } & {
  config: BrevoConfigI | FirebaseConfigI;
  instance: Brevo | Firebase;
};
