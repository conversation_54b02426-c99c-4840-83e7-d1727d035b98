import { Prisma, PrismaClient } from '@prisma/mongodb';
import type { UndefinedNullableI } from './data';
import type { DefaultArgs } from '@prisma/client/runtime/library';

export type MongoTxnI = Omit<
  PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
  '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
>;
export type TotalI = { total: number };

export type RunCommandRawI<T> = {
  cursor: {
    firstBatch?: UndefinedNullableI<T[]>;
  };
};
export type TimestampI = {
  $timestamp: NestedTimestampI;
};
export type NestedTimestampI = {
  t: number; // seconds since epoch
  i: number; // increment
};

export type ObjectIdI = {
  $oid: string;
};
