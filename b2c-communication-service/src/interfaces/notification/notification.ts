import { NotificationTypeI } from '@consts/notification/notification';
import { ObjectIdI, TimestampI } from '@interfaces/common/db';
import { NotificationTypeE } from '@prisma/mongodb';

export type NotificationFetchManyQueryI = {
  id: string;
  type: NotificationTypeE;
  data: NotificationDataI;
  isRead: boolean;
  createdAt: Date;
};

export type NotificationDataI = { [key: string]: string } & {
  actorProfileId: string;
  postId: string;
};

export type NotificationDBResultI = {
  _id: ObjectIdI;
  type: NotificationTypeI;
  isRead: boolean;
  createdAt: TimestampI;
  data: NotificationDataI;
};
