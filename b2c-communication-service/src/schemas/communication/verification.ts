import { CommunicationModeE, CommunicationTypeE } from '@consts/communication/common';
import { UUIDSchema } from '@schemas/common/common';
import z from 'zod';

export const CommunicationVerificationDeactivateSchema = z.object({
  mode: CommunicationModeE,
  type: CommunicationTypeE,
});
export type CommunicationVerificationDeactivateI = z.infer<typeof CommunicationVerificationDeactivateSchema>;

export const CommunicationVerificationCreateOneSchema = z.object({
  communicationId: UUIDSchema,
  data: z.union([z.string(), z.string(), z.string().url()]),
  expiry: z.number().int(),
  profileId: UUIDSchema,
});
export type CommunicationVerificationCreateOneI = z.infer<typeof CommunicationVerificationCreateOneSchema>;
