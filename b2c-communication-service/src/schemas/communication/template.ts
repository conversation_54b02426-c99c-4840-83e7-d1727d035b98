import { CommunicationModeE, CommunicationTypeE } from '@consts/communication/common';
import z from 'zod';
import { OTPSchema } from './common';

const CommunicationTemplateFetchOneSchema = z.object({
  mode: CommunicationModeE,
  type: CommunicationTypeE,
});
export type CommunicationTemplateFetchOneI = z.infer<typeof CommunicationTemplateFetchOneSchema>;

export const CommunicationTemplateEmailIdVerificationSchema = z.object({
  otp: OTPSchema,
});
export type CommunicationTemplateEmailIdVerificationI = z.infer<typeof CommunicationTemplateEmailIdVerificationSchema>;

export const CommunicationTemplatePhoneVerificationSchema = z.object({
  otp: z.string().length(6),
});
export type CommunicationTemplatePhoneVerificationI = z.infer<typeof CommunicationTemplatePhoneVerificationSchema>;
