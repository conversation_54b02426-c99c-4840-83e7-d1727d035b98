import { CommunicationTypeE } from '@consts/communication/common';
import { UUIDSchema } from '@schemas/common/common';
import z from 'zod';
import { CommunicationEmailTypeE, OTPSchema } from './common';

export const CommunicationEmailSendOneSchema = z.object({
  email: z.string().email(),
  name: z.string(),
  profileId: UUIDSchema,
  type: CommunicationEmailTypeE,
});
export type CommunicationEmailSendOneI = z.infer<typeof CommunicationEmailSendOneSchema>;

export const CommunicationEmailVerifySchema = z.object({
  otp: OTPSchema,
  profileId: UUIDSchema,
  type: CommunicationEmailTypeE,
});
export type CommunicationEmailVerifyI = z.infer<typeof CommunicationEmailVerifySchema>;
