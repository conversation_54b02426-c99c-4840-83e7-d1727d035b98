import { CommunicationTypeE } from '@consts/communication/common';
import z from 'zod';

export const EmailPhoneSchema = z.union([z.string().email(), z.string()]);
export type EmailPhoneSchemaI = z.infer<typeof EmailPhoneSchema>;

export const OTPSchema = z.string().length(6);
export const CommunicationEmailTypeE = z.enum([
  CommunicationTypeE.Enum.EMAIL_ID_PASSWORD_RESET,
  CommunicationTypeE.Enum.EMAIL_ID_VERIFICATION,
]);
