import { CommunicationModeE, CommunicationTypeE } from '@consts/communication/common';
import { UUIDSchema } from '@schemas/common/common';
import z from 'zod';
import { EmailPhoneSchema } from './common';
import {
  CommunicationTemplateEmailIdVerificationSchema,
  CommunicationTemplatePhoneVerificationSchema,
} from './template';

export const CommunicationCreateParamsSchema = z.union([
  CommunicationTemplateEmailIdVerificationSchema,
  CommunicationTemplatePhoneVerificationSchema,
]);
export type CommunicationCreateParamsI = z.infer<typeof CommunicationCreateParamsSchema>;

export const CommunicationCreateOneSchema = z.object({
  mode: CommunicationModeE,
  type: CommunicationTypeE,
  templateId: UUIDSchema,
  sender: EmailPhoneSchema,
  receiver: EmailPhoneSchema,
  profileId: UUIDSchema,
  params: CommunicationCreateParamsSchema,
});
export type CommunicationCreateOneI = z.infer<typeof CommunicationCreateOneSchema>;
