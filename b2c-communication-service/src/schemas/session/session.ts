import { OprTypeE } from '@consts/common/data';
import { UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const SessionCreateOneSchema = z.object({
  opr: z.enum([OprTypeE.Values.CREATE]),
  sessionId: UUIDSchema,
  profileId: UUIDSchema,
  isActive: z.boolean().optional(),
  // uc 100
  deviceToken: z.string().min(1).max(255),
});
export type SessionCreateOneI = z.infer<typeof SessionCreateOneSchema>;

export const SessionUpdateOneSchema = z.object({
  opr: z.enum([OprTypeE.Values.DELETE]),
  sessionId: UUIDSchema,
});
export type SessionUpdateOneI = z.infer<typeof SessionUpdateOneSchema>;

export const SessionDelsertMessageSchema = z.discriminatedUnion('opr', [
  SessionUpdateOneSchema,
  SessionCreateOneSchema,
]);

export type SessionDelsertMessageI = z.infer<typeof SessionDelsertMessageSchema>;
