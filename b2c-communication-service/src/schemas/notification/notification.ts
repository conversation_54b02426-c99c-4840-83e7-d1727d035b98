import { KafkaTopicE } from '@consts/kafka';
import { NotificationTypeE } from '@consts/notification/notification';
import { FirebaseFCMTopicSchema } from '@navicater/vendor-firebase';
import { CursorPaginationSchema, ObjectIdSchema, ProfileIdQuerySchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const ActorProfileNameSchema = z
  .string()
  .trim()
  .transform((data) => data?.split(' ')?.[0]?.substring(0, 15) || 'Unknown');
export const PostTextSchema = z
  .string()
  .trim()
  .transform((data) => `${data?.length > 20 ? `${data?.substring(0, 16)}...` : data}`);

export const NotificationPostSchema = z.object({
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
  postId: UUIDSchema,
});
export type NotificationPostI = z.infer<typeof NotificationPostSchema>;

export const NotificationLikeSchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.LIKE]),
  postText: PostTextSchema,
});
export type NotificationLikeI = z.infer<typeof NotificationLikeSchema>;

export const NotificationCommentSchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.COMMENT]),
  commentId: UUIDSchema,
  postText: PostTextSchema,
});
export type NotificationCommentI = z.infer<typeof NotificationCommentSchema>;

export const NotificationReplySchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.REPLY]),
  commentId: UUIDSchema,
  parentCommentId: UUIDSchema,
  postText: PostTextSchema,
});

export type NotificationReplyI = z.infer<typeof NotificationReplySchema>;

export const NotificationUpdateReadSchema = z.object({
  type: z.enum([NotificationTypeE.Enum.UPDATE_READ]),
  ids: z.array(ObjectIdSchema).min(1),
});
export type NotificationUpdateReadI = z.infer<typeof NotificationUpdateReadSchema>;

export const NotificationFollowerSchema = z.object({
  type: z.enum([NotificationTypeE.Enum.FOLLOWER]),
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
});
export type NotificationFollowerI = z.infer<typeof NotificationFollowerSchema>;

export const NotificationRequestReceivedSchema = z.object({
  type: z.enum([NotificationTypeE.Enum.REQUEST_RECEIVED]),
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
  requestId: UUIDSchema,
});
export type NotificationRequestReceivedI = z.infer<typeof NotificationRequestReceivedSchema>;

export const NotificationRequestAcceptedSchema = NotificationRequestReceivedSchema.extend({
  type: z.enum([NotificationTypeE.Enum.REQUEST_ACCEPTED]),
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
});
export type NotificationRequestAcceptedI = z.infer<typeof NotificationRequestAcceptedSchema>;

export const NotificationFetchManySchema = CursorPaginationSchema;
export type NotificationFetchManyI = z.infer<typeof NotificationFetchManySchema>;
export const NotificationCreateOneSchema = z
  .object({
    actorProfileId: UUIDSchema,
    actorProfileName: ActorProfileNameSchema,
    commentId: UUIDSchema,
    ids: z.array(ObjectIdSchema).min(1),
    parentCommentId: UUIDSchema,
    postId: UUIDSchema,
    postText: PostTextSchema,
    profileId: UUIDSchema.optional(),
    profileIds: z.array(UUIDSchema).min(1).optional(),
    type: NotificationTypeE,
    topic: KafkaTopicE,
    firebaseTopic: FirebaseFCMTopicSchema.optional(),
  })
  .partial();
export type NotificationCreateOneI = z.infer<typeof NotificationCreateOneSchema>;
