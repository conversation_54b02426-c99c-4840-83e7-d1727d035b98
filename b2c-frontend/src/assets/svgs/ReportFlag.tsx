/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const ReportFlag: React.FC<FilledIconPropsI> = ({
  width = 2.976,
  height = 2.976,
  color,
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <Path
        d="M5.155 6.24A.43.43 0 005 6.565v12.007a.42.42 0 00.13.303.457.457 0 00.633 0 .42.42 0 00.13-.303v-3.23c2.092-1.627 3.864-.785 5.909.184 1.2.571 2.494 1.184 3.872 1.184 1.01 0 2.067-.328 3.17-1.244a.432.432 0 00.156-.322V6.564a.416.416 0 00-.07-.232.441.441 0 00-.191-.158.464.464 0 00-.479.066c-2.159 1.794-3.967.935-6.062-.06-2.077-.987-4.443-2.097-7.043.06zm12.951 8.7c-2.09 1.63-3.863.786-5.908-.184-1.881-.893-3.99-1.894-6.304-.47v-7.52c2.09-1.629 3.863-.786 5.908.182 1.881.894 3.99 1.895 6.304.474v7.519z"
        fill={color || '#000'}
      />
    </Svg>
  );
};

export default ReportFlag;
