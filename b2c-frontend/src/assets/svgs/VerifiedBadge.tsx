/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const VerifiedBadgeIcon: React.FC<FilledIconPropsI> = ({
  width = 1.736,
  height = 1.984,
  fill = '#0063FB',
  color,
  disabled,
  accessibilityLabel = 'Verified Badge',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 14 16"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M7 0.208252C7 0.208252 0.625 2.0145 0.625 2.33325V8.77909C0.625 12.0374 5.33542 14.9062 6.64583 15.6853C6.75067 15.7554 6.87392 15.7928 7 15.7928C7.12608 15.7928 7.24933 15.7554 7.35417 15.6853C8.7 14.9416 13.375 12.0374 13.375 8.77909V2.33325C13.375 2.0145 7 0.208252 7 0.208252ZM10.3292 6.01658L6.43333 9.91242C6.3703 9.98112 6.29367 10.036 6.20832 10.0735C6.12296 10.111 6.03074 10.1304 5.9375 10.1304C5.84426 10.1304 5.75204 10.111 5.66668 10.0735C5.58133 10.036 5.5047 9.98112 5.44167 9.91242L3.70625 8.177C3.57843 8.05683 3.4949 7.89705 3.46919 7.7235C3.44348 7.54996 3.47709 7.37282 3.56458 7.22075C3.62559 7.13478 3.70501 7.0635 3.79706 7.01213C3.88911 6.96075 3.99147 6.93057 4.09667 6.92378C4.20187 6.91699 4.30725 6.93377 4.40514 6.9729C4.50303 7.01202 4.59096 7.0725 4.6625 7.14992L5.9375 8.42492L9.3375 5.02492C9.47391 4.92261 9.64264 4.87295 9.81271 4.88503C9.98279 4.89712 10.1428 4.97015 10.2634 5.09072C10.3839 5.21128 10.457 5.37129 10.4691 5.54137C10.4811 5.71145 10.4315 5.88018 10.3292 6.01658Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default VerifiedBadgeIcon;
