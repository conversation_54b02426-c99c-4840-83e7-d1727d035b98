/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

type UpVoteProps = OutlinedIconPropsI & {
  isLiked?: boolean;
};

const UpVote: React.FC<UpVoteProps> = ({
  width = 2.0,
  height = 2.0,
  isLiked = false,
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Up Vote',
  disabled,
  ...props
}) => {
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 16 15"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M15.27 7.653a.563.563 0 01-.52.347h-2.812v5.625a1.125 1.125 0 01-1.125 1.125H5.188a1.125 1.125 0 01-1.125-1.125V8H1.25a.563.563 0 01-.398-.96L7.602.29a.563.563 0 01.796 0l6.75 6.75a.562.562 0 01.122.613z"
        fill={isLiked ? '#448600' : '#525252'}
      />
    </Svg>
  );
};

export default UpVote;
