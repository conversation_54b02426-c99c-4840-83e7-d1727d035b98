/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Search: React.FC<FilledIconPropsI> = ({ width = 2.232, height = 2.232, color, ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 18 18"
      fill="none"
      {...props}
    >
      <Path
        d="M16.1479 15.3521L12.6273 11.8322C13.6477 10.6072 14.1566 9.03589 14.048 7.44524C13.9394 5.85459 13.2217 4.36705 12.0443 3.29205C10.8668 2.21705 9.32029 1.63737 7.72635 1.6736C6.13241 1.70982 4.6138 2.35916 3.48642 3.48654C2.35904 4.61392 1.7097 6.13253 1.67348 7.72647C1.63725 9.32041 2.21693 10.867 3.29193 12.0444C4.36692 13.2218 5.85447 13.9395 7.44512 14.0481C9.03577 14.1567 10.6071 13.6479 11.8321 12.6275L15.3519 16.148C15.4042 16.2003 15.4663 16.2417 15.5345 16.27C15.6028 16.2983 15.676 16.3129 15.7499 16.3129C15.8238 16.3129 15.897 16.2983 15.9653 16.27C16.0336 16.2417 16.0956 16.2003 16.1479 16.148C16.2001 16.0957 16.2416 16.0337 16.2699 15.9654C16.2982 15.8971 16.3127 15.8239 16.3127 15.75C16.3127 15.6761 16.2982 15.6029 16.2699 15.5347C16.2416 15.4664 16.2001 15.4043 16.1479 15.3521ZM2.81242 7.87504C2.81242 6.87377 3.10933 5.89499 3.6656 5.06246C4.22188 4.22994 5.01253 3.58107 5.93758 3.1979C6.86263 2.81473 7.88053 2.71448 8.86256 2.90981C9.84459 3.10515 10.7466 3.58731 11.4546 4.29531C12.1626 5.00331 12.6448 5.90536 12.8401 6.88739C13.0355 7.86942 12.9352 8.88732 12.5521 9.81237C12.1689 10.7374 11.52 11.5281 10.6875 12.0844C9.85497 12.6406 8.87618 12.9375 7.87492 12.9375C6.53271 12.9361 5.24591 12.4022 4.29683 11.4531C3.34775 10.504 2.81391 9.21724 2.81242 7.87504Z"
        fill={color || '#7B7A76'}
      />
    </Svg>
  );
};

export default Search;
