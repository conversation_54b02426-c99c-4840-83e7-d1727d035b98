import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const UsersThree: React.FC<OutlinedIconPropsI> = ({
  width = 3.1,
  height = 2.6,
  fill = '#000',
  color,
  disabled,
  ...props
}) => {
  const mainFill = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 31 26"
      fill="none"
      {...props}
    >
      <Path
        d="M26.2 14.162a.753.753 0 01-1.05-.15 4.838 4.838 0 00-3.9-1.95.751.751 0 010-1.5 2.25 2.25 0 10-2.178-2.813.75.75 0 11-1.454-.375 3.75 3.75 0 116.164 3.704 6.37 6.37 0 012.571 2.033.75.75 0 01-.153 1.05zm-5.051 5.775a.75.75 0 11-1.297.75 5.344 5.344 0 00-9.203 0 .75.75 0 11-1.297-.75 6.755 6.755 0 013.163-2.805 4.5 4.5 0 115.47 0 6.755 6.755 0 013.164 2.805zm-5.899-3.375a3 3 0 100-6.001 3 3 0 000 6zM10 11.312a.75.75 0 00-.75-.75 2.25 2.25 0 112.179-2.813.75.75 0 001.453-.375 3.75 3.75 0 10-6.163 3.704 6.37 6.37 0 00-2.569 2.033.75.75 0 001.2.9 4.837 4.837 0 013.9-1.95.75.75 0 00.75-.75z"
        fill={mainFill}
        opacity={opacity}
      />
    </Svg>
  );
};

export default UsersThree;
