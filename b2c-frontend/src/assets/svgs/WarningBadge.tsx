/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const WarningBadgeIcon: React.FC<FilledIconPropsI> = ({
  width = 1.86,
  height = 1.86,
  fill = '#DC2626',
  color,
  disabled,
  accessibilityLabel = 'Warning Badge',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 15 15"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M7.5 0C6.01664 0 4.56659 0.439867 3.33323 1.26398C2.09986 2.08809 1.13856 3.25943 0.570907 4.62987C0.00324963 6.00032 -0.145275 7.50832 0.144114 8.96318C0.433503 10.418 1.14781 11.7544 2.1967 12.8033C3.2456 13.8522 4.58197 14.5665 6.03682 14.8559C7.49168 15.1453 8.99968 14.9967 10.3701 14.4291C11.7406 13.8614 12.9119 12.9001 13.736 11.6668C14.5601 10.4334 15 8.98336 15 7.5C14.9979 5.51152 14.207 3.60509 12.801 2.19902C11.3949 0.79295 9.48848 0.00209987 7.5 0ZM7.5 13.8462C6.24485 13.8462 5.01789 13.474 3.97427 12.7766C2.93065 12.0793 2.11725 11.0882 1.63692 9.92857C1.1566 8.76896 1.03092 7.49296 1.27579 6.26193C1.52066 5.03089 2.12507 3.90012 3.01259 3.01259C3.90012 2.12507 5.0309 1.52065 6.26193 1.27579C7.49296 1.03092 8.76896 1.15659 9.92857 1.63692C11.0882 2.11724 12.0793 2.93065 12.7766 3.97426C13.474 5.01788 13.8462 6.24485 13.8462 7.5C13.8442 9.18252 13.175 10.7956 11.9853 11.9853C10.7956 13.175 9.18252 13.8442 7.5 13.8462ZM6.92308 8.07692V4.03846C6.92308 3.88545 6.98386 3.73871 7.09206 3.63051C7.20025 3.52232 7.34699 3.46154 7.5 3.46154C7.65301 3.46154 7.79975 3.52232 7.90795 3.63051C8.01614 3.73871 8.07692 3.88545 8.07692 4.03846V8.07692C8.07692 8.22993 8.01614 8.37667 7.90795 8.48487C7.79975 8.59306 7.65301 8.65384 7.5 8.65384C7.34699 8.65384 7.20025 8.59306 7.09206 8.48487C6.98386 8.37667 6.92308 8.22993 6.92308 8.07692ZM8.36539 10.6731C8.36539 10.8442 8.31463 11.0115 8.21954 11.1539C8.12445 11.2962 7.9893 11.4071 7.83117 11.4726C7.67304 11.5381 7.49904 11.5552 7.33117 11.5218C7.16331 11.4884 7.00911 11.406 6.88808 11.285C6.76706 11.164 6.68464 11.0098 6.65125 10.8419C6.61785 10.674 6.63499 10.5 6.70049 10.3419C6.76599 10.1838 6.87691 10.0486 7.01922 9.95353C7.16153 9.85844 7.32885 9.80769 7.5 9.80769C7.72952 9.80769 7.94963 9.89886 8.11192 10.0612C8.27421 10.2234 8.36539 10.4436 8.36539 10.6731Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default WarningBadgeIcon;
