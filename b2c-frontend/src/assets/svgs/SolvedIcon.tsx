/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const SolvedIcon: React.FC<OutlinedIconPropsI> = ({
  width = 3.0,
  height = 3.5,
  stroke = '#448600',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Solved Icon',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 12 13"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.835 6.5a5.833 5.833 0 11-11.667 0 5.833 5.833 0 0111.667 0zM8.352 4.732c.171.171.171.448 0 .619L5.436 8.268a.437.437 0 01-.62 0L3.65 7.1a.437.437 0 11.619-.619l.857.858L6.43 6.036l1.304-1.304c.17-.17.447-.17.618 0z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default SolvedIcon;
