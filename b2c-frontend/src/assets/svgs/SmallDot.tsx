/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const DotSmall: React.FC<FilledIconPropsI> = ({
  width = 1.736,
  height = 1.86,
  fill = '#525252',
  color,
  disabled,
  accessibilityLabel = 'Dot Small',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 14 15"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path d="M7.327 6.692a.794.794 0 110 1.587.794.794 0 010-1.587z" fill={fillColor} />
    </Svg>
  );
};

export default DotSmall;
