/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Skills: React.FC<FilledIconPropsI> = ({
  width = 5.456,
  height = 5.58,
  fill = '#448600',
  color,
  ...props
}) => {
  const fillColor = color || fill;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 44 45"
      fill="none"
      {...props}
    >
      <Circle cx={22} cy={22.4707} r={21.5} fill="#F2F9E5" stroke="#DDEFC8" />
      <Path
        d="M31.2581 16.9393C31.2126 16.8269 31.1405 16.7271 31.048 16.6487C30.9555 16.5702 30.8453 16.5153 30.727 16.4888C30.6086 16.4623 30.4856 16.4648 30.3684 16.4963C30.2513 16.5277 30.1435 16.5871 30.0543 16.6693L26.2762 20.1559L24.6609 19.809L24.314 18.1937L27.8006 14.4156C27.8828 14.3264 27.9422 14.2186 27.9736 14.1015C28.0051 13.9843 28.0076 13.8613 27.9811 13.7429C27.9545 13.6246 27.8997 13.5144 27.8212 13.4219C27.7428 13.3294 27.643 13.2573 27.5306 13.2118C26.5058 12.7973 25.395 12.6408 24.2956 12.756C23.1962 12.8713 22.1419 13.2548 21.2254 13.8728C20.3089 14.4908 19.5582 15.3245 19.0393 16.3006C18.5204 17.2766 18.2491 18.3652 18.2493 19.4706C18.2481 20.404 18.4396 21.3277 18.8118 22.1837L13.1671 27.0643C13.1531 27.0756 13.14 27.0887 13.1268 27.1009C12.5642 27.6636 12.248 28.4267 12.248 29.2224C12.248 29.6165 12.3257 30.0066 12.4764 30.3706C12.6272 30.7346 12.8482 31.0654 13.1268 31.344C13.4054 31.6226 13.7362 31.8436 14.1002 31.9944C14.4642 32.1452 14.8544 32.2228 15.2484 32.2228C16.0441 32.2228 16.8073 31.9067 17.37 31.344C17.3821 31.3318 17.3953 31.3178 17.4065 31.3046L22.2862 25.6581C23.3141 26.1093 24.4383 26.297 25.5571 26.2043C26.6758 26.1115 27.7537 25.7412 28.6933 25.1269C29.6329 24.5125 30.4044 23.6736 30.938 22.6859C31.4716 21.6983 31.7505 20.5931 31.7493 19.4706C31.7508 18.603 31.584 17.7434 31.2581 16.9393ZM24.9993 24.7206C24.1116 24.7194 23.2386 24.4936 22.4615 24.0643C22.311 23.9812 22.1364 23.9523 21.9671 23.9825C21.7978 24.0127 21.644 24.1002 21.5315 24.2303L16.2909 30.2996C16.0073 30.5691 15.6297 30.7171 15.2385 30.712C14.8474 30.707 14.4737 30.5494 14.1971 30.2728C13.9205 29.9962 13.7629 29.6225 13.7579 29.2314C13.7528 28.8402 13.9008 28.4626 14.1703 28.179L20.235 22.9393C20.3653 22.8268 20.4529 22.6728 20.4831 22.5033C20.5133 22.3338 20.4843 22.159 20.4009 22.0084C19.9228 21.1437 19.6984 20.1616 19.7536 19.1751C19.8087 18.1885 20.1412 17.2376 20.7127 16.4315C21.2842 15.6255 22.0715 14.9971 22.9842 14.6186C23.8969 14.2401 24.8979 14.1268 25.8721 14.2918L22.9471 17.4615C22.8658 17.5498 22.8068 17.6563 22.7751 17.7722C22.7435 17.888 22.7401 18.0097 22.7653 18.1271L23.2959 20.5956C23.3262 20.7368 23.3967 20.8662 23.4988 20.9683C23.6009 21.0704 23.7303 21.1408 23.8715 21.1712L26.3418 21.7018C26.4592 21.727 26.581 21.7236 26.6968 21.692C26.8126 21.6603 26.9192 21.6013 27.0075 21.5199L30.1771 18.5949C30.3035 19.3478 30.2644 20.1191 30.0624 20.8552C29.8605 21.5914 29.5006 22.2747 29.0078 22.8577C28.5151 23.4407 27.9012 23.9093 27.209 24.231C26.5167 24.5528 25.7627 24.7198 24.9993 24.7206Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Skills;
