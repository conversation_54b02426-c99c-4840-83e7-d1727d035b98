/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Suitcase: React.FC<FilledIconPropsI> = ({
  width = 5.456,
  height = 5.456,
  color,
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 44 45"
      fill="none"
      {...props}
    >
      <Circle cx={22} cy={22.4707} r={21.5} fill="#F2F9E5" stroke="#DDEFC8" />
      <Path
        d="M30.25 16.47H26.5a4.5 4.5 0 10-9 0h-3.75a1.5 1.5 0 00-1.5 1.5v11.25a1.5 1.5 0 001.5 1.5h16.5a1.5 1.5 0 001.5-1.5V17.97a1.5 1.5 0 00-1.5-1.5zm-8.25-3a3 3 0 013 3h-6a3 3 0 013-3zm8.25 15.75h-16.5V17.97h16.5v11.25z"
        fill={color || '#448600'}
      />
    </Svg>
  );
};

export default Suitcase;
