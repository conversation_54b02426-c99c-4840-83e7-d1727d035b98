/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ViewStyle } from 'react-native';
import { SvgProps } from 'react-native-svg';

export type IconBasePropsI = Omit<SvgProps, 'width' | 'height'> & {
  width?: number;
  height?: number;
  color?: string;
  secondaryColor?: string;
  containerStyle?: ViewStyle;
  strokeWidth?: number;
  accessibilityLabel?: string;
  active?: boolean;
  disabled?: boolean;
};

export type FilledIconPropsI = IconBasePropsI & {
  fill?: string;
};

export type OutlinedIconPropsI = IconBasePropsI & {
  stroke?: string;
};

export type DualStyleIconPropsI = OutlinedIconPropsI & IconBasePropsI;
