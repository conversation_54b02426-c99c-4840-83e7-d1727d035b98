/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const VideoPreview: React.FC<OutlinedIconPropsI> = ({
  width = 3.0,
  height = 3.5,
  stroke = '#448600',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Video Preview',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 23 14"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M22.603 1.844a.75.75 0 00-.77.036L18.5 4.098V1.75A1.5 1.5 0 0017 .25H2a1.5 1.5 0 00-1.5 1.5v10.5a1.5 1.5 0 001.5 1.5h15a1.5 1.5 0 001.5-1.5V9.906l3.334 2.223a.75.75 0 00.416.121.75.75 0 00.75-.75v-9a.75.75 0 00-.397-.656zM17 12.25H2V1.75h15v10.5zm4.5-2.152l-3-2V5.902l3-1.995v6.192z"
        fill={strokeColor}
        strokeWidth={strokeWidth}
      />
    </Svg>
  );
};

export default VideoPreview;
