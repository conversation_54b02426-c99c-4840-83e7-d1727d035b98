/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
const { execSync } = require('child_process');
const os = require('os');
const path = require('path');

try {
  if (os.platform() === 'win32') {
    execSync(path.resolve('scripts', 'setup-env', 'install-extension.bat'), { stdio: 'inherit' });
  } else {
    execSync(`bash ${path.resolve('scripts', 'setup-env', 'install-extensions.sh')}`, {
      stdio: 'inherit',
    });
  }
} catch (error) {
  process.exit(1);
}
