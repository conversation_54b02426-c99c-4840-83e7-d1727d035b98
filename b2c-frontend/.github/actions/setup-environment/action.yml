name: 'Setup Mobile Environment'
description: 'Sets version management for both Android and iOS'

inputs:
  node-version:
    description: 'Node.js version'
    required: false
    default: '20'
  platform:
    description: 'Mobile platform (android or ios)'
    required: true

outputs:
  version:
    description: 'The semantic version to use'
    value: ${{ steps.version.outputs.version }}
  build-number:
    description: 'The build number to use'
    value: ${{ steps.version.outputs.build_number }}

runs:
  using: 'composite'
  steps:
    - name: Calculate version info
      id: version
      shell: bash
      run: |
        VERSION_NAME=$(node -p "require('./package.json').version")
        echo "📦 Package version: $VERSION_NAME"

        if [ "${{ inputs.platform }}" == "android" ]; then
          VERSION_CODE=$(grep -o "versionCode [0-9]*" android/app/build.gradle | awk '{print $2}')
          if [ -z "$VERSION_CODE" ]; then
            echo "Could not find Android versionCode. Defaulting to 1."
            VERSION_CODE=1
          fi
          NEW_VERSION_CODE=$((VERSION_CODE + 1))
          echo "📱 Android versionCode: $VERSION_CODE -> $NEW_VERSION_CODE"
        else
          if [ -f "ios/navicater/Info.plist" ]; then
            PLIST_FILE="ios/navicater/Info.plist"
            echo "📄 Found Info.plist at: $PLIST_FILE"
            
            CURRENT_BUILD_NUMBER=$(grep -A1 "<key>CFBundleVersion</key>" "$PLIST_FILE" | grep -o "<string>[0-9]*</string>" | grep -o "[0-9]*")
            
            if [ -z "$CURRENT_BUILD_NUMBER" ]; then
              echo "⚠️ Could not extract build number from $PLIST_FILE. Defaulting to 1."
              CURRENT_BUILD_NUMBER=1
            else
              echo "🔍 Found current build number: $CURRENT_BUILD_NUMBER"
            fi
          else
            echo "⚠️ Info.plist not found. Defaulting to build number 1."
            CURRENT_BUILD_NUMBER=1
          fi
          
          NEW_VERSION_CODE=$((CURRENT_BUILD_NUMBER + 1))
          echo "🍏 iOS build number: $CURRENT_BUILD_NUMBER -> $NEW_VERSION_CODE"
        fi

        echo "version=$VERSION_NAME" >> $GITHUB_OUTPUT
        echo "build_number=$NEW_VERSION_CODE" >> $GITHUB_OUTPUT
        echo "✅ Final Version: $VERSION_NAME"
        echo "✅ Final Build Number: $NEW_VERSION_CODE"
