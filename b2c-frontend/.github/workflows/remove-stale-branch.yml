name: 📦 Delete Stale Branches

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

jobs:
  delete-stale-branches:
    runs-on: ubuntu-latest
    steps:
      - name: Delete stale branches
        uses: fpicalausa/remove-stale-branches@v2.4.0
        with:
          github-token: ${{ secrets.STALE_BRANCH_ACTION_TOKEN }}
          dry-run: true
          restrict-branches-regex: '^(?!main$|dev$|staging$|android-prod-release$|ios-prod-release$|android-alpha-release$|ios-alpha-release$|android-beta-release$).*$'
          days-before-branch-stale: 30
          days-before-branch-delete: 0
