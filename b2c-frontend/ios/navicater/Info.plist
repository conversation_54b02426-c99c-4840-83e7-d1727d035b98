<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>

	<key>CFBundleDisplayName</key>
	<string>Navicater</string>

	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>

	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>

	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>

	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>

	<key>CFBundlePackageType</key>
	<string>APPL</string>

	<key>CFBundleShortVersionString</key>
	<string>0.0.4</string>

	<key>CFBundleSignature</key>
	<string>????</string>

	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.900862906994-urn1h2m4r4db8t3pnlj0dp43jndp79f1</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.navicater</string>
			</array>
		</dict>
	</array>

	<key>CFBundleVersion</key>
	<string>488</string>

	<key>LSRequiresIPhoneOS</key>
	<true/>

	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>

	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Show current location on map.</string>

	<key>NSMicrophoneUsageDescription</key>
	<string>We allow voice recording in chat for clearer and more convenient communication with other maritime professionals.</string>

	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We request permission to save photos and videos to your photo library, such as when you download shared media or save captured content.</string>

	<key>NSPhotoLibraryUsageDescription</key>
	<string>We allow you to upload profile pictures and share photos in posts, forums, and chats for professional networking and knowledge sharing.</string>

	<key>NSUserNotificationAlertUsageDescription</key>
	<string>We use notifications to inform you of messages, alerts, and updates.</string>

	<key>NSDocumentsFolderUsageDescription</key>
	<string>We allow uploading of certificates, identity documents, and PDFs to build your professional record and share files in posts and forums.</string>

	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
	</array>

	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>

	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>

	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>

	<key>UIUserInterfaceStyle</key>
	<string>Light</string>

	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>

</dict>
</plist>
