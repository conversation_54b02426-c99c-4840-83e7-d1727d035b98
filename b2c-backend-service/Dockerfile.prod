ARG DEPS_IMAGE=node:22-alpine
ARG BUILD_IMAGE=node:22-alpine
ARG PROD_IMAGE=node:22-alpine
ARG RUN_IMAGE=node:22-alpine
###########################

# Stage 1: Dependency installation

FROM $DEPS_IMAGE AS deps

WORKDIR /app

# Copy files required for dependency installation
COPY .npmrc .npmrc
COPY package.json package-lock.json* ./

# Installs dependencies & removes the credentials in the same layer
RUN npm ci --legacy-peer-deps && rm -r .npmrc

###########################
###########################

### Stage 2: Build

FROM $BUILD_IMAGE AS builder

WORKDIR /app

# Copy installed dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy files needed for build
COPY --from=deps /app/package.json /app/package-lock.json ./
COPY tsconfig.json ./

# Copy prisma file
COPY prisma ./prisma/

# Copy source code
COPY src ./src

# Install dependencies needed by Prisma
# RUN apt-get update -y && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*


# Generate Prisma client
RUN npm run generate

# Compile Typescript
RUN npm run build

###########################
###########################

### Stage 3: Production
FROM $PROD_IMAGE AS prod

WORKDIR /app

# Copy package.json for installation of production's dependencies
COPY .npmrc .npmrc
COPY --from=builder /app/package.json /app/package-lock.json ./

# Install only production's dependencies
RUN npm ci --only=production --omit=dev --legacy-peer-deps && rm -r .npmrc

###########################
###########################

### Stage 4: Runtime

FROM $RUN_IMAGE AS runtime

WORKDIR /app

# Install dependencies needed by Prisma
# RUN apt-get update -y && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*

# Copy compiled runtime files
COPY --from=builder /app/dist ./dist

# Copy prisma files needed for runtime
COPY --from=builder /app/prisma ./prisma

# Copy node_modules with only production's dependencies
COPY --from=prod /app/node_modules ./node_modules

# Copy compiled prisma files
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma

# App's port
EXPOSE 4000
# Start the server
CMD ["node", "dist/index.js"]

###########################
