root = true

[*]
end_of_line = lf
charset = utf-8
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true

[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false  # preserve formatting in markdown

[*.sh]
end_of_line = lf
charset = utf-8
indent_style = space
indent_size = 2

[*.yml]
indent_style = space
indent_size = 2

[*.yaml]
indent_style = space
indent_size = 2

[*.json]
indent_style = space
indent_size = 2

[*.{js,ts,jsx,tsx}]
indent_style = space
indent_size = 2
