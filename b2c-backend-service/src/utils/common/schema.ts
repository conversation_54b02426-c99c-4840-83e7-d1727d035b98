import { z } from 'zod';

// Utility function to validate decimal precision
export const createDecimalPrecisionSchema = ({
  maxDigits,
  decimalPlaces,
  min,
  max,
}: {
  maxDigits: number;
  decimalPlaces: number;
  min?: number;
  max?: number;
}) =>
  z
    .union([
      z.number().int(), // Allow integers
      z.number().refine(
        (val) => {
          const regex = new RegExp(`^-?\\d{1,${maxDigits - decimalPlaces}}(\\.\\d{1,${decimalPlaces}})?$`);
          return regex.test(val.toString());
        },
        { message: `Must be a number with up to ${decimalPlaces} decimal places and max ${maxDigits} total digits.` },
      ),
    ])
    .refine((val) => (min !== undefined && max !== undefined ? val >= min && val <= max : true), {
      message: min !== undefined && max !== undefined ? `Must be between ${min} and ${max}` : 'Invalid range',
    });
