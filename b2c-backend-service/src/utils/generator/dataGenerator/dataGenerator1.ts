import * as fs from 'fs';
import * as path from 'path';

const inputPath = path.join(__dirname, './input1.json');
const inputRawData = JSON.parse(fs.readFileSync(inputPath, 'utf8'));

const _inputMap = inputRawData.reduce((acc, curr) => {
  acc[curr.imo] = curr.subVesselTypeId;
  return acc;
}, {});

const _shipPath = path.join(__dirname, './ship.json');
// const shipRawData = JSON.parse(fs.readFileSync(shipPath, 'utf8'));

const outputPath = path.join(__dirname, './output.json');
// const data = shipRawData.reduce((acc, item) => {

//   if (inputMap?.[item?.imo]) {
//     acc.push({
//       ...item,
//       subVesselTypeId: inputMap?.[String(item?.imo)],
//       createdAt: item.createdAt,
//       updatedAt: item.updatedAt,
//     })
//   }
//   return acc
// }, []);
const data = inputRawData.reduce((acc, item) => {
  // if (inputMap?.[item?.imo]) {
  acc.push({
    ...item,
    // subVesselTypeId: inputMap?.[String(item?.imo)],
    // createdAt: item.createdAt,
    // updatedAt: item.updatedAt,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  // }
  return acc;
}, []);
fs.writeFileSync(outputPath, JSON.stringify(data));
