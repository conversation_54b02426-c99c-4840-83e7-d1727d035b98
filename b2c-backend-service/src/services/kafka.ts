import { ENV } from '@consts/common/env';
import { KafkaTopicI } from '@consts/kafka';
import { delay } from '@utils/fn/process';
import { Kafka, Partitioners, Producer } from 'kafkajs';

class KafkaService {
  // Static instance initialized early
  public static readonly instance: KafkaService = new KafkaService();
  private producer: Producer;
  private kafka: Kafka;

  private isProducerConnected: boolean = false;
  private producerConnectAttempts: number = 0;
  private readonly maxAttempts: number = 3;
  private readonly reconnectDelay: number = 5000;

  private constructor() {
    if (!this.kafka) {
      this.kafka = new Kafka({
        clientId: ENV.KAFKA_BACKEND_CLIENT_ID,
        brokers: [ENV.KAFKA_BACKEND_BROKER],
        connectionTimeout: 10000, // 10 seconds
        requestTimeout: 30000, // 30 seconds
        retry: {
          initialRetryTime: 300,
          maxRetryTime: 30000,
          retries: 10,
        },
      });
      this.producer = this.kafka.producer({
        createPartitioner: Partitioners.DefaultPartitioner,
      });
      this.setupEventListeners();
    }
  }
  private setupEventListeners = (): void => {
    this.producer.on('producer.disconnect', () => {
      this.isProducerConnected = false;
      this.connectProducer();
    });

    this.producer.on('producer.connect', () => {
      this.isProducerConnected = true;
      this.producerConnectAttempts = 0;
    });
  };
  private connectProducer = async (): Promise<void> => {
    if (this.isProducerConnected || this.producerConnectAttempts > this.maxAttempts) {
      return;
    }
    try {
      await this.producer.connect();
      this.isProducerConnected = true;
    } catch (_error) {
      ++this.producerConnectAttempts;
      await delay(this.reconnectDelay);
      await this.connectProducer();
    }
    return;
  };
  private disconnectProducer = async (): Promise<void> => {
    if (this.isProducerConnected) {
      try {
        await this.producer.disconnect();
        this.isProducerConnected = false;
      } catch (_error) {
        //
      }
    }
  };
  public sendMessage = async (topic: KafkaTopicI, data: unknown): Promise<void> => {
    try {
      if (!this.isProducerConnected) {
        await this.connectProducer();
      }

      await this.producer.send({
        topic,
        messages: [{ value: JSON.stringify(data) }],
      });
    } catch (_error) {
      //
    }
    return;
  };
  public start = async (): Promise<void> => {
    await this.connectProducer();
  };
  public stop = async (): Promise<void> => {
    await this.disconnectProducer();
  };
}

export default KafkaService;
