import { Decimal10_2R, Decimal12_2R, Decimal6_6R, Decimal7_6R, Decimal8_2R } from '@consts/common/regex/numberRegex';
import { z } from 'zod';

export const Decimal6_6Schema = z.number().refine((value) => Decimal6_6R.test(value.toString()), {
  message: 'Amount must be a decimal number with up to 6 digits before and 6 digits after the decimal point',
});
export const Decimal7_6Schema = z.number().refine((value) => Decimal7_6R.test(value.toString()), {
  message: 'Amount must be a decimal number with up to 7 digits before and 6 digits after the decimal point',
});
export const Decimal8_2Schema = z.number().refine((value) => Decimal8_2R.test(value.toString()), {
  message: 'Amount must be a decimal number with up to 8 digits before and 2 digits after the decimal point',
});
export const Decimal10_2Schema = z.number().refine((value) => Decimal10_2R.test(value.toString()), {
  message: 'Amount must be a decimal number with up to 10 digits before and 2 digits after the decimal point',
});

export const Decimal12_2Schema = z.number().refine((value) => Decimal12_2R.test(value.toString()), {
  message: 'Amount must be a decimal number with up to 12 digits before and 2 digits after the decimal point',
});
