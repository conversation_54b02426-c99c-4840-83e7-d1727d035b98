import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const BlockingBlockOneSchema = z.object({
  toBlockId: UUIDSchema,
});

export type BlockingBlockOneI = z.infer<typeof BlockingBlockOneSchema>;

export const BlockingUnblockOneSchema = z.object({
  toUnblockId: UUIDSchema,
});

export type BlockingUnblockOneI = z.infer<typeof BlockingUnblockOneSchema>;

export const BlockingFetchManySchema = PaginationSchema;

export type BlockingFetchManyI = z.infer<typeof BlockingFetchManySchema>;
