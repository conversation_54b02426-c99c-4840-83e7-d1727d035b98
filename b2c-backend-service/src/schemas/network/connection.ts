import { PaginationSchema, ProfileIdCursorPaginationSchema } from '@schemas/common/common';
import { z } from 'zod';

export const ConnectionFetchManySchema = ProfileIdCursorPaginationSchema.extend({
  name: z.string().min(1).max(100).optional(),
});
export type ConnectionFetchManyI = z.infer<typeof ConnectionFetchManySchema>;

export const ConnectionSearchSchema = PaginationSchema.extend({
  search: z.string().min(1).max(100),
  // .transform((data) => data?.trim()),
});

export type ConnectionSearchI = z.infer<typeof ConnectionSearchSchema>;
