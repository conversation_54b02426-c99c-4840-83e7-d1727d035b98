import { CursorPaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

const TextSchema = z.string().min(1).max(255);

export const CommentCreateOneSchema = z.object({
  postId: UUIDSchema,
  text: TextSchema,
  parentCommentId: UUIDSchema.optional(),
});

export type CommentCreateOneI = z.infer<typeof CommentCreateOneSchema>;

export const CommentFetchManySchema = CursorPaginationSchema.extend({
  postId: UUIDSchema,
});

export type CommentFetchManyI = z.infer<typeof CommentFetchManySchema>;
export const CommentFetchRepliesSchema = CursorPaginationSchema.extend({
  postId: UUIDSchema,
  parentCommentId: UUIDSchema,
});
export type CommentFetchRepliesI = z.infer<typeof CommentFetchRepliesSchema>;
