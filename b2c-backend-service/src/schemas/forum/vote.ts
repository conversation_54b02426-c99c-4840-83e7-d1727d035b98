import { VoteTypeE } from '@consts/forum/answer';
import { CursorPaginationSchema, UUIDSchema } from '@schemas/common/common';
import z from 'zod';

export const ForumAnswerVoteFetchManySchema = CursorPaginationSchema.extend({
  answerId: UUIDSchema,
  type: VoteTypeE,
});

export type ForumAnswerVoteFetchManyI = z.infer<typeof ForumAnswerVoteFetchManySchema>;

export const ForumAnswerVoteCreateOneSchema = z.object({
  answerId: UUIDSchema,
  type: VoteTypeE,
});

export type ForumAnswerVoteCreateOneI = z.infer<typeof ForumAnswerVoteCreateOneSchema>;

export const ForumQuestionVoteFetchManySchema = CursorPaginationSchema.extend({
  questionId: UUIDSchema,
  type: VoteTypeE,
});

export type ForumQuestionVoteFetchManyI = z.infer<typeof ForumQuestionVoteFetchManySchema>;

export const ForumQuestionVoteCreateOneSchema = z.object({
  questionId: UUIDSchema,
  type: VoteTypeE,
});

export type ForumQuestionVoteCreateOneI = z.infer<typeof ForumQuestionVoteCreateOneSchema>;

export const ForumQuestionVoteDeleteOneSchema = z.object({
  questionId: UUIDSchema,
});
export type ForumQuestionVoteDeleteOneI = z.infer<typeof ForumQuestionVoteDeleteOneSchema>;

export const ForumAnswerVoteDeleteOneSchema = z.object({
  answerId: UUIDSchema,
});
export type ForumAnswerVoteDeleteOneI = z.infer<typeof ForumAnswerVoteDeleteOneSchema>;
