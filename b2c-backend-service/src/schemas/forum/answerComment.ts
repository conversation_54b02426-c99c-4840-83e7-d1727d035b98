import { CursorPaginationSchema, RouteParamsSchema, UUIDSchema } from '@schemas/common/common';
import z from 'zod';

export const ForumAnswerCommentCreateOneSchema = z.object({
  text: z.string().min(1).max(1000),
  answerId: UUIDSchema,
  parentCommentId: UUIDSchema.optional(),
});

export type ForumAnswerCommentCreateOneI = z.infer<typeof ForumAnswerCommentCreateOneSchema>;

export const ForumAnswerCommentFetchManySchema = CursorPaginationSchema.extend({
  answerId: UUIDSchema,
});
export type ForumAnswerCommentFetchManyI = z.infer<typeof ForumAnswerCommentFetchManySchema>;

export const ForumAnswerCommentFetchRepliesSchema = CursorPaginationSchema.extend({
  answerId: UUIDSchema,
  parentCommentId: UUIDSchema,
});
export type ForumAnswerCommentFetchRepliesI = z.infer<typeof ForumAnswerCommentFetchRepliesSchema>;

export const ForumAnswerCommentDeleteOneSchema = RouteParamsSchema;
export type ForumAnswerCommentDeleteOneI = z.infer<typeof ForumAnswerCommentDeleteOneSchema>;
