import { z } from 'zod';

export const ForumExploreSchema = z.object({
  days: z.number().min(1).max(30).default(7).optional(),
  limit: z.number().min(1).max(20).default(5).optional(),
  equipmentFilter: z
    .object({
      categoryId: z.string().optional(),
      manufacturerId: z.string().optional(),
      modelId: z.string().optional(),
    })
    .optional(),
});

export type ForumExploreOptions = z.infer<typeof ForumExploreSchema>;
