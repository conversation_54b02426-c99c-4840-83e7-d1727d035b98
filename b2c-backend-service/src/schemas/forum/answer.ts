import { AnswerStatusE } from '@consts/forum/answer';
import { CursorPaginationSchema, UUIDSchema } from '@schemas/common/common';
import z from 'zod';
import { ForumMediaCreateItemSchema } from './common';
import { FORUM_MAX_NO_OF_FILES } from '@consts/forum/question';

export const ForumAnswerCreateOneSchema = z.object({
  text: z.string().min(1).max(2000),
  questionId: UUIDSchema,
  files: z.array(ForumMediaCreateItemSchema).max(FORUM_MAX_NO_OF_FILES).optional().nullable(),
});

export type ForumAnswerCreateOneI = z.infer<typeof ForumAnswerCreateOneSchema>;

export const ForumAnswerFetchManySchema = CursorPaginationSchema.extend({
  questionId: UUIDSchema,
});

export type ForumAnswerFetchManyI = z.infer<typeof ForumAnswerFetchManySchema>;

export const AnswerUpdateStatusSchema = z.object({
  id: UUIDSchema,
  status: AnswerStatusE,
});

export type AnswerUpdateStatusI = z.infer<typeof AnswerUpdateStatusSchema>;

export const SlugParamSchema = z.object({
  slug: z.string().min(3),
});
