import { DBDataTypeE } from '@consts/common/data';
import { EquipmentCategoryR } from '@consts/common/regex/regex';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const EquipmentCategoryFetchForClientSchema = PaginationSchema.merge(
  z.object({
    search: z
      .string()
      .min(0)
      .max(100)
      .regex(EquipmentCategoryR)
      .transform((data) => data.trim().toLowerCase()),
    isEmptyAllowed: z.boolean().optional(),
  }),
).superRefine((data, ctx) => {
  if (!data?.search?.length && !data.isEmptyAllowed) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "search can't be empty" });
  }
});
export type EquipmentCategoryFetchForClientI = z.infer<typeof EquipmentCategoryFetchForClientSchema>;

export const EquipmentCategoryFetchsertSchema = z.object({
  name: z.string().min(2).max(100).regex(EquipmentCategoryR),
  hasFuelType: z.boolean(),
});
export type EquipmentCategoryFetchsertI = z.infer<typeof EquipmentCategoryFetchsertSchema>;

export const EquipmentCategoryIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type EquipmentCategoryIdClientI = z.infer<typeof EquipmentCategoryIdClientSchema>;
