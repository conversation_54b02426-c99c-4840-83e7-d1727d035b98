import { z } from 'zod';
import { ShipCallSignSchema, ShipImoClientSchema, ShipMmsiSchema, ShipNameSchema } from './ship';
import { CountryIso2Schema, IdTypeSchema, PaginationSchema, YearSchema } from '@schemas/common/common';
import { ContributionParamsSchema } from '@schemas/common/contribution';
import { ShipContributionLabelE, ShipContributionLabelI } from '@consts/ship/contribution';
import { PortNameSchema } from '@schemas/port/common';

export const ShipContributionFetchOneParamsSchema = ShipImoClientSchema;
export type ShipContributionFetchOneParamsI = z.infer<typeof ShipContributionFetchOneParamsSchema>;

export const ShipContributionUpsertOneParamsSchema = ShipImoClientSchema.merge(
  z.object({
    contributions: z.array(ContributionParamsSchema),
  }),
).superRefine((data, ctx) => {
  data.contributions.forEach((contributionItem) => {
    const { error: keyError } = ShipContributionLabelE.safeParse(contributionItem.label);
    if (keyError) {
      keyError.errors.forEach((errorItem) => {
        ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
      });
    } else {
      switch (contributionItem.label as ShipContributionLabelI) {
        case 'mmsi': {
          const { error: valueError } = ShipMmsiSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'callSign': {
          const { error: valueError } = ShipCallSignSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'name': {
          const { error: valueError } = ShipNameSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'flagCountryIso2': {
          const { error: valueError } = CountryIso2Schema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'generalVesselType': {
          const { error: valueError } = IdTypeSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'otherVesselType': {
          const { error: valueError } = IdTypeSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'portOfRegistry': {
          const { error: valueError } = PortNameSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'yearBuilt': {
          const { error: valueError } = YearSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
      }
    }
  });
});
export type ShipContributionUpsertOneParamsI = z.infer<typeof ShipContributionUpsertOneParamsSchema>;

export const ShipImageFetchForClientParamsSchema = PaginationSchema.merge(ShipImoClientSchema);
export type ShipImageFetchForClientParamsI = z.infer<typeof ShipImageFetchForClientParamsSchema>;
