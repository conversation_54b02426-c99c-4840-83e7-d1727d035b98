import { DBDataTypeE } from '@consts/common/data';
import { EquipmentManufacturerR } from '@consts/common/regex/regex';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const EquipmentManufacturerFetchForClientSchema = PaginationSchema.merge(
  z.object({
    search: z
      .string()
      .min(0)
      .max(100)
      .regex(EquipmentManufacturerR)
      .transform((data) => data.trim().toLowerCase()),
  }),
);
export type EquipmentManufacturerFetchForClientI = z.infer<typeof EquipmentManufacturerFetchForClientSchema>;

export const EquipmentManufacturerFetchsertSchema = z.object({
  name: z.string().min(2).max(100).regex(EquipmentManufacturerR),
});
export type EquipmentManufacturerFetchsertI = z.infer<typeof EquipmentManufacturerFetchsertSchema>;

export const EquipmentManufacturerIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type EquipmentManufacturerIdClientI = z.infer<typeof EquipmentManufacturerIdClientSchema>;
