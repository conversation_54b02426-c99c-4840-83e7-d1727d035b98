import { CertificateCourseNameR } from '@consts/common/regex/regex';
import { z } from 'zod';
import { PaginationSchema } from '../common/common';
import { CertificateCourseTypeE } from '@consts/company/certificateCourse';

export const CertificateCourseOptionsFetchSchema = PaginationSchema.extend({
  search: z.string().min(1).max(150).regex(CertificateCourseNameR),
  type: CertificateCourseTypeE,
});
export type CertificateCourseModuleFetchParamsI = z.infer<typeof CertificateCourseOptionsFetchSchema>;

export const CertificateCourseNameSchema = z.object({
  name: z.string().min(2).max(150).regex(CertificateCourseNameR),
  type: CertificateCourseTypeE,
});
export type CertificateCourseNameI = z.infer<typeof CertificateCourseNameSchema>;
