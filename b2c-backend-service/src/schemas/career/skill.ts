import { DBDataTypeE, OprTypeE } from '@consts/common/data';
import { SkillCategoryE } from '@consts/company/skill';
import { IdTypeSchema, ProfileIdPaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const ProfileSkillFetchForExternalClientSchema = ProfileIdPaginationSchema.merge(
  z.object({
    category: SkillCategoryE,
  }),
);
export type ProfileSkillFetchForExternalClientI = z.infer<typeof ProfileSkillFetchForExternalClientSchema>;

export const ProfileSkillPostBodyItemSchema = z.object({
  id: UUIDSchema,
  category: SkillCategoryE,
  dataType: DBDataTypeE,
});
export type ProfileSkillPostBodyItemI = z.infer<typeof ProfileSkillPostBodyItemSchema>;

export const ProfileSkillPostBodySchema = z.array(ProfileSkillPostBodyItemSchema);
export type ProfileSkillPostBodyI = z.infer<typeof ProfileSkillPostBodySchema>;

export const ProfileSkillDeleteBodyItemSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type ProfileSkillDeleteBodyItemI = z.infer<typeof ProfileSkillDeleteBodyItemSchema>;

export const ProfileSkillDeleteBodySchema = z.object({ skills: z.array(ProfileSkillDeleteBodyItemSchema) });

export type ProfileSkillDeleteBodyI = z.infer<typeof ProfileSkillDeleteBodySchema>;

export const CRUDExperienceSkillSchema = IdTypeSchema.extend({
  opr: OprTypeE,
});
export type CRUDExperienceSkillI = z.infer<typeof CRUDExperienceSkillSchema>;

export const CRUDExperienceShipSkillsSchema = z.object({
  skills: z.array(CRUDExperienceSkillSchema),
  experienceShipId: UUIDSchema,
});

export type CRUDExperienceShipSkillsI = z.infer<typeof CRUDExperienceShipSkillsSchema>;
