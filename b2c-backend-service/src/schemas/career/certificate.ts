import { CertificateCourseTypeE } from '@consts/company/certificateCourse';
import { OprTypeE } from '@consts/common/data';
import { IdTypeSchema, ProfileIdPaginationSchema } from '@schemas/common/common';
import { isEmpty } from '@utils/data/object';
import { z } from 'zod';

export const ProfileCertificatePostBodySchema = z
  .object({
    institute: IdTypeSchema,
    certificateCourse: IdTypeSchema,
    fileUrl: z.string().url().optional(),
    fromDate: z.coerce.date(),
    untilDate: z.union([z.string().transform((data) => new Date(data)), z.null()]),
    skills: z.array(IdTypeSchema).nullable(),
  })
  .superRefine((data, ctx) => {
    if (data?.untilDate && data.fromDate > data.untilDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than To date",
        path: ['fromDate'],
      });
    }
  });
export type ProfileCertificatePostBodyI = z.infer<typeof ProfileCertificatePostBodySchema>;

export const ProfileCertificateFetchForClientSchema = ProfileIdPaginationSchema.merge(
  z.object({
    type: CertificateCourseTypeE,
  }),
);
export type ProfileCertificateFetchForClientI = z.infer<typeof ProfileCertificateFetchForClientSchema>;

export const ProfileCertificatePatchBodySchema = z
  .object({
    institute: IdTypeSchema.optional(),
    certificateCourse: IdTypeSchema.optional(),
    fromDate: z.coerce.date().optional(),
    untilDate: z.coerce.date().optional().nullable(),
    skillsToAdd: z.array(IdTypeSchema).nullable().optional(),
    skillsToDelete: z.array(IdTypeSchema).nullable().optional(),
    file: z
      .object({
        opr: OprTypeE,
        fileUrl: z.string().url().optional(),
      })
      .optional(),
  })
  .superRefine((data, ctx) => {
    if (isEmpty(data)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'At least one attribute is required',
      });
    } else if (data?.fromDate && data?.untilDate && data.fromDate > data.untilDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than Until date",
        path: ['fromDate'],
      });
    }
    if (data?.file?.opr && (data.file.opr === 'CREATE' || data.file.opr === 'UPDATE')) {
      if (!data?.file?.fileUrl) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'fileUrl is required',
          path: ['file', 'fileUrl'],
        });
      }
    }

    if (data?.file?.opr === 'DELETE') {
      if (data.file.fileUrl !== undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'DELETE operation does not allow fileUrl field',
          path: ['file', 'fileUrl'],
        });
      }
    }
  });
export type ProfileCertificatePatchBodyI = z.infer<typeof ProfileCertificatePatchBodySchema>;
