import { OprTypeE } from '@consts/common/data';
import { CargoNameR, TextAlphaNumericSpecialCharR } from '@consts/common/regex/regex';
import {
  IdTypeSchema,
  NameSchema,
  ProfileIdPaginationSchema,
  RouteParamsSchema,
  TextAlphaNumericSpecialCharSchema,
  UUIDSchema,
} from '@schemas/common/common';
import { Decimal10_2Schema, Decimal8_2Schema } from '@schemas/common/number';
import { DepartmentIdClientSchema } from '@schemas/company/department';
import { DesignationIdClientSchema } from '@schemas/company/designation';
import { EntityIdClientSchema } from '@schemas/company/entity';
import { EquipmentCategoryIdClientSchema } from '@schemas/ship/equipmentCategory';
import { FuelTypeIdClientSchema } from '@schemas/ship/fuelType';
import { ShipImoClientSchema, ShipNameSchema } from '@schemas/ship/ship';

import { isEmpty, isNumber } from '@utils/data/object';
import { isFilledString } from '@utils/data/string';
import { z } from 'zod';
import { CRUDExperienceSkillSchema } from './skill';

export const ExperienceFetchForClientParamsSchema = ProfileIdPaginationSchema;
export type ExperienceFetchForClientParamsI = z.infer<typeof ExperienceFetchForClientParamsSchema>;

export const ExperienceFetchOneForExternalClientParamsSchema = RouteParamsSchema;
export type ExperienceOneForExternalClientParamsI = z.infer<typeof ExperienceFetchOneForExternalClientParamsSchema>;
export const ExperienceFuelTypeSchema = z
  .object({
    opr: OprTypeE,
    id: UUIDSchema.optional(),
    fuelType: FuelTypeIdClientSchema.optional().nullable(),
  })
  .superRefine((data, ctx) => {
    switch (data.opr) {
      case 'CREATE': {
        if (isEmpty(data?.fuelType)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'fuelType is required',
          });
        }
        break;
      }
      case 'UPDATE': {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Update is invalid for a fuelType',
        });
        break;
      }
      case 'DELETE': {
        if (isEmpty(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
      case 'NESTED_OPR': {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "fuelType can't have a nested data",
        });
        break;
      }
    }
  });

export type ExperienceFuelTypeI = z.infer<typeof ExperienceFuelTypeSchema>;
export const ExperienceEquipmentCategorySchema = z
  .object({
    opr: OprTypeE,
    id: UUIDSchema.optional(),
    equipmentCategory: EquipmentCategoryIdClientSchema.optional(),
    manufacturerName: NameSchema.optional(),
    model: NameSchema.optional(),
    powerCapacity: Decimal8_2Schema.optional().nullable(),
    details: TextAlphaNumericSpecialCharSchema.optional(),
    fuelTypes: z.array(ExperienceFuelTypeSchema).optional(),
  })
  .superRefine((data, ctx) => {
    switch (data.opr) {
      case 'CREATE': {
        if (isEmpty(data.equipmentCategory)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'equipmentCategory is required',
          });
        } else if (!isFilledString(data.manufacturerName)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'manufacturerName is required',
          });
        } else if (!isFilledString(data.model)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'model is required',
          });
        }
        break;
      }
      case 'UPDATE': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
      case 'DELETE': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
      case 'NESTED_OPR': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
    }
  });

export type ExperienceEquipmentCategoryI = z.infer<typeof ExperienceEquipmentCategorySchema>;
export const CargoNameSchema = z.string().min(1).max(100).regex(CargoNameR);

export const ExperienceCargoSchema = z
  .object({
    opr: OprTypeE,
    id: UUIDSchema.optional(),
    name: CargoNameSchema.optional(),
    description: TextAlphaNumericSpecialCharSchema.max(150).optional(),
    fromDate: z.coerce.date().optional(),
    toDate: z.coerce.date().optional().nullable(),
    fuelTypes: z.array(ExperienceFuelTypeSchema).optional(),
  })
  .superRefine((data, ctx) => {
    switch (data.opr) {
      case 'CREATE': {
        if (!isFilledString(data?.name)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'name is required',
          });
        } else if (!data?.fromDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'fromDate is required',
          });
        } else if (data?.toDate && data?.fromDate > data?.toDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "fromDate can't be greater than toDate",
          });
        }
        break;
      }
      case 'UPDATE': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        } else if (data?.fromDate && data?.toDate && data?.fromDate > data?.toDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "fromDate can't be greater than toDate",
          });
        }
        break;
      }
      case 'DELETE': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
      case 'NESTED_OPR': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
    }
  });

export type ExperienceCargoI = z.infer<typeof ExperienceCargoSchema>;

export const ExperienceShipSchema = z
  .object({
    opr: OprTypeE,
    id: UUIDSchema.optional(),
    ship: ShipImoClientSchema.optional(),
    name: ShipNameSchema.optional(),
    sizeGt: Decimal10_2Schema.optional(),
    powerKw: Decimal8_2Schema.optional(),
    dwt: z.number().int().optional(),
    details: z.string().min(1).regex(TextAlphaNumericSpecialCharR).max(255).optional().nullable(),
    fromDate: z.coerce.date().optional(),
    toDate: z.coerce.date().nullable().optional(),
    department: DepartmentIdClientSchema.optional(),
    subVesselType: IdTypeSchema.optional(),
    skills: z.array(CRUDExperienceSkillSchema).optional(),
    equipmentCategories: z.array(ExperienceEquipmentCategorySchema).optional(),
    cargos: z.array(ExperienceCargoSchema).optional(),
  })
  .superRefine((data, ctx) => {
    switch (data.opr) {
      case 'CREATE': {
        if (isEmpty(data.ship)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'ship is required',
          });
        } else if (!isNumber(data.sizeGt)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'sizeGt is required',
          });
        } else if (!data?.fromDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'fromDate is required',
          });
        } else if (data?.toDate && data?.fromDate > data?.toDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "fromDate can't be greater than toDate",
          });
        } else if (isEmpty(data.department)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'department is required',
          });
        } else if (isEmpty(data.subVesselType)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'subVesselType is required',
          });
        }
        break;
      }
      case 'UPDATE': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        } else if (data?.fromDate && data?.toDate && data?.fromDate > data?.toDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "fromDate can't be greater than toDate",
          });
        }
        break;
      }
      case 'DELETE': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
      case 'NESTED_OPR': {
        if (!isFilledString(data?.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
    }
  });
export type ExperienceShipI = z.infer<typeof ExperienceShipSchema>;
export const ExperienceDesignationSchema = z
  .object({
    opr: OprTypeE,
    id: UUIDSchema.optional(),
    designation: DesignationIdClientSchema.optional(),
    fromDate: z.coerce.date().optional(),
    toDate: z.coerce.date().nullable().optional(),
    ships: z.array(ExperienceShipSchema).optional(),
  })
  .superRefine((data, ctx) => {
    switch (data.opr) {
      case 'CREATE': {
        if (isEmpty(data.designation)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'designation is required',
          });
        } else if (!data?.fromDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'fromDate is required',
          });
        } else if (data?.toDate && data?.fromDate > data?.toDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "fromDate can't be greater than toDate",
          });
        }
        break;
      }
      case 'UPDATE': {
        if (!isFilledString(data.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        } else if (data?.fromDate && data?.toDate && data?.fromDate > data?.toDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "fromDate can't be greater than toDate",
          });
        }
        break;
      }
      case 'DELETE': {
        if (!isFilledString(data.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
      case 'NESTED_OPR': {
        if (!isFilledString(data.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
    }
  });
export type ExperienceDesignationI = z.infer<typeof ExperienceDesignationSchema>;
export const ExperienceItemSchema = z
  .object({
    opr: OprTypeE,
    id: UUIDSchema.optional(),
    entity: EntityIdClientSchema.optional(),
    designations: z.array(ExperienceDesignationSchema).optional(),
  })
  .superRefine((data, ctx) => {
    switch (data.opr) {
      case 'CREATE': {
        if (isEmpty(data.entity)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'entity is required',
          });
        }
        break;
      }
      case 'UPDATE': {
        if (!isFilledString(data.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
      case 'DELETE': {
        if (!isFilledString(data.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
      case 'NESTED_OPR': {
        if (!isFilledString(data.id)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'id is required',
          });
        }
        break;
      }
    }
  });
export type ExperienceItemI = z.infer<typeof ExperienceItemSchema>;

export const ExperienceModuleCreateOneParamsSchema = z.array(ExperienceItemSchema);
export type ExperienceModuleCreateOneParamsI = z.infer<typeof ExperienceModuleCreateOneParamsSchema>;
