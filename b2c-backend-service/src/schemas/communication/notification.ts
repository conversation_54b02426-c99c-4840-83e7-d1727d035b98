import { ObjectIdSchema, ProfileIdQuerySchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';
import { KafkaTopicE } from '@consts/kafka';
import { FirebaseFCMTopicSchema } from '@consts/communication/common';
import { NotificationTypeE } from '@consts/notification/notification';

export const ActorProfileNameSchema = z
  .string()
  .trim()
  .transform((data) => data?.split(' ')?.[0]?.substring(0, 15) || 'Unknown');
export const PostTextSchema = z
  .string()
  .trim()
  .transform((data) => `${data?.length > 20 ? `${data?.substring(0, 16)}...` : data}`);

export const NotificationFetchManyItemSchema = z.object({
  actorProfileId: UUIDSchema.optional(),
  postId: UUIDSchema.optional(),
});
export type NotificationFetchManyItemI = z.infer<typeof NotificationFetchManyItemSchema>;

export const NotificationFetchManySchema = z.object({
  items: z.array(NotificationFetchManyItemSchema),
});
export type NotificationFetchManyI = z.infer<typeof NotificationFetchManySchema>;

// export const NotificationCreateOneSchema = z.object({
//   actorProfileId: UUIDSchema.optional(),
//   actorProfileName: ActorProfileNameSchema.optional(),
//   commentId: UUIDSchema.optional(),
//   message: UUIDSchema.optional(),
//   parentCommentId: UUIDSchema.optional(),
//   postId: UUIDSchema.optional(),
//   postText: PostTextSchema.optional(),
//   requestId: UUIDSchema.optional(),
//   title: UUIDSchema.optional(),
//   type: NotificationTypeE,
// }).superRefine((data, ctx) => {
//   switch (data.type) {
//     case 'COMMENT': {
//       if (!(data.actorProfileId && data.actorProfileName && data.commentId && data.postId)) {
//         ctx.addIssue(
//           {
//             code: z.ZodIssueCode.custom,
//             message:  "Comment notification's data is invalid"
//           }
//         )
//       }
//     }
//     case 'FOLLOWER': {
//       if (!(data.actorProfileId && data.actorProfileName)) {
//         ctx.addIssue(
//           {
//             code: z.ZodIssueCode.custom,
//             message:  "Follower notification's data is invalid"
//           }
//         )
//       }
//     }
//     case 'LIKE': {
//       if (!(data.actorProfileId && data.actorProfileName && data.postId && data.postText)) {
//         ctx.addIssue(
//           {
//             code: z.ZodIssueCode.custom,
//             message:  "Comment notification's data is invalid"
//           }
//         )
//       }
//     }
//     case 'REPLY': {
//       if (!(data.actorProfileId && data.actorProfileName && data.commentId && data.parentCommentId && data.postId)) {
//         ctx.addIssue(
//           {
//             code: z.ZodIssueCode.custom,
//             message:  "Reply notification's data is invalid"
//           }
//         )
//       }
//     }
//     case 'REQUEST': {
//       if (!(data.actorProfileId && data.actorProfileName && data.requestId)) {
//         ctx.addIssue(
//           {
//             code: z.ZodIssueCode.custom,
//             message:  "Request notification's data is invalid"
//           }
//         )
//       }
//     }
//     default: {
//       ctx.addIssue(
//         {
//           code: z.ZodIssueCode.custom,
//           message: 'type is invalid',
//         }
//       )
//       break
//     }
//   }
// });
// export type NotificationCreateOneI = z.infer<typeof NotificationCreateOneSchema>;

export const NotificationPostSchema = z.object({
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
  postId: UUIDSchema,
});
export type NotificationPostI = z.infer<typeof NotificationPostSchema>;

export const NotificationLikeSchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.LIKE]),
  postText: PostTextSchema,
});
export type NotificationLikeI = z.infer<typeof NotificationLikeSchema>;

export const NotificationCommentSchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.COMMENT]),
  commentId: UUIDSchema,
});
export type NotificationCommentI = z.infer<typeof NotificationCommentSchema>;

export const NotificationReplySchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.REPLY]),
  commentId: UUIDSchema,
  parentCommentId: UUIDSchema,
});

export type NotificationReplyI = z.infer<typeof NotificationReplySchema>;

export const NotificationUpdateReadSchema = ProfileIdQuerySchema.extend({
  type: z.enum([NotificationTypeE.Enum.UPDATE_READ]),
  ids: z.array(ObjectIdSchema).min(1),
});
export type NotificationUpdateReadI = z.infer<typeof NotificationUpdateReadSchema>;

export const NotificationFollowerSchema = z.object({
  type: z.enum([NotificationTypeE.Enum.FOLLOWER]),
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
});
export type NotificationFollowerI = z.infer<typeof NotificationFollowerSchema>;

export const NotificationRequestReceivedSchema = z.object({
  type: z.enum([NotificationTypeE.Enum.REQUEST_RECEIVED]),
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
  requestId: UUIDSchema,
});
export type NotificationRequestReceivedI = z.infer<typeof NotificationRequestReceivedSchema>;

export const NotificationRequestAcceptedSchema = NotificationRequestReceivedSchema.extend({
  type: z.enum([NotificationTypeE.Enum.REQUEST_ACCEPTED]),
});
export type NotificationRequestAcceptedI = z.infer<typeof NotificationRequestAcceptedSchema>;

export const NotificationCreateSchema = z
  .object({})
  .or(NotificationCommentSchema)
  .or(NotificationFollowerSchema)
  .or(NotificationLikeSchema)
  .or(NotificationReplySchema)
  .or(NotificationUpdateReadSchema)
  .or(NotificationRequestReceivedSchema)
  .or(NotificationRequestAcceptedSchema);
export type NotificationCreateI = z.infer<typeof NotificationCreateSchema>;

export const NotificationCreateOneSchema = z
  .object({
    actorProfileId: UUIDSchema,
    actorProfileName: ActorProfileNameSchema,
    commentId: UUIDSchema,
    ids: z.array(ObjectIdSchema).min(1),
    parentCommentId: UUIDSchema,
    postId: UUIDSchema,
    postText: PostTextSchema,
    profileId: UUIDSchema.optional(),
    requestId: UUIDSchema.optional(),
    profileIds: z.array(UUIDSchema).min(1).optional(),
    type: NotificationTypeE,
    topic: KafkaTopicE,
    firebaseTopic: FirebaseFCMTopicSchema.optional(),
  })
  .partial();
export type NotificationCreateOneI = z.infer<typeof NotificationCreateOneSchema>;
