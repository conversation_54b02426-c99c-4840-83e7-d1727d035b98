import { z } from 'zod';
import { UnLocodeSchema } from './common';

export const ScrapBookPostCreateOneParamsSchema = z.object({
  text: z.string().min(1).max(1000),
  portUnLocode: UnLocodeSchema,
});
export type ScrapBookPostCreateOneParamsI = z.infer<typeof ScrapBookPostCreateOneParamsSchema>;

export const ScrapBookPostFetchForClientParamsSchema = z.object({
  portUnLocode: UnLocodeSchema,
});
export type ScrapBookPostFetchForClientParamsI = z.infer<typeof ScrapBookPostFetchForClientParamsSchema>;
