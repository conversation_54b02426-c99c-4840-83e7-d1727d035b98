import { ScrapBookReactionTypeE } from '@consts/port/reaction';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const ScrapBookReactionFetchForClientParamsSchema = PaginationSchema.merge(
  z.object({
    scrapBookPostId: UUIDSchema,
  }),
);
export type ScrapBookReactionFetchForClientParamsI = z.infer<typeof ScrapBookReactionFetchForClientParamsSchema>;

export const ScrapBookReactionCreateOneParamsSchema = z.object({
  scrapBookPostId: UUIDSchema,
  reactionType: ScrapBookReactionTypeE,
});
export type ScrapBookReactionpUsertOneParamsI = z.infer<typeof ScrapBookReactionCreateOneParamsSchema>;

export const ScrapBookReactionPostIdSchema = z.object({
  scrapBookPostId: UUIDSchema,
});
export type ScrapBookReactionPostIdParamsI = z.infer<typeof ScrapBookReactionPostIdSchema>;
