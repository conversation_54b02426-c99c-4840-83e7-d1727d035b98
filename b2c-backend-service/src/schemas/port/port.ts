import { DBDataTypeE } from '@consts/common/data';
import { PortR } from '@consts/common/regex/regex';
import { CountryIso2Schema, IdTypeSchema, PaginationSchema } from '@schemas/common/common';
import {
  LatitudeSchema,
  LongitudeSchema,
  MaxAirDraughtSchema,
  MaxDeadweightSchema,
  MaxDraughtSchema,
  MaxLengthSchema,
  NoOfBerthsSchema,
  NoOfTerminalsSchema,
  PortNameSchema,
  UnLocodeSchema,
} from '@schemas/port/common';
import { z } from 'zod';

export const PortUnLocodeTypeSchema = z.object({
  unLocode: UnLocodeSchema,
  dataType: DBDataTypeE,
});

export type PortUnLocodeTypeI = z.infer<typeof PortUnLocodeTypeSchema>;
export const PortFetchSchema = PaginationSchema.extend({
  search: z.string().min(1).max(100).regex(PortR),
});
export type PortFetchI = z.infer<typeof PortFetchSchema>;

export const PortModuleFetchsertParamsSchema = z.object({
  unLocode: UnLocodeSchema,
  name: PortNameSchema,
  city: IdTypeSchema,
  countryIso2: CountryIso2Schema,
  timezoneIso2: CountryIso2Schema.optional(),
  latitude: LatitudeSchema.optional(),
  longitude: LongitudeSchema.optional(),
  noOfTerminals: NoOfTerminalsSchema.optional(),
  noOfBerths: NoOfBerthsSchema.optional(),
  maxDraught: MaxDraughtSchema.optional(),
  maxDeadweight: MaxDeadweightSchema.optional(),
  maxLength: MaxLengthSchema.optional(),
  maxAirDraught: MaxAirDraughtSchema.optional(),
});
export type PortModuleFetchsertParamsI = z.infer<typeof PortModuleFetchsertParamsSchema>;
