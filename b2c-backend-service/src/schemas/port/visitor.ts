import { PaginationSchema } from '@schemas/common/common';
import { z } from 'zod';
import { UnLocodeSchema, UnLocodeTypeSchema } from './common';

export const PortVisitorCreateOneParamsSchema = z.object({
  portUnLocode: UnLocodeSchema,
});
export type PortVisitorCreateOneParamsI = z.infer<typeof PortVisitorCreateOneParamsSchema>;

export const PortVisitorFetchForClientParamsSchema = PaginationSchema.merge(UnLocodeTypeSchema);
export type PortVisitorFetchForClientParamsI = z.infer<typeof PortVisitorFetchForClientParamsSchema>;

export const PortVisitorDeleteOneParamsSchema = PortVisitorCreateOneParamsSchema;
export type PortVisitorDeleteOneParamsI = z.infer<typeof PortVisitorDeleteOneParamsSchema>;
