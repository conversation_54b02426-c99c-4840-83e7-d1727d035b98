import { PortContributionKeyE, PortContributionKeyI } from '@consts/port/contribution';
import { CountryIso2Schema, IdTypeSchema, PaginationSchema } from '@schemas/common/common';
import { ContributionParamsSchema } from '@schemas/common/contribution';
import {
  LatitudeSchema,
  LongitudeSchema,
  MaxAirDraughtSchema,
  MaxDeadweightSchema,
  MaxDraughtSchema,
  MaxLengthSchema,
  NoOfBerthsSchema,
  NoOfTerminalsSchema,
  PortNameSchema,
} from '@schemas/port/common';
import { z } from 'zod';
import { PortUnLocodeTypeSchema } from './port';

export const PortContributionUpsertOneParamsSchema = PortUnLocodeTypeSchema.merge(
  z.object({
    contributions: z.array(ContributionParamsSchema),
  }),
).superRefine((data, ctx) => {
  data.contributions.forEach((contributionItem) => {
    const { error: keyError } = PortContributionKeyE.safeParse(contributionItem.label);
    if (keyError) {
      keyError.errors.forEach((errorItem) => {
        ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
      });
    } else {
      switch (contributionItem.label as PortContributionKeyI) {
        case 'name': {
          const { error: valueError } = PortNameSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'cityId': {
          const { error: valueError } = IdTypeSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'countryIso2': {
          const { error: valueError } = CountryIso2Schema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'timezoneIso2': {
          const { error: valueError } = CountryIso2Schema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'latitude': {
          const { error: valueError } = LatitudeSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'longitude': {
          const { error: valueError } = LongitudeSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'noOfTerminals': {
          const { error: valueError } = NoOfTerminalsSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'noOfBerths': {
          const { error: valueError } = NoOfBerthsSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'maxDraught': {
          const { error: valueError } = MaxDraughtSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'maxDeadweight': {
          const { error: valueError } = MaxDeadweightSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'maxLength': {
          const { error: valueError } = MaxLengthSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
        case 'maxAirDraught': {
          const { error: valueError } = MaxAirDraughtSchema.safeParse(contributionItem.value);
          if (valueError) {
            valueError.errors.forEach((errorItem) => {
              ctx.addIssue({ ...errorItem, path: ['contributions', ...errorItem.path] });
            });
          }
          break;
        }
      }
    }
  });
});
export type PortContributionUpsertOneParamsI = z.infer<typeof PortContributionUpsertOneParamsSchema>;

export const PortContributionFetchOneParamsSchema = PortUnLocodeTypeSchema;
export type PortContributionFetchOneParamsI = z.infer<typeof PortContributionFetchOneParamsSchema>;

export const PortImageFetchForClientParamsSchema = PaginationSchema.merge(PortUnLocodeTypeSchema);
export type PortImageFetchForClientParamsI = z.infer<typeof PortImageFetchForClientParamsSchema>;
