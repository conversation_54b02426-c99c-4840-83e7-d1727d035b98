import { PaginationSchema } from '@schemas/common/common';
import { z } from 'zod';
import { LeaderboardDurationE, LeaderboardTypeE } from '@consts/leaderboard/leaderboard';

export const LeaderboardFetchParamsSchema = PaginationSchema.merge(
  z.object({
    duration: LeaderboardDurationE,
    type: LeaderboardTypeE,
  }),
);

export type LeaderboardFetchParamsI = z.infer<typeof LeaderboardFetchParamsSchema>;
