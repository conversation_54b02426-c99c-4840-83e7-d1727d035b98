import z from 'zod';
import { PolicyTypeE } from '@consts/privacyPolicy/privacyPolicy';

export const PrivacyPolicyFetchSchema = z
  .object({
    type: PolicyTypeE,
  })
  .strict();
export type PrivacyPolicyFetchI = z.infer<typeof PrivacyPolicyFetchSchema>;

export const PrivacyPolicyAcceptSchema = z.object({
  type: PolicyTypeE,
});
export type PrivacyPolicyAcceptI = z.infer<typeof PrivacyPolicyAcceptSchema>;
