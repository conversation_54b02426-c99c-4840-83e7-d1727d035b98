import { FastifyInstance } from 'fastify';
import masterRoutes from './v1/master';
import userRoutes from './v1/user';
import feedRoutes from './v1/feed';
import careerRoutes from './v1/career';
import companyRoutes from './v1/company';
import portRoutes from './v1/port';
import networkRoutes from './v1/network';
import { authTokenMiddleware } from 'middlewares/authMiddleware';
import storageRoutes from './v1/storage';
import documentRoutes from './v1/document';
import shipRoutes from './v1/ship';
import logoutRoutes from './v1/auth/logout';
import communicationRoutes from './v1/communication';
import forumRoutes from './v1/forum';
import secureAuthRoutes from './v1/secureAuth';
import privacyPolicyAcceptRoutes from './v1/privacyPolicy/accept';
import rewardRoutes from './v1/reward/profile';
import leaderboardRoutes from './v1/leaderboard';
import addressRawDataRoutes from './v1/rawData/addressRawData';
import announcementRoutes from './v1/announcement';


const secureRoutes = (fastify: FastifyInstance): void => {
  fastify.addHook('preHandler', authTokenMiddleware);
  fastify.register(secureAuthRoutes);
  fastify.register(masterRoutes);
  fastify.register(networkRoutes);
  fastify.register(userRoutes);
  fastify.register(feedRoutes);
  fastify.register(careerRoutes);
  fastify.register(companyRoutes);
  fastify.register(communicationRoutes);
  fastify.register(portRoutes);
  fastify.register(storageRoutes);
  fastify.register(documentRoutes);
  fastify.register(shipRoutes);
  fastify.register(logoutRoutes);
  fastify.register(forumRoutes);
  fastify.register(privacyPolicyAcceptRoutes);
  fastify.register(rewardRoutes);
  fastify.register(leaderboardRoutes);
  fastify.register(addressRawDataRoutes);
  fastify.register(announcementRoutes);

};

export default secureRoutes;
