import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import User from '@modules/user';
import {
  CreateOnBoardingPersonalParamsI,
  CreateOnBoardingPersonalParamsSchema,
  CreateOnBoardingWorkParamsSchema,
} from '@schemas/user/profile';
import { getError } from '@utils/errors/schema';
import { FastifyInstance, FastifyReply } from 'fastify';

const onBoardingRoutes = (fastify: FastifyInstance): void => {
  fastify.post(
    '/backend/api/v1/user/profile/onboarding/personal',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data } = CreateOnBoardingPersonalParamsSchema.safeParse(request.body);
      if (error) {
        throw new AppError('PFL010', getError(error));
      }
      const result = await User.ProfileModule.upsertOnBoardingPersonal(
        request,
        data as CreateOnBoardingPersonalParamsI,
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/user/profile/onboarding/work',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data } = CreateOnBoardingWorkParamsSchema.safeParse(request.body);

      if (error) {
        throw new AppError('PFL010', error);
      }

      const result = await User.ProfileModule.upsertOnBoardingWork(request, data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default onBoardingRoutes;
