import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Career from '@modules/career';
import type { ProfileEducation } from '@prisma/postgres';
import { ProfileEducationCreateOneParamsSchema, ProfileEducationPatchBodySchema } from '@schemas/career/education';
import { ProfileIdPaginationSchema, RouteParamsSchema } from '@schemas/common/common';
import { getError } from '@utils/errors/schema';

import type { FastifyInstance, FastifyReply } from 'fastify';

const profileEducationRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/career/institutes', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ProfileIdPaginationSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PFEDU006', queryError);
    }
    const result = await Career.EducationModule.fetchForExternalClient(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/career/institute/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('PFEDU007', paramsError);
    }
    const result = await Career.EducationModule.fetchOneForInternalClient(request, paramsData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/career/institute/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('PFEDU007', paramsError);
    }
    await Career.EducationModule.deleteOne(request, paramsData as Pick<ProfileEducation, 'id'>);
    reply.status(HttpStatus.NO_CONTENT);
  });
  fastify.post('/backend/api/v1/career/institute', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = ProfileEducationCreateOneParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('PFEDU009', getError(error));
    }
    const result = await Career.EducationModule.createOne(request, data);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.patch('/backend/api/v1/career/institute/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('PFEDU007', paramsError);
    }

    const { error: bodyError, data: bodyData } = ProfileEducationPatchBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('PFEDU008', { error: bodyError });
    }
    const result = await Career.EducationModule.updateOne(request, bodyData, paramsData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default profileEducationRoutes;
