import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Career from '@modules/career';
import {
  ExperienceFetchForClientParamsSchema,
  ExperienceFetchOneForExternalClientParamsSchema,
  ExperienceModuleCreateOneParamsSchema,
} from '@schemas/career/experience';
import { FastifyInstance, FastifyReply } from 'fastify';

const experienceRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/career/profile-experiences',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = ExperienceFetchForClientParamsSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('EXP005', queryError);
      }
      const result = await Career.ExperienceModule.fetchForClient(queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/career/profile-experience/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: paramsData, error: paramsError } = ExperienceFetchOneForExternalClientParamsSchema.safeParse(
        request.params,
      );
      if (paramsError) {
        throw new AppError('EXP011', paramsError);
      }
      const result = await Career.ExperienceModule.fetchOneForExternalClient(paramsData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/career/profile-experience',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = ExperienceModuleCreateOneParamsSchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('EXP006', { error: bodyError });
      }
      const result = await Career.ExperienceModule.crudMultiple(request, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default experienceRoutes;
