import { HttpStatus } from '@consts/common/api/status';
import type { FastifyInstance, FastifyReply } from 'fastify';
import Career from '@modules/career';
import { PeopleFetchPeopleOnShipSchema } from '@schemas/career/people';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';

const peopleRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/career/people/ship', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = PeopleFetchPeopleOnShipSchema.safeParse(request.query);
    if (error) {
      throw new AppError('PPL001');
    }
    const result = await Career.PeopleModule.fetchPeopleOnShip(request, data);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/career/people/port', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = PeopleFetchPeopleOnShipSchema.safeParse(request.query);
    if (error) {
      throw new AppError('PPL001');
    }
    const result = await Career.PeopleModule.fetchPeopleOnPort(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default peopleRoutes;
