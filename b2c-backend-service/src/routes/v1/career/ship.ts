import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Career from '@modules/career';
import { RouteParamsSchema } from '@schemas/common/common';
import { FastifyInstance, FastifyReply } from 'fastify';

const experienceShipRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/career/profile-experience/ship/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data } = RouteParamsSchema.safeParse(request.params);
      if (error) {
        throw new AppError('EXP014', error);
      }
      const result = await Career.CoreShipModule.fetchShipForClient(request, data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default experienceShipRoutes;
