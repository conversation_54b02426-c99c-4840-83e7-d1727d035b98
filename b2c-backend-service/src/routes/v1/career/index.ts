import { FastifyInstance } from 'fastify';
import profileEducationRoutes from './education';
import profileCertificateRoutes from './certificate';
import profileSkillRoutes from './skill';
import experienceRoutes from './experience';
import peopleRoutes from './people';
import portVisitRoutes from './portVisit';
import experienceShipRoutes from './ship';
import experienceEquipmentCategoryRoutes from './equipmentCategory';
import experienceCargoRoutes from './cargo';

const careerRoutes = (fastify: FastifyInstance): void => {
  fastify.register(profileEducationRoutes);
  fastify.register(experienceRoutes);
  fastify.register(peopleRoutes);
  fastify.register(portVisitRoutes);
  fastify.register(profileCertificateRoutes);
  fastify.register(profileSkillRoutes);
  fastify.register(experienceShipRoutes);
  fastify.register(experienceCargoRoutes);
  fastify.register(experienceEquipmentCategoryRoutes);
};

export default careerRoutes;
