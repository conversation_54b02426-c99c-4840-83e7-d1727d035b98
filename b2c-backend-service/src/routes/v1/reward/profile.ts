import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import RewardModule from '@modules/reward';
import { RewardSchema } from '@schemas/reward/profile';

import { FastifyInstance, FastifyReply } from 'fastify';

const rewardRoutes = (fastify: FastifyInstance): void => {
  fastify.patch('/backend/api/v1/reward', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = RewardSchema.safeParse(request.body);
    if (bodyError) throw new AppError('RWD004', bodyError);
    const result = await RewardModule.RewardActionModule.assignReward(bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.delete('/backend/api/v1/reward', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = RewardSchema.safeParse(request.query);
    if (queryError) throw new AppError('RWD006', queryError);
    const result = await RewardModule.RewardActionModule.unassignReward(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default rewardRoutes;
