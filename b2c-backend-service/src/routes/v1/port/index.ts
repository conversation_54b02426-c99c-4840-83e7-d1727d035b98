import { FastifyInstance } from 'fastify';
import scrapBookPostRoutes from './scrapBook/post';
import scrapBookReactionRoutes from './scrapBook/reaction';
import scrapBookCommentRoutes from './scrapBook/comment';
import portChildRoutes from './port';
import contributionDataRoutes from './contribution/data';
import visitorRoutes from './visitor';
import contributionImageRoutes from './contribution/image';
const portRoutes = (fastify: FastifyInstance): void => {
  fastify.register(portChildRoutes);
  fastify.register(scrapBookPostRoutes);
  fastify.register(scrapBookReactionRoutes);
  fastify.register(scrapBookCommentRoutes);
  fastify.register(contributionDataRoutes);
  fastify.register(contributionImageRoutes);
  fastify.register(visitorRoutes);
};

export default portRoutes;
