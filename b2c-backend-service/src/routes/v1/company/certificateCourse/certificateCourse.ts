import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { CertificateCourseModuleFetchsertParamsI } from '@interfaces/company/certificateCourse';
import Company from '@modules/company';
import { CertificateCourseOptionsFetchSchema, CertificateCourseNameSchema } from '@schemas/company/certificateCourse';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const certificateCourseRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/company/certificate-course/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = CertificateCourseOptionsFetchSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('CRTCR005', queryError);
      }
      const result = await Company.CertificateCourseModule.fetchForClient(
        queryData.search,
        queryData.type,
        pick(queryData, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/certificate-course/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = CertificateCourseNameSchema.safeParse(request.body);

      if (bodyError) {
        throw new AppError('CRTCR006', bodyError);
      }
      const result = await Company.CertificateCourseModule.fetchsert(
        bodyData as CertificateCourseModuleFetchsertParamsI,
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default certificateCourseRoutes;
