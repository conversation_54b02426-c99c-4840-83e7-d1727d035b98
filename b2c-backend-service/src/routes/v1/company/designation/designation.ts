import { HttpStatus } from '@consts/common/api/status';
import AppError from '@classes/AppError';
import { DesignationModuleFetchsertParamsI } from '@interfaces/company/designation';
import Company from '@modules/company';
import { DesignationOptionsFetchSchema, DesignationNameSchema } from '@schemas/company/designation';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const designationRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/company/designation/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = DesignationOptionsFetchSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('DSG006', { error: queryError });
      }
      const result = await Company.DesignationModule.fetchForClient(
        queryData.search,
        pick(queryData, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/designation/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = DesignationNameSchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('DSG005', { error: bodyError });
      }
      const result = await Company.DesignationModule.fetchsert(bodyData as DesignationModuleFetchsertParamsI);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default designationRoutes;
