import { HttpStatus } from '@consts/common/api/status';
import { DepartmentOptionsFetchSchema, DepartmentNameSchema } from '@schemas/company/department';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { DepartmentModuleFetchsertParamsI } from '@interfaces/company/department';
import Company from '@modules/company';
import AppError from '@classes/AppError';

const departmentRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/company/department/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = DepartmentOptionsFetchSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('DEP006', { error: queryError });
      }

      const result = await Company.DepartmentModule.fetchForClient(
        queryData.search,
        pick(queryData, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/department/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = DepartmentNameSchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('DEP005', { error: bodyError });
      }
      const result = await Company.DepartmentModule.fetchsert(bodyData as DepartmentModuleFetchsertParamsI);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default departmentRoutes;
