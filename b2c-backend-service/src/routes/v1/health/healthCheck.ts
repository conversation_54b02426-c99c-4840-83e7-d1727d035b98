import { HttpStatus } from '@consts/common/api/status';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const healthCheckRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/health', {}, async (_request: FastifyRequest, reply: FastifyReply) => {
    reply.status(HttpStatus.OK).send({ status: 'API is UP' });
  });
};

export default healthCheckRoutes;
