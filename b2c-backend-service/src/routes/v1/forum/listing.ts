import type { FastifyInstance, FastifyReply } from 'fastify';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { ForumQuestionListingSchema } from '@schemas/forum/question';

const listing = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/questions-listing', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ForumQuestionListingSchema.safeParse(request.query);

    if (queryError) {
      throw new AppError('FMQUE009', queryError);
    }

    const result = await ForumModule.QuestionModule.fetchListingForClient(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default listing;
