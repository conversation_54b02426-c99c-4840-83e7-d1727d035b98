import { FastifyInstance } from 'fastify';
import communityRoutes from './community';
import questionRoutes from './question';
import communityMemberRoutes from './member';
import communityRequestRoutes from './request';
import questionCommentRoutes from './questionComment';
import answerRoutes from './answer';
import answerCommentRoutes from './answerComment';
import answerVoteRoutes from './answerVote';
import topicRoutes from './topic';
import questionVoteRoutes from './questionVote';
import exploreRoutes from './explore';
import forumGlobalSearchRoutes from './globalSearch';

const forumRoutes = (fastify: FastifyInstance): void => {
  fastify.register(answerRoutes);
  fastify.register(answerCommentRoutes);
  fastify.register(answerVoteRoutes);
  fastify.register(communityRoutes);
  fastify.register(communityRequestRoutes);
  fastify.register(communityMemberRoutes);
  fastify.register(questionRoutes);
  fastify.register(questionCommentRoutes);
  fastify.register(questionVoteRoutes);
  fastify.register(topicRoutes);
  fastify.register(exploreRoutes);
  fastify.register(forumGlobalSearchRoutes);
};

export default forumRoutes;
