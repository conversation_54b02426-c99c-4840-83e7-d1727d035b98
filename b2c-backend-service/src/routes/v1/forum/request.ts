import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';

import ForumModule from '@modules/forum';
import { CommunityRequestBodySchema, CommunityRequestRevocationQuerySchema } from '@schemas/forum/request';
import type { FastifyInstance, FastifyReply } from 'fastify';

const communityRequestRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/forum/community-request', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = CommunityRequestBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('CMRQ005', bodyError);
    }
    const result = await ForumModule.CommunityRequestModule.createOne(bodyData);
    reply.status(HttpStatus.OK).send(result);
  });
  // fastify.post(
  //   '/backend/api/v1/forum/community-request-approval',
  //   {},
  //   async (request: FastifyRequestI, reply: FastifyReply) => {
  //     const { error: queryError, data: queryData } = CommunityMemberTypeChangeApprovalQuerySchema.safeParse(
  //       request.query,
  //     );
  //     const { data: bodyData, error: bodyError } = CommunityMemberTypeChangeApprovalBodySchema.safeParse(request.body);
  //     if (queryError) {
  //       throw new AppError('GEN002', queryError);
  //     }
  //     if (bodyError) {
  //       throw new AppError('GEN002', bodyError);
  //     }
  //     const result = await ForumModule.CommunityRequestModule.approveRequestTypeChange(request, queryData, bodyData);
  //     reply.status(HttpStatus.OK).send(result);
  //   },
  // );
  // fastify.delete(
  //   '/backend/api/v1/forum/community-request-rejection',
  //   {},
  //   async (request: FastifyRequestI, reply: FastifyReply) => {
  //     const { error: queryError, data: queryData } = CommunityRequestRejectionQuerySchema.safeParse(request.query);
  //     if (queryError) {
  //       throw new AppError('GEN002', queryError);
  //     }
  //     const _result = await ForumModule.CommunityRequestModule.rejectRequest(request, queryData);
  //     reply.status(HttpStatus.NO_CONTENT);
  //   },
  // );
  fastify.post(
    '/backend/api/v1/forum/community-request-revocation',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = CommunityRequestRevocationQuerySchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN002', queryError);
      }
      const _result = await ForumModule.CommunityRequestModule.revokeRequest(queryData);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
};

export default communityRequestRoutes;
