import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { GlobalSearchCombinedResponseI } from '@interfaces/forum/search';
import ForumModule from '@modules/forum';
import { GlobalSearchParamsSchema } from '@schemas/forum/question';

import type { FastifyInstance, FastifyReply } from 'fastify';

const forumGlobalSearchRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/global-search', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = GlobalSearchParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('GEN005', queryError);
    }

    const { type } = queryData;
    const response: GlobalSearchCombinedResponseI = {};

    if (type === 'questions' || type === 'all') {
      const questionsResult = await ForumModule.QuestionModule.globalSearch(request, queryData);
      response.questions = questionsResult;
    }

    if (type === 'communities' || type === 'all') {
      const communitiesResult = await ForumModule.CommunityModule.globalSearch(request, queryData);
      response.communities = communitiesResult;
    }

    reply.status(HttpStatus.OK).send(response);
  });
};

export default forumGlobalSearchRoutes;
