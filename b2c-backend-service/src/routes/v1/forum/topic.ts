import { HttpStatus } from '@consts/common/api/status';
import ForumModule from '@modules/forum';
import { TopicFetchForClientSchema, TopicFetchsertForClientSchema, TopicFetchsertI } from '@schemas/forum/topic';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const topicRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/topic/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const query = TopicFetchForClientSchema.parse(request.query);
    const result = await ForumModule.TopicModule.fetchForClient(
      pick(query, ['search']),
      pick(query, ['page', 'pageSize']),
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/forum/topic/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const body = TopicFetchsertForClientSchema.parse(request.body);
    const result = await ForumModule.TopicModule.fetchsert(body as TopicFetchsertI);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default topicRoutes;
