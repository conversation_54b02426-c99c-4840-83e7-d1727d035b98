import { HttpStatus } from '@consts/common/api/status';

import { FastifyInstance } from 'fastify';
import type { FastifyReply, FastifyRequest } from 'fastify';
import Master from '@modules/master';
import { CountryOptionsFetchSchema } from '@schemas/master/country';
import AppError from '@classes/AppError';

const countryRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/master/country/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = CountryOptionsFetchSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('GEN005', queryError);
    }
    const result = await Master.CountryModule.fetch(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default countryRoutes;
