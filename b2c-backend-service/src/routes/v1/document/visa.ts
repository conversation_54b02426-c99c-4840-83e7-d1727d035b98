import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import DocumentModule from '@modules/document';
import { ProfileIdPaginationSchema, RouteParamsSchema } from '@schemas/common/common';
import { VisaPatchBodySchema, VisaPostBodySchema } from '@schemas/document/visa';
import { FastifyInstance, FastifyReply } from 'fastify';

const visaRoutes = (fastify: FastifyInstance): void => {
  fastify.patch('/backend/api/v1/document/visa/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: routeParamsError, data: routeParamsData } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('VISA009', { error: routeParamsError });
    }
    const { error: bodyError, data: bodyData } = VisaPatchBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('VISA005', { error: bodyError });
    }
    const result = await DocumentModule.VisaModule.updateOne(
      {
        ...bodyData,
      },
      { id: routeParamsData.id },
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/document/visa', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = VisaPostBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('VISA005', { error: bodyError });
    }

    const result = await DocumentModule.VisaModule.createOne(request, {
      ...bodyData,
    });
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.get('/backend/api/v1/document/visas', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ProfileIdPaginationSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('VISA007', queryError);
    }
    const result = await DocumentModule.VisaModule.fetchForExternalClient(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/document/visa/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: routeParamsError, data: routeParamsData } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('VISA009', { error: routeParamsError });
    }
    const result = await DocumentModule.VisaModule.fetchOneForClient(routeParamsData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/document/visa/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: routeParamsError, data: routeParamsData } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('VISA009', { error: routeParamsError });
    }
    await DocumentModule.VisaModule.deleteOne(routeParamsData);
    reply.status(HttpStatus.NO_CONTENT);
  });
};

export default visaRoutes;
