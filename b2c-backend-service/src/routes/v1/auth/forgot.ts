import type { FastifyInstance, FastifyReply } from 'fastify';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Auth from '@modules/auth';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { ResetPasswordSchema, SendPasswordResetOTPSchema } from '@schemas/auth/auth';
import { VerifyOTPForPasswordResetSchema } from '@schemas/user/profile';

const forgotPasswordRoutes = (fastify: FastifyInstance): void => {
  fastify.post(
    '/backend/api/v1/auth/forgot-password/send-otp',
    async (request: FastifyRequestI, reply: FastifyReply) => {
      try {
        const parseResult = SendPasswordResetOTPSchema.safeParse(request.body);
        if (!parseResult.success) {
          const errorMessage = parseResult.error.errors.map((err) => err.message).join(', ');
          throw new AppError('AUTH007', errorMessage);
        }
        const { email } = parseResult.data;
        await Auth.AuthModule.sendOTPForPasswordReset({ email });
        reply.status(HttpStatus.OK).send();
      } catch (error) {
        if (error instanceof AppError) {
          throw error;
        }
        throw new AppError('AUTH025', error);
      }
    },
  );

  fastify.post(
    '/backend/api/v1/auth/forgot-password/verify-otp',
    async (request: FastifyRequestI, reply: FastifyReply) => {
      try {
        const parseResult = VerifyOTPForPasswordResetSchema.safeParse(request.body);
        if (!parseResult.success) {
          const errorMessage = parseResult.error.errors.map((err) => err.message).join(', ');
          throw new AppError('AUTH007', errorMessage);
        }
        const { email, otp } = parseResult.data;
        await Auth.AuthModule.verifyOTPForPasswordReset({ email, otp });
        reply.status(HttpStatus.OK).send();
      } catch (error) {
        if (error instanceof AppError) {
          throw error;
        }
        throw new AppError('AUTH025', error);
      }
    },
  );

  fastify.post(
    '/backend/api/v1/auth/forgot-password/reset-password',
    async (request: FastifyRequestI, reply: FastifyReply) => {
      try {
        const parseResult = ResetPasswordSchema.safeParse(request.body);
        if (!parseResult.success) {
          const errorMessage = parseResult.error.errors.map((err) => err.message).join(', ');
          throw new AppError('AUTH007', errorMessage);
        }
        const { email, newPassword } = parseResult.data;
        await Auth.AuthModule.resetPassword({ email, newPassword });
        reply.status(HttpStatus.OK);
      } catch (error) {
        if (error instanceof AppError) {
          throw error;
        }
        throw new AppError('AUTH025', error);
      }
    },
  );
};

export default forgotPasswordRoutes;
