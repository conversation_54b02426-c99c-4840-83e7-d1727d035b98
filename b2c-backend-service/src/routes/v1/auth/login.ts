import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Auth from '@modules/auth';
import { AuthLoginBodyI, AuthLoginParamsI, AuthLoginParamsSchema } from '@schemas/auth/auth';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const loginRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/auth/login', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data } = AuthLoginParamsSchema.safeParse({
      ...(request.body as AuthLoginBodyI),
      ip: request.ip,
      versionNo: request.headers?.['x-version-no'],
      deviceId: request.headers?.['x-device-id'],
      platform: request.headers?.['x-platform'],
    } as AuthLoginParamsI);

    if (error) {
      throw new AppError('AUTH007', error);
    }
    const loginResult = await Auth.AuthModule.login(data);
    reply.status(HttpStatus.OK).send(loginResult);
  });
};

export default loginRoutes;
