import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Auth from '@modules/auth';
import { AuthRegisterParamsSchema } from '@schemas/auth/auth';
import type { AuthRegisterBodyI, AuthRegisterParamsI } from '@schemas/auth/auth';
import type { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const registerRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/auth/register', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data } = AuthRegisterParamsSchema.safeParse({
      ...(request.body as AuthRegisterBodyI),
      ip: request.ip,
      versionNo: request.headers?.['x-version-no'],
      deviceId: request.headers?.['x-device-id'],
      platform: request.headers?.['x-platform'],
      deviceToken: request.headers?.['x-device-token'],
    } as AuthRegisterParamsI);

    if (error) {
      throw new AppError('AUTH007', error);
    }
    const registerResult = await Auth.AuthModule.register(data);
    reply.status(HttpStatus.CREATED).send(registerResult);
  });
};

export default registerRoutes;
