import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import User from '@modules/user';
import { UpdateAuthUsernameParamsSchema, UpdateUsernameParamsSchema } from '@schemas/user/profile';
import { FastifyInstance, FastifyReply, type FastifyRequest } from 'fastify';

const authUsernameRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/auth/user/profile/username',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = UpdateUsernameParamsSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('PFL008', queryError);
      }
      await User.ProfileModule.isUnusedUsername({ username: queryData.username! });
      reply.status(HttpStatus.OK);
    },
  );

  fastify.patch(
    '/backend/api/v1/auth/user/profile/username',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { data: bodyData, error: bodyError } = UpdateAuthUsernameParamsSchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('PFL008', { error: bodyError.errors });
      }
      const result = await User.ProfileModule.updateOne(bodyData, {
        id: bodyData.profileId,
      });
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default authUsernameRoutes;
