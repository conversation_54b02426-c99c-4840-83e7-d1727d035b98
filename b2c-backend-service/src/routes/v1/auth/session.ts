import type { FastifyInstance, FastifyReply } from 'fastify';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Auth from '@modules/auth';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { SessionUpdateCoordinatesParamsSchema } from '@schemas/auth/session';

const sessionRoutes = (fastify: FastifyInstance): void => {
  fastify.patch('/backend/api/v1/auth/session/coordinates', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = SessionUpdateCoordinatesParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('AUTH007', error);
    }
    await Auth.SessionModule.updateCoordinates(request, data);
    reply.status(HttpStatus.OK);
  });
};

export default sessionRoutes;
