import type { FastifyInstance, FastifyReply } from 'fastify';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Auth from '@modules/auth';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { VerifyOTPForEmailVerificationSchema } from '@schemas/user/profile';

const emailRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/secure-auth/email/otp', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    await Auth.AuthModule.sendOTPForEmailVerification(request);
    reply.status(HttpStatus.CREATED);
  });
  fastify.patch('/backend/api/v1/secure-auth/email/otp', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = VerifyOTPForEmailVerificationSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('AUTH025', bodyError);
    }
    await Auth.AuthModule.verifyOTPForEmailVerification(request, bodyData);
    reply.status(HttpStatus.OK);
  });
};

export default emailRoutes;
