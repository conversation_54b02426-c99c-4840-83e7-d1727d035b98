import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Ship from '@modules/ship';
import { ShipVisitorFetchForClientParamsSchema } from '@schemas/ship/ship';
import type { FastifyInstance, FastifyReply } from 'fastify';

const visitorRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/ship/visitors', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ShipVisitorFetchForClientParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PRTVSR004', queryError);
    }
    const result = await Ship.CoreShipModule.fetchVisitors(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default visitorRoutes;
