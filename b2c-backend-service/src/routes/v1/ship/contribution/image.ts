import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Ship from '@modules/ship';
import { ShipImageFetchForClientParamsSchema } from '@schemas/ship/contribution';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply } from 'fastify';

const contributionImageRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/ship/contribution/image', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ShipImageFetchForClientParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SHPCNB005', queryError);
    }
    const result = await Ship.ShipImageContributionModule.fetchForClient(
      queryData,
      pick(queryData, ['page', 'pageSize']),
    );
    reply.status(HttpStatus.OK).send(result);
  });
};

export default contributionImageRoutes;
