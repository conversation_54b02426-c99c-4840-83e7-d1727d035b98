import { FastifyRequestI } from '@interfaces/common/declaration';
import { FastifyInstance, FastifyReply } from 'fastify';

const rsvpRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/announcement/rsvp', {}, async (_request: FastifyRequestI, _reply: FastifyReply) => {
    // const { data: bodyData, error: bodyError } = NearByFetchPeopleBodySchema.safeParse(request.body);
    // if (bodyError) {
    //   throw new AppError('PFL008', { error: bodyError.errors });
    // }
    // const result = await AnnouncementModule.NearByModule.fetchPeople(request, bodyData);
    // reply.status(HttpStatus.OK).send(result);
  });
};

export default rsvpRoutes;
