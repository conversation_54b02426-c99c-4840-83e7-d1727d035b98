import { FastifyInstance, FastifyReply } from 'fastify';
import { HttpStatus } from '@consts/common/api/status';
import Network from '@modules/network';
import { FastifyRequestI } from '@interfaces/common/declaration';
import {
  RequestFetchManySchema,
  RequestUpsertOneForReceiverSchema,
  RequestUpsertOneForSenderSchema,
} from '@schemas/network/request';
import AppError from '@classes/AppError';

const requestRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/network/request/send', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = RequestUpsertOneForSenderSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('REQ015', bodyError);
    }
    await Network.RequestModule.upsertOneForSender(request, bodyData);
    reply.status(HttpStatus.OK);
  });
  fastify.post('/backend/api/v1/network/request/respond', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = RequestUpsertOneForReceiverSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('REQ015', bodyError);
    }
    await Network.RequestModule.upsertOneForReceiver(request, bodyData);
    reply.status(HttpStatus.OK);
  });

  fastify.get('/backend/api/v1/network/request/sent', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = RequestFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('REQ016', queryError);
    }

    const result = await Network.RequestModule.fetchManySent(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/network/request/received', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = RequestFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('REQ016', queryError);
    }
    const result = await Network.RequestModule.fetchManyReceived(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default requestRoutes;
