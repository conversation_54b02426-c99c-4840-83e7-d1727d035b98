import { HttpStatus } from '@consts/common/api/status';
import LogModule from '@modules/log';
import { Prisma } from '@prisma/mongodb';
import type { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const appLogRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/log/app-log', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    await LogModule.AppLogModule.createOne(request.body as Prisma.AppLogUncheckedCreateInput);
    reply.status(HttpStatus.CREATED);
  });
};

export default appLogRoutes;
