import { FastifyInstance } from 'fastify';
import authRoutes from './v1/auth';
import { authNoTokenMiddleware } from 'middlewares/authMiddleware';
import healthRoutes from './v1/health';
import privacyPolicyFetchRoutes from './v1/privacyPolicy/fetch';
import logRoutes from './v1/log';
import listing from './v1/forum/listing';
import slugRoutes from './v1/forum/slug';

const unsecureRoutes = (fastify: FastifyInstance): void => {
  fastify.addHook('preHandler', authNoTokenMiddleware);
  fastify.register(authRoutes);
  fastify.register(healthRoutes);
  fastify.register(privacyPolicyFetchRoutes);
  fastify.register(logRoutes);
  fastify.register(listing);
  fastify.register(slugRoutes);
};

export default unsecureRoutes;
