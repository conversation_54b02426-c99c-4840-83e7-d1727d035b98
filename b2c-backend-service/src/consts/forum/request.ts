import { CommunityRequestStatusE as CommunityRequestStatus } from '@prisma/postgres';
import z from 'zod';

export const CommunityRequestStatusE = z.enum([
  CommunityRequestStatus.ACCEPTED,
  CommunityRequestStatus.PARTIALLY_ACCEPTED,
  CommunityRequestStatus.PENDING,
  CommunityRequestStatus.REJECTED,
  CommunityRequestStatus.REVOKED,
]);

export type CommunityRequestStatusI = z.infer<typeof CommunityRequestStatusE>;
