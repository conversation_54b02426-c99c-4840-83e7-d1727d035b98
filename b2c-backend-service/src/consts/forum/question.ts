import { z } from 'zod';
import { QuestionTypeE as QuestionType } from '@prisma/postgres';
import { ForumFileExtensionE as ForumFileExtension } from '@prisma/postgres';

export const FORUM_MAX_NO_OF_FILES: number = 7;
export const QuestionTypeE = z.enum([QuestionType.NORMAL, QuestionType.TROUBLESHOOT]);
export type QuestionTypeI = z.infer<typeof QuestionTypeE>;
export const ForumFileExtensionE = z.enum([
  ForumFileExtension.webp,
  ForumFileExtension.jpeg,
  ForumFileExtension.jpg,
  ForumFileExtension.pdf,
  ForumFileExtension.xls,
  ForumFileExtension.xlsx,
]);
