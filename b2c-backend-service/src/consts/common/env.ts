import { EnvVariablesI } from '@interfaces/common/env';

export const ENV: EnvVariablesI = {
  API_KEY: process.env.API_KEY as string,
  BACKEND_PORT: parseInt(process.env.BACKEND_PORT),
  DO_ACCESS_KEY_ID: process.env.DO_ACCESS_KEY_ID as string,
  DO_REGION: process.env.DO_REGION as string,
  DO_SECRET_KEY: process.env.DO_SECRET_KEY as string,
  DO_SIGNED_URL_EXPIRY_S: parseInt(process.env.DO_SIGNED_URL_EXPIRY_S),
  DO_SPACES: process.env.DO_SPACES as string,
  DO_SPACES_CDN_ENDPOINT: process.env.DO_SPACES_CDN_ENDPOINT as string,
  DO_SPACES_ENDPOINT: process.env.DO_SPACES_ENDPOINT as string,
  ENCRYPTION_SECRET_KEY: process.env.ENCRYPTION_SECRET_KEY as string,
  JWT_SECRET: process.env.JWT_SECRET,
  KAFKA_BACKEND_CLIENT_ID: process.env.KAFKA_BACKEND_CLIENT_ID as string,
  KAFKA_BACKEND_BROKER: process.env.KAFKA_BACKEND_BROKER as string,
  MONGO_DATABASE_URL: process.env.MONGO_DATABASE_URL,
  NODE_ENV: process.env.NODE_ENV as 'development' | 'production' | 'test',
  POSTGRES_DATABASE_URL: process.env.POSTGRES_DATABASE_URL,
} as EnvVariablesI;
