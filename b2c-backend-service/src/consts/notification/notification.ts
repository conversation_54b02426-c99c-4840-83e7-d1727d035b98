import { z } from 'zod';
import { NotificationTypeE as NotificationType } from '@prisma/mongodb';

export const NotificationTypeE = z.enum([
  NotificationType.COMMENT,
  NotificationType.FOLLOWER,
  NotificationType.LIKE,
  NotificationType.MESSAGE,
  NotificationType.PUBLIC,
  NotificationType.REPLY,
  NotificationType.REQUEST_ACCEPTED,
  NotificationType.REQUEST_RECEIVED,
  NotificationType.UPDATE_READ,
]);

export type NotificationTypeI = z.infer<typeof NotificationTypeE>;
