import { ShipContributionLabelE as ShipContributionLabel } from '@prisma/postgres';
import { z } from 'zod';

export const ShipContributionLabelE = z.enum([
  ShipContributionLabel.mmsi,
  ShipContributionLabel.callSign,
  ShipContributionLabel.name,
  ShipContributionLabel.flagCountryIso2,
  ShipContributionLabel.generalVesselType,
  ShipContributionLabel.otherVesselType,
  ShipContributionLabel.status,
  ShipContributionLabel.portOfRegistry,
  ShipContributionLabel.yearBuilt,
]);

export type ShipContributionLabelI = z.infer<typeof ShipContributionLabelE>;
