import { Profile, ScrapBookPost } from '@prisma/postgres';

export type ScrapBookPostFetchForClientI = Pick<
  ScrapBookPost,
  'id' | 'textPreview' | 'reactionCount' | 'commentCount' | 'createdAt'
> & {
  text?: ScrapBookPost['text'];
  profile: Pick<Profile, 'id' | 'name' | 'avatar' | 'designationText'>;
  isLiked: boolean;
  isCaptionTruncated: boolean;
};

export type ScrapBookPostCreateOneResultI = Pick<ScrapBookPost, 'id'>;
