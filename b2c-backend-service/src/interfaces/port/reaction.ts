import { ScrapBookReactionTypeI } from '@consts/port/reaction';
import type { Profile } from '@prisma/postgres';

export type ScrapBookReactionFetchForClientI = {
  Profile: Pick<Profile, 'id' | 'name' | 'avatar'>;
  reactionType: ScrapBookReactionTypeI;
};
export type ProfileItemI = Pick<
  Profile,
  | 'id'
  | 'name'
  | 'avatar'
  | 'designationText'
  | 'entityText'
  | 'designationAlternativeId'
  | 'designationRawDataId'
  | 'entityId'
  | 'entityRawDataId'
>;
export type ScrapBookReactionRawResultI = {
  reactionType: ScrapBookReactionTypeI;
  Profile: ProfileItemI;
};
