import { DBDataTypeI } from '@consts/common/data';
import { CertificateCourse, Prisma } from '@prisma/postgres';

export type CertificateCourseClientI = Pick<CertificateCourse, 'id' | 'name' | 'type'> & {
  dataType: DBDataTypeI;
};

export type CertificateCourseNestedClientI = Pick<CertificateCourse, 'id' | 'name' | 'type'> & {
  dataType: DBDataTypeI;
};
export type CertificateCourseModuleFetchsertParamsI = Pick<Prisma.CertificateCourseRawDataCreateInput, 'name' | 'type'>;
