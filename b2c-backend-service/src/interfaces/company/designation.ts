import { DBDataTypeI } from '@consts/common/data';
import { Designation, Prisma } from '@prisma/postgres';

export type DesignationClientI = Designation & {
  dataType: DBDataTypeI;
};
export type DesignationNestedClientI = Pick<Designation, 'id' | 'name'> & {
  dataType: DBDataTypeI;
};
export type DesignationModuleFetchsertParamsI = Pick<Prisma.DesignationRawDataCreateInput, 'name'>;

export type DesignationRawQueryFetchForClientResultI = {
  id: string;
  name: string;
  dataType: DBDataTypeI;
};
