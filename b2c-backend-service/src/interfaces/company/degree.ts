import { DBDataTypeI } from '@consts/common/data';
import { Degree, Prisma } from '@prisma/postgres';

export type DegreeClientI = Degree & {
  dataType: DBDataTypeI;
};
export type DegreeNestedClientI = Pick<Degree, 'id' | 'name'> & {
  dataType: DBDataTypeI;
};
export type DegreeRawQueryFetchForClientResultI = {
  id: string;
  name: string;
  dataType: DBDataTypeI;
};

export type DegreeRawQueryFetchsertResultI = {
  id: string;
  name: string;
  dataType: DBDataTypeI;
};
export type DegreeModuleFetchsertParamsI = Pick<Prisma.DegreeRawDataCreateInput, 'name'>;
