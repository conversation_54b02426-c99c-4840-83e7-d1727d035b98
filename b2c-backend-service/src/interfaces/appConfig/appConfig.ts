export type AuthSessionAppConfigI = {
  // 1000 * 60 * 60 * 24 * 30 (Expires in 30 days)
  expiry: number; // in milliseconds
  // 5
  maxSessions: number;
};
export type CommunicationOTPConfigI = {
  expiry: number; // in milliseconds
};

export type CommunicationRateLimitConfigI = {
  count: number;
  duration: number; // in milliseconds
};

export type CommunicationEmailConfigI = {
  name: string;
  verification: {
    email: {
      limit: CommunicationRateLimitConfigI;
      emailId: string;
      otp: CommunicationOTPConfigI;
    };
  };
};
export type CommunicationConfigI = CommunicationEmailConfigI;
export type ForumQuestionConfigI = { liveExpiry: number };
export type AnnouncementNearByConfigI = { profileRadiusInKM: number };
