import { VisaPatchBodyI, VisaPostBodyI } from '@schemas/document/visa';
import type { Country, Prisma, Visa } from '@prisma/postgres';
import { DocumentExpiryStatusI } from '@consts/document/common';

export type VisaCreateOneParamsI = VisaPostBodyI;
export type VisaCreateForClientI = Pick<Visa, 'id' | 'fileUrl'> & {
  expiryStatus: DocumentExpiryStatusI;
};
export type VisaUpdateForClientI = Pick<Visa, 'id' | 'fileUrl'> & {
  expiryStatus: DocumentExpiryStatusI;
};
export type VisaForExternalClientI = Pick<Visa, 'id' | 'name' | 'fromDate' | 'untilDate' | 'fileUrl'> & {
  country: Pick<Country, 'name'>;
  expiryStatus: DocumentExpiryStatusI;
};

export type VisaForInternalClientI = Pick<Visa, 'id' | 'documentNo' | 'name' | 'fromDate' | 'untilDate' | 'fileUrl'> & {
  country: Pick<Country, 'iso2' | 'name'>;
  expiryStatus: DocumentExpiryStatusI;
};
export type VisaUpdateOneI = VisaPatchBodyI;
export type VisaUpdateOneDataI = Prisma.XOR<Prisma.VisaUpdateInput, Prisma.VisaUncheckedUpdateInput>;
