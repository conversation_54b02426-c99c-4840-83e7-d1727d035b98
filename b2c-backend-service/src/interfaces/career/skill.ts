import { DBDataTypeI } from '@consts/common/data';
import { NullableAttrI, NumberUndefinedNullI, StringNullI } from '@interfaces/common/data';
import { CertificateCourseClientI } from '@interfaces/company/certificateCourse';
import { DegreeClientI } from '@interfaces/company/degree';
import { EntityFilterI, EntityNestedClientI } from '@interfaces/company/entity';
import { ProfileSkill, Skill } from '@prisma/postgres';
import { ProfileSkillPostBodyI } from '@schemas/career/skill';
export type GetProfileSkillInputsI = { masterSkills?: string[]; rawDataSkills?: string[]; profileId: string };
export type ProfileSkillCreateManyI = {
  masterSkills?: string[];
  rawDataSkills?: string[];
  profileId: string;
};

export type ProfileSkillCreateManyWithExperienceShipI = ProfileSkillCreateManyI & {
  experienceShipId: string;
};
export type ProfileSkillCreateManyWithEntityDegreeI = ProfileSkillCreateManyI & {
  entity: EntityNestedClientI;
  degree: DegreeClientI;
};
export type ProfileSkillEntityDegreeFilterI = ProfileSkillCreateManyI & {
  entity: EntityFilterI;
  degree: DegreeClientI;
};

export type ProfileSkillCreateManyWithEntityDegreeResultI = {
  countProfileSkill?: NumberUndefinedNullI;
  countProfileSkillEntityDegree?: NumberUndefinedNullI;
};
export type ProfileSkillCreateManyWithExperienceShipResultI = {
  countProfileSkill?: NumberUndefinedNullI;
  countProfileSkillExperienceShip?: NumberUndefinedNullI;
};
export type ProfileSkillCreateManyWithEntityCertificateI = ProfileSkillCreateManyI & {
  entity: EntityNestedClientI;
  certificate: CertificateCourseClientI;
};
export type ProfileSkillCreateManyWithEntityCertificateResultI = {
  countProfileSkill?: NumberUndefinedNullI;
  countProfileSkillEntityCertificate?: NumberUndefinedNullI;
};

export type ProfileSkillCreateOneI = ProfileSkillPostBodyI & {
  profileId: string;
};

export type ProfileSkillDataI = NullableAttrI<
  Pick<ProfileSkill, 'id' | 'skillId' | 'skillRawDataId'>,
  'skillId' | 'skillRawDataId'
> & {
  skillName: StringNullI;
  skillRawDataName: StringNullI;
};
export type SkillNestedExternalI = Pick<Skill, 'id' | 'name'> & {
  dataType: DBDataTypeI;
};
