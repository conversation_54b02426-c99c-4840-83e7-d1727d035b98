import { DegreeNestedClientI } from '@interfaces/company/degree';
import { EntityNestedClientI } from '@interfaces/company/entity';
import { SkillNestedClientI } from '@interfaces/company/skill';
import { Prisma, type ProfileEducation } from '@prisma/postgres';

export type ProfileEducationCreateOneDataI = Prisma.XOR<
  Prisma.ProfileEducationCreateInput,
  Prisma.ProfileEducationUncheckedCreateInput
>;

export type ProfileEducationUpdateOneDataI = Prisma.XOR<
  Prisma.ProfileEducationUpdateInput,
  Prisma.ProfileEducationUncheckedUpdateInput
>;
export type ProfileEducationCreateForClientI = Pick<ProfileEducation, 'id'>;
export type ProfileEducationForExternalClientI = Pick<ProfileEducation, 'id' | 'fromDate' | 'toDate' | 'createdAt'> & {
  entity: EntityNestedClientI;
  degree: DegreeNestedClientI;
};
export type ProfileEducationForInternalClientI = Pick<ProfileEducation, 'id' | 'fromDate' | 'toDate' | 'profileId'> & {
  entity: EntityNestedClientI;
  degree: DegreeNestedClientI;
  skills: SkillNestedClientI[];
};
