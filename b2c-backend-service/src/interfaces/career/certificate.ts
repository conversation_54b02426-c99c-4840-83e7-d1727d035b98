import { CertificateCourseNestedClientI } from '@interfaces/company/certificateCourse';
import type { EntityNestedClientI } from '@interfaces/company/entity';
import type { SkillNestedClientI } from '@interfaces/company/skill';
import { Prisma } from '@prisma/postgres';
import type { ProfileCertificate } from '@prisma/postgres';
import type { ProfileCertificatePatchBodyI, ProfileCertificatePostBodyI } from '@schemas/career/certificate';

export type ProfileCertificateCreateOneDataI = Prisma.XOR<
  Prisma.ProfileCertificateCreateInput,
  Prisma.ProfileCertificateUncheckedCreateInput
>;

export type ProfileCertificateUpdateOneDataI = Prisma.XOR<
  Prisma.ProfileCertificateUpdateInput,
  Prisma.ProfileCertificateUncheckedUpdateInput
>;

export type ProfileCertificateCreateOneParamsI = ProfileCertificatePostBodyI;
export type ProfileCertificateUpdateOneParamsI = ProfileCertificatePatchBodyI;
export type ProfileCertificateForExternalClientI = Pick<
  ProfileCertificate,
  'id' | 'fromDate' | 'untilDate' | 'createdAt'
> & {
  entity: EntityNestedClientI;
  certificateCourse: CertificateCourseNestedClientI;
};
export type ProfileCertificateCreateForClientI = Pick<ProfileCertificate, 'id' | 'fileUrl'>;

export type ProfileCertificateForInternalClientI = Pick<
  ProfileCertificate,
  'id' | 'fileUrl' | 'fromDate' | 'untilDate'
> & {
  entity: EntityNestedClientI;
  certificateCourse: CertificateCourseNestedClientI;
  skills: SkillNestedClientI[];
};
