import { Prisma } from '@prisma/postgres';
import type { PrismaClient } from '@prisma/postgres';
import type { DefaultArgs } from '@prisma/postgres/runtime/library';

export type PostgresTxnI = Omit<
  PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
  '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
>;
export type MongoTxnI = Omit<
  PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
  '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
>;
export type TotalI = { total: number };
