import { DBDataTypeI } from '@consts/common/data';
import { StringUndefinedNullI } from '@interfaces/common/data';
import type { Ship } from '@prisma/postgres';

export type ShipNestedClientI = Pick<Ship, 'imo' | 'name'> & {
  dataType: DBDataTypeI;
};

export type ShipSearchQueryDataItemI = {
  imo: StringUndefinedNullI;
  rawImo: StringUndefinedNullI;
  name: StringUndefinedNullI;
  rawName: StringUndefinedNullI;
  matchedName: StringUndefinedNullI;
  imageUrl: StringUndefinedNullI;
  dataType: 'raw' | 'master';
};
export type ShipSearchDataItemI = ShipNestedClientI & {
  matchedName: StringUndefinedNullI;
  imageUrl: StringUndefinedNullI;
};
export type ShipSearchByNameExternalResultI = {
  total: number;
  data: ShipSearchDataItemI[];
};
