import { NumberUndefinedNullI, StringNullI } from '@interfaces/common/data';
import { ProfileDataI, ProfileNetworkExternalI, ProfileSearchExternalI } from '@interfaces/user/profile';

export type ConnectionDataI = {
  cursorId: number;
  Profile: ProfileDataI;
  isConnected: boolean;
};

export type ConnectionExternalI = {
  cursorId: number;
  Profile: ProfileNetworkExternalI;
};
export type ConnectionFetchManyResultI = {
  total?: NumberUndefinedNullI;
  data: ConnectionExternalI[];
};
export type ConnectionSearchResultI = {
  total?: NumberUndefinedNullI;
  data: ProfileSearchExternalI[];
};

export type ConnectionRawExternalI = {
  connectedId: string; // UUID
  cursorId: number; // Assuming this is a sequential ID
  profileId: string; // UUID
  name: StringNullI;
  avatar: StringNullI;
  designationText: StringNullI;
  designationAlternativeId: StringNullI;
  designationRawDataId: StringNullI;
  entityText: StringNullI;
  entityId: StringNullI;
  entityRawDataId: StringNullI;
};
