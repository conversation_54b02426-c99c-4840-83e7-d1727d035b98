import { TopicClientI } from '@interfaces/forum/topic';
import { TroubleshootQuestionI } from '@interfaces/forum/question';
import { FetchCommunityClientI } from '@interfaces/forum/community';

export type ForumExploreI = {
  topics: TopicClientI[];
  troubleshootQuestions: TroubleshootQuestionI[];
  recommendedCommunities: FetchCommunityClientI[];
};

export type ForumExploreOptions = {
  days?: number;
  limit?: number;
  equipmentFilter?: {
    categoryId?: string;
    manufacturerId?: string;
    modelId?: string;
  };
};
