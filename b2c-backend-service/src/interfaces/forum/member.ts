import { MemberTypeE } from '@prisma/postgres';
export type FetchCommunityMemberI = {
  type: string;
  profile: {
    id: string;
    username: string;
    name: string;
    avatar: string;
  };
  community: {
    id: string;
    name: string;
  };
};

export type CommunityMemberUpsertResultI = {
  action: 'created' | 'updated';
  member: {
    profileId: string;
    type: MemberTypeE;
  };
  communityId: string;
};
