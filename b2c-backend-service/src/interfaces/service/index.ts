import type { ObjUnknownI } from '@interfaces/common/data';
import CommunicationService, { type CommunicationServiceConfigI } from '@navicater/b2c-internal-communication';
import Firebase, { type FirebaseConfigI } from '@navicater/vendor-firebase';
import Google, { type GoogleConfigI } from '@navicater/vendor-google';
import type { InternalService, InternalServiceNameE, Vendor, VendorNameE } from '@prisma/mongodb';
export type VendorI = Pick<Vendor, 'name' | 'type'> & {
  config: ObjUnknownI;
};
export type GetVendorResultI =
  | ({ name: VendorNameE } & { config: FirebaseConfigI; instance: Firebase })
  | { config: GoogleConfigI; instance: Google };

export type InternalServiceI = Pick<InternalService, 'name'> & {
  config: ObjUnknownI;
};
export type GetInternalServiceResultI = { name: InternalServiceNameE } & {
  config: CommunicationServiceConfigI;
  instance: CommunicationService;
};
