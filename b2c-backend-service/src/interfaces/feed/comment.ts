import type { ProfileDataI, ProfileExternalI } from '@interfaces/user/profile';
import type { PostComment } from '@prisma/postgres';

export type CommentDataI = {
  text: string;
  id: string;
  cursorId: number;
  repliesCount: number;
  createdAt: Date;
  updatedAt: Date;
  Profile: ProfileDataI;
  replies: CommentDataI[];
};

export type CommentCreateOneResultI = Pick<PostComment, 'id'> & {
  cursorId: number;
};

export type CommentExternalI = {
  text: string;
  id: string;
  cursorId: number;
  repliesCount: number;
  createdAt: Date;
  updatedAt: Date;
  Profile: ProfileExternalI;
  replies: CommentExternalI[];
};
export type CommentFetchManyResultI = {
  comments: CommentExternalI[];
  total: number;
  cursorId: number | null;
};
