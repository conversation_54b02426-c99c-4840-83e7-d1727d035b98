import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { IdentityTypeOrderIndex } from '@consts/document/identity';
import type { IdentityTypeI } from '@consts/document/identity';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type {
  IdentityCreateForClientI,
  IdentityCreateOneParamsI,
  IdentityForInternalClientI,
  IdentityForExternalClientI,
  IdentityUpdateOneDataI,
  IdentityUpdateOneI,
  IdentityUpdateForClientI,
} from '@interfaces/document/identity';
import Master from '@modules/master';
import StorageModule from '@modules/storage';
import { Prisma } from '@prisma/postgres';
import type { Identity } from '@prisma/postgres';
import type { ProfileIdPaginationI, RouteParamsI } from '@schemas/common/common';
import { getExpiryStatus } from '@utils/data/date';
export const IdentityModule = {
  fetchOne: async (
    filters: Partial<Pick<Prisma.IdentityWhereInput, 'id' | 'profileId' | 'type'>>,
    select: Prisma.IdentitySelect = {
      id: true,
      type: true,
    },
    throwsError: boolean = true,
  ): Promise<Identity> => {
    const identityResult = await prismaPG.identity.findFirst({
      where: filters,
      select,
    });
    if (throwsError && !identityResult) {
      throw new AppError('IDTY001');
    }
    return identityResult;
  },
  fetchOneForInternalClient: async (
    filters: Pick<Prisma.IdentityWhereInput, 'id' | 'profileId'>,
  ): Promise<IdentityForInternalClientI> => {
    const identityResult = await prismaPG.identity.findFirst({
      where: filters,
      select: {
        id: true,
        documentNo: true,
        fromDate: true,
        untilDate: true,
        type: true,
        fileUrl: true,
        profileId: true,
        Country: {
          select: {
            iso2: true,
            name: true,
          },
        },
      },
    });
    if (!identityResult) {
      throw new AppError('IDTY001');
    }

    const identityCreateEditForClientResult: IdentityForInternalClientI = {
      id: identityResult.id!,
      documentNo: identityResult.documentNo!,
      fromDate: identityResult.fromDate!,
      untilDate: identityResult.untilDate!,
      type: identityResult.type!,
      fileUrl: identityResult.fileUrl || null,
      country: identityResult.Country,
      expiryStatus: getExpiryStatus(identityResult),
    };
    return identityCreateEditForClientResult;
  },
  createOne: async (state: FastifyStateI, params: IdentityCreateOneParamsI): Promise<IdentityCreateForClientI> => {
    if (params.type !== 'PASSPORT') {
      const existingIdentityResult = await IdentityModule.fetchOne(
        { profileId: state.profileId, type: params.type },
        { id: true },
        false,
      );
      if (existingIdentityResult) {
        throw new AppError('IDTY004');
      }
    }
    const [identityResult, _profileMetaResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.identity.create({
            data: {
              fileUrl: params.file ? params.file.fileUrl : null,
              type: params.type,
              fromDate: params.fromDate,
              untilDate: params.untilDate,
              profileId: state.profileId,
              countryIso2: params.countryIso2,
              documentNo: params.documentNo,
              orderIndex: IdentityTypeOrderIndex[params.type as IdentityTypeI],
            },
            select: {
              id: true,
              fromDate: true,
              untilDate: true,
              fileUrl: true,
              Country: {
                select: {
                  iso2: true,
                  name: true,
                },
              },
            },
          }),
          txn.profileMeta.update({
            select: { identityCount: true },
            data: {
              identityCount: { increment: 1 },
            },
            where: { profileId: state.profileId },
          }),
        ]),
    );
    if (!identityResult) {
      throw new AppError('IDTY002');
    }
    const identityCreateEditForClientResult: IdentityCreateForClientI = {
      id: identityResult.id!,
      fileUrl: identityResult.fileUrl || null,
      expiryStatus: getExpiryStatus(identityResult),
    };
    return identityCreateEditForClientResult;
  },
  updateOne: async (
    params: IdentityUpdateOneI,
    filters: Pick<Prisma.IdentityWhereUniqueInput, 'id'>,
  ): Promise<IdentityUpdateForClientI> => {
    const [existingIdentityResult, _countryResult] = await Promise.all([
      IdentityModule.fetchOne({ id: filters.id }, { id: true, fromDate: true, untilDate: true, fileUrl: true }),
      typeof params?.countryIso2 === 'string' && params?.countryIso2?.length
        ? Master.CountryModule.fetchOne({ iso2: params.countryIso2 })
        : null,
    ]);

    if (params?.fromDate && params?.untilDate && params?.fromDate > params?.untilDate) {
      throw new AppError('IDTY001');
    } else if (
      params?.fromDate &&
      existingIdentityResult.untilDate &&
      params.fromDate > existingIdentityResult.untilDate
    ) {
      throw new AppError('IDTY001');
    } else if (params?.untilDate && existingIdentityResult.fromDate > params.untilDate!) {
      throw new AppError('IDTY001');
    }

    if (params?.file?.opr) {
      if (params.file.opr === 'UPDATE' && params?.file?.fileUrl && existingIdentityResult.fileUrl) {
        try {
          await StorageModule.CoreStorageModule.deleteFile({ fileUrl: existingIdentityResult.fileUrl });
        } catch (_error) {
          //
        }
      } else if (params.file.opr === 'DELETE' && existingIdentityResult.fileUrl) {
        try {
          await StorageModule.CoreStorageModule.deleteFile({ fileUrl: existingIdentityResult.fileUrl });
        } catch (_error) {
          //
        }
      }
    }

    const toUpdateParams: IdentityUpdateOneDataI = {};

    if (params?.fromDate) {
      toUpdateParams.fromDate = params.fromDate;
    }
    toUpdateParams.untilDate = params.untilDate;
    if (params?.countryIso2) {
      toUpdateParams.countryIso2 = params.countryIso2;
    }
    if (params?.documentNo) {
      toUpdateParams.documentNo = params.documentNo;
    }
    if (params?.file?.opr) {
      if (params.file.opr === 'CREATE' || params.file.opr === 'UPDATE') {
        if (params?.file?.fileUrl) {
          toUpdateParams.fileUrl = params.file.fileUrl;
        }
      } else if (params.file.opr === 'DELETE') {
        toUpdateParams.fileUrl = null;
      }
    }

    const identityResult = await prismaPG.identity.update({
      data: toUpdateParams,
      where: {
        id: filters.id,
      },
      select: {
        id: true,
        fromDate: true,
        untilDate: true,
        fileUrl: true,
      },
    });

    if (!identityResult) {
      throw new AppError('IDTY003');
    }

    const identityForInternalClientResult: IdentityUpdateForClientI = {
      id: identityResult.id!,
      fileUrl: identityResult.fileUrl || null,
      expiryStatus: getExpiryStatus(identityResult),
    };
    return identityForInternalClientResult;
  },
  deleteOne: async (state: FastifyStateI, { id }: RouteParamsI): Promise<void> => {
    const [_profileMetaResult, identityResult] = await Promise.all([
      prismaPG.profileMeta.findUnique({
        select: { identityCount: true },
        where: { profileId: state.profileId },
      }),
      prismaPG.identity.findUnique({
        select: { id: true },
        where: { id },
      }),
    ]);

    if (!identityResult) {
      throw new AppError('IDTY001');
    }

    await prismaPG.$transaction(async (txn) => {
      const deleteResult = await txn.identity.delete({
        select: { id: true, profileId: true },
        where: { id },
      });

      const remainingCount = await txn.identity.count({
        where: { profileId: state.profileId },
      });

      await txn.profileMeta.update({
        where: { profileId: state.profileId },
        data: {
          identityCount: remainingCount === 0 ? 0 : { decrement: 1 },
        },
      });

      if (!deleteResult) {
        throw new AppError('IDTY010');
      }
    });

    return;
  },
  fetchForExternalClient: async ({
    page,
    pageSize,
    profileId,
  }: ProfileIdPaginationI): Promise<IdentityForExternalClientI[]> => {
    const identityResult = await prismaPG.identity.findMany({
      where: { profileId },
      skip: page * pageSize,
      take: pageSize,
      orderBy: { orderIndex: 'asc' },
      select: {
        id: true,
        documentNo: true,
        fromDate: true,
        untilDate: true,
        type: true,
        orderIndex: true,
        fileUrl: true,
        Country: {
          select: { name: true },
        },
      },
    });
    const identityForClientResult: IdentityForExternalClientI[] = [];
    if (identityResult?.length) {
      identityForClientResult.push(
        ...identityResult.map(
          (identity) =>
            ({
              id: identity.id!,
              fromDate: identity.fromDate!,
              untilDate: identity.untilDate!,
              type: identity.type!,
              country: identity.Country,
              expiryStatus: getExpiryStatus(identity),
            }) as IdentityForExternalClientI,
        ),
      );
    }
    return identityForClientResult;
  },
};
