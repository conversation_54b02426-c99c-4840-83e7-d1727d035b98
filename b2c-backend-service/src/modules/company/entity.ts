import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { ObjUnknownI, TotalDataI } from '@interfaces/common/data';
import { PostgresTxnI } from '@interfaces/common/db';
import type { EntityClientI, EntityModuleFetchsertParamsI, EntityNestedClientI } from '@interfaces/company/entity';
import { EntityTypeE, Prisma } from '@prisma/postgres';
import type { IdTypeI } from '@schemas/common/common';

export const EntityModule = {
  fetchById: async (filters: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<EntityClientI> => {
    const select: Prisma.EntitySelect = { id: true, name: true, type: true };
    if (filters.dataType === 'raw') {
      const entityRawDataResult = await txn.entityRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(entityRawDataResult as ObjUnknownI),
        dataType: 'raw',
      } as EntityClientI;
    } else if (filters.dataType === 'master') {
      const entityResult = await txn.entity.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(entityResult as ObjUnknownI),
        dataType: 'master',
      } as EntityClientI;
    }
    throw new AppError('ORG001');
  },
  fetchForClient: async (
    name?: string,
    type?: EntityTypeE,
    { page, pageSize } = PAGINATION,
  ): Promise<TotalDataI<EntityNestedClientI>> => {
    name = name?.trim()?.toLowerCase();
    const [entitiesResultTemp, entitiesTotalResult] = await Promise.all([
      prismaPG.$queryRaw<EntityNestedClientI[]>`
          SELECT *
          FROM
          (
            (SELECT
            e."id",
            e."name",
            e."type",
            'master' AS "dataType"
            FROM
            "company"."Entity" e
            WHERE
            e."name" ILIKE ${name + '%'}
            ${type ? Prisma.sql`AND e."type" = ${type}::"company"."EntityTypeE"` : Prisma.empty})
            UNION ALL
            (SELECT
            erw."id",
            erw."name",
            erw."type",
            'raw' AS "dataType"
            FROM
            "rawData"."EntityRawData" erw
            WHERE
            erw."name" ILIKE ${name + '%'}
            ${type ? Prisma.sql`AND erw."type" = ${type}::"company"."EntityTypeE"` : Prisma.empty})
          )
          ORDER BY "name" ASC
          OFFSET ${page * pageSize}
          LIMIT ${pageSize}
      `,
      prismaPG.$queryRaw<{ total: number }[]>`
              WITH master_entity AS (
                SELECT 1
                FROM "company"."Entity" entity
                WHERE
                  entity."name" ILIKE ${name + '%'}
                  ${type ? Prisma.sql`AND entity."type" = ${type}::"company"."EntityTypeE"` : Prisma.empty}
              ),
              raw_entity AS (
                SELECT 1
                FROM "rawData"."EntityRawData" entityRawData
                WHERE
                  entityRawData."name" ILIKE ${name + '%'}
                  ${type ? Prisma.sql`AND entityRawData."type" = ${type}::"company"."EntityTypeE"` : Prisma.empty}
              ),
              combined AS (
                SELECT * FROM master_entity
                UNION ALL
                SELECT * FROM raw_entity
              )
              SELECT COUNT(*)::INTEGER AS total FROM combined
            `,
    ]);

    return {
      data: entitiesResultTemp,
      total: Number(entitiesTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async (params: EntityModuleFetchsertParamsI): Promise<EntityClientI> => {
    const [entityResult, entityRawDataResult] = await Promise.all([
      prismaPG.entity.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
          type: params.type,
        },
        select: { id: true, name: true, type: true },
      }),
      prismaPG.entityRawData.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
          type: params.type,
        },
        select: { id: true, name: true, type: true },
      }),
    ]);
    if (entityResult) {
      return { ...entityResult, dataType: 'master' as DBDataTypeI } as EntityClientI;
    } else if (entityRawDataResult) {
      return { ...entityRawDataResult, dataType: 'raw' as DBDataTypeI } as EntityClientI;
    }
    const result = await prismaPG.entityRawData.create({ data: params });
    if (!result?.id) {
      throw new AppError('ORG002');
    }
    return { ...result, dataType: 'raw' as DBDataTypeI } as unknown as EntityClientI;
  },
  count: async (masterEntities: string[], rawDataEntities: string[]) => {
    masterEntities = Array.from(new Set<string>(masterEntities));
    rawDataEntities = Array.from(new Set<string>(rawDataEntities));
    const [countMasterEntities, countRawDataEntities] = await Promise.all([
      masterEntities?.length
        ? prismaPG.entity.count({
            where: {
              id: {
                in: masterEntities,
              },
            },
          })
        : 0,
      rawDataEntities?.length
        ? prismaPG.entityRawData.count({
            where: {
              id: {
                in: rawDataEntities,
              },
            },
          })
        : 0,
    ]);
    return { countMasterEntities, countRawDataEntities };
  },
};
