import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { CertificateCourseTypeI } from '@consts/company/certificateCourse';
import { ObjUnknownI, TotalDataI } from '@interfaces/common/data';
import {
  CertificateCourseClientI,
  CertificateCourseNestedClientI,
  CertificateCourseModuleFetchsertParamsI,
} from '@interfaces/company/certificateCourse';
import type { IdNameI } from '@consts/master/common';
import { Prisma } from '@prisma/postgres';
import { IdTypeI } from '@schemas/common/common';

export const CertificateCourseModule = {
  fetchById: async (
    filters: IdTypeI,
    select: IdNameI & { type: boolean } = { id: true, name: true, type: true },
    _isThrowingError: boolean = true,
  ): Promise<CertificateCourseClientI> => {
    if (filters.dataType === 'raw') {
      const certificateCourseRawDataResult = await prismaPG.certificateCourseRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(certificateCourseRawDataResult as ObjUnknownI),
        dataType: 'raw',
      } as CertificateCourseClientI;
    } else if (filters.dataType === 'master') {
      const certificateCourseResult = await prismaPG.certificateCourse.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(certificateCourseResult as ObjUnknownI),
        dataType: 'master',
      } as CertificateCourseClientI;
    }

    if (_isThrowingError) {
      throw new AppError('ORG001');
    }
  },
  fetchForClient: async (
    name?: string,
    type?: CertificateCourseTypeI,
    { page, pageSize } = PAGINATION,
  ): Promise<TotalDataI<CertificateCourseNestedClientI>> => {
    name = name?.trim()?.toLowerCase();

    const [certificateCoursesResultTemp, certificateCoursesTotalResult] = await Promise.all([
      prismaPG.$queryRaw<CertificateCourseNestedClientI[]>`
        SELECT *
        FROM
        (
          (SELECT
          cc."id",
          cc."name",
          cc."type"::text,
          'master' AS "dataType"
          FROM
          "company"."CertificateCourse" cc
          WHERE
          cc."name" ILIKE ${name + '%'}
          ${type ? Prisma.sql`AND cc."type" = ${type}::"company"."CertificateCourseTypeE"` : Prisma.empty})
          UNION ALL
          (SELECT
          ccrw."id",
          ccrw."name",
          ccrw."type"::text,
          'raw' AS "dataType"
          FROM
          "rawData"."CertificateCourseRawData" ccrw
          WHERE
          ccrw."name" ILIKE ${name + '%'}
          ${type ? Prisma.sql`AND ccrw."type" = ${type}::"company"."CertificateCourseTypeE"` : Prisma.empty})
        )
        ORDER BY "name" ASC
        OFFSET ${page * pageSize}
        LIMIT ${pageSize}
      `,
      prismaPG.$queryRaw<{ total: number }[]>`
        WITH master_certificate_course AS (
          SELECT 1
          FROM "company"."CertificateCourse" certificate
          WHERE
            certificate."name" ILIKE ${name + '%'}
            ${type ? Prisma.sql`AND certificate."type" = ${type}::"company"."CertificateCourseTypeE"` : Prisma.empty}
        ),
        raw_certificate_course AS (
          SELECT 1
          FROM "rawData"."CertificateCourseRawData" certificateRawData
          WHERE
            certificateRawData."name" ILIKE ${name + '%'}
            ${type ? Prisma.sql`AND certificateRawData."type" = ${type}::"company"."CertificateCourseTypeE"` : Prisma.empty}
        ),
        combined AS (
          SELECT * FROM master_certificate_course
          UNION ALL
          SELECT * FROM raw_certificate_course
        )
        SELECT COUNT(*)::INTEGER AS total FROM combined
      `,
    ]);

    return {
      data: certificateCoursesResultTemp,
      total: Number(certificateCoursesTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async (params: CertificateCourseModuleFetchsertParamsI): Promise<CertificateCourseClientI> => {
    const cleanName = params.name.trim().toLowerCase();
    const existingCertificateResult = await prismaPG.$queryRaw<CertificateCourseClientI[]>`
      SELECT *
      FROM (
        (SELECT
          cc."id",
          cc."name",
          'master' AS "dataType"
        FROM "company"."CertificateCourse" cc
        WHERE
          cc."name" ILIKE ${cleanName}
          AND cc."type" = ${params.type}::"company"."CertificateCourseTypeE"
        LIMIT 1)

        UNION ALL

        (SELECT
          ccrw."id",
          ccrw."name",
          'raw' AS "dataType"
        FROM "rawData"."CertificateCourseRawData" ccrw
        WHERE
          ccrw."name" ILIKE ${cleanName}
          AND ccrw."type" = ${params.type}::"company"."CertificateCourseTypeE"
        LIMIT 1)
      ) AS combined
      LIMIT 1
    `;
    const existingCertificate = existingCertificateResult[0];

    if (existingCertificate) {
      return existingCertificate;
    }

    const result = await prismaPG.certificateCourseRawData.create({
      data: {
        ...params,
        name: cleanName,
      },
    });

    if (!result?.id) {
      throw new AppError('CRTCR002');
    }

    return {
      id: result.id,
      name: result.name,
      dataType: 'raw' as DBDataTypeI,
    } as CertificateCourseClientI;
  },
  count: async (masterCertificateCourses: string[], rawDataCertificateCourses: string[]) => {
    masterCertificateCourses = Array.from(new Set<string>(masterCertificateCourses));
    rawDataCertificateCourses = Array.from(new Set<string>(rawDataCertificateCourses));
    const [countMasterCertificateCourses, countRawDataCertificateCourses] = await Promise.all([
      masterCertificateCourses?.length
        ? prismaPG.certificateCourse.count({
            where: {
              id: {
                in: masterCertificateCourses,
              },
            },
          })
        : 0,
      rawDataCertificateCourses?.length
        ? prismaPG.certificateCourseRawData.count({
            where: {
              id: {
                in: rawDataCertificateCourses,
              },
            },
          })
        : 0,
    ]);
    return { countMasterCertificateCourses, countRawDataCertificateCourses };
  },
};
