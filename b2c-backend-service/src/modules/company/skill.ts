import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import type { IdNameI } from '@consts/master/common';
import type { ObjUnknownI, TotalDataI } from '@interfaces/common/data';
import type { SkillClientI, SkillModuleFetchsertParamsI, SkillNestedClientI } from '@interfaces/company/skill';
import type { IdTypeI } from '@schemas/common/common';

export const SkillModule = {
  fetchById: async (
    filters: IdTypeI,
    select: IdNameI = { id: true, name: true },
    _isThrowingError: boolean = true,
  ): Promise<SkillClientI> => {
    if (filters.dataType === 'raw') {
      const skillRawDataResult = await prismaPG.skillRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(skillRawDataResult as ObjUnknownI),
        dataType: 'raw',
      } as SkillClientI;
    } else if (filters.dataType === 'master') {
      const skillResult = await prismaPG.skill.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(skillResult as ObjUnknownI),
        dataType: 'master',
      } as SkillClientI;
    }
    if (_isThrowingError) {
      throw new AppError('ORG001');
    }
  },
  fetchForClient: async (name?: string, { page, pageSize } = PAGINATION): Promise<TotalDataI<SkillNestedClientI>> => {
    name = name?.trim()?.toLowerCase();

    const [skillsResultTemp, skillsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<SkillNestedClientI[]>`
          SELECT *
          FROM
          (
            (SELECT
            s."id",
            s."name",
            s."category"::text,
            'master' AS "dataType"
            FROM
            "company"."Skill" s
            WHERE
            s."name" ILIKE ${name + '%'})
            UNION ALL
            (SELECT
            srw."id",
            srw."name",
            srw."category"::text,
            'raw' AS "dataType"
            FROM
            "rawData"."SkillRawData" srw
            WHERE
            srw."name" ILIKE ${name + '%'})
          )
          ORDER BY "name" ASC
          OFFSET ${page * pageSize}
          LIMIT ${pageSize}
      `,
      prismaPG.$queryRaw<{ total: number }[]>`
              WITH master_skill AS (
                SELECT 1
                FROM "company"."Skill" skill
                WHERE
                  skill."name" ILIKE ${name + '%'}
              ),
              raw_skill AS (
                SELECT 1
                FROM "rawData"."SkillRawData" skillRawData
                WHERE
                  skillRawData."name" ILIKE ${name + '%'}
              ),
              combined AS (
                SELECT * FROM master_skill
                UNION ALL
                SELECT * FROM raw_skill
              )
              SELECT COUNT(*)::INTEGER AS total FROM combined
            `,
    ]);

    return {
      data: skillsResultTemp,
      total: Number(skillsTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async (params: SkillModuleFetchsertParamsI): Promise<SkillClientI> => {
    const [skillResult, skillRawDataResult] = await Promise.all([
      prismaPG.skill.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true },
      }),
      prismaPG.skillRawData.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true },
      }),
    ]);
    if (skillResult) {
      return { ...skillResult, dataType: 'master' as DBDataTypeI } as SkillClientI;
    } else if (skillRawDataResult) {
      return { ...skillRawDataResult, dataType: 'raw' as DBDataTypeI } as SkillClientI;
    }
    const result = await prismaPG.skillRawData.create({ data: params });
    if (!result?.id) {
      throw new AppError('ORG002');
    }
    return { ...result, dataType: 'raw' as DBDataTypeI } as SkillClientI;
  },
  count: async (masterSkills: string[], rawDataSkills: string[]) => {
    masterSkills = Array.from(new Set<string>(masterSkills));
    rawDataSkills = Array.from(new Set<string>(rawDataSkills));
    const [countMasterSkills, countRawDataSkills] = await Promise.all([
      masterSkills?.length
        ? prismaPG.skill.count({
            where: {
              id: {
                in: masterSkills,
              },
            },
          })
        : 0,
      rawDataSkills?.length
        ? prismaPG.skillRawData.count({
            where: {
              id: {
                in: rawDataSkills,
              },
            },
          })
        : 0,
    ]);
    return { countMasterSkills, countRawDataSkills };
  },
};
