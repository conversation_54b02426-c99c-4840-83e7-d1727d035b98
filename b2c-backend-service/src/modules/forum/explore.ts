import { FastifyStateI } from '@interfaces/common/declaration';
import { ForumExploreOptions, ForumExploreI } from '@interfaces/forum/explore';

import ForumModule from '.';

export const ExploreModule = {
  fetchExploreData: async (state: FastifyStateI, options?: ForumExploreOptions): Promise<ForumExploreI> => {
    const days = options?.days || 7;
    const limit = options?.limit || 5;

    const [topics, troubleshootQuestions, recommendedCommunities] = await Promise.all([
      ForumModule.TopicModule.fetchExploreTopics(),
      ForumModule.QuestionModule.fetchTroubleshootQuestions(state, days, limit, options?.equipmentFilter),
      ForumModule.CommunityModule.fetchRecommendedCommunities(state, days, limit),
    ]);

    return {
      topics,
      troubleshootQuestions,
      recommendedCommunities: recommendedCommunities,
    };
  },
};
