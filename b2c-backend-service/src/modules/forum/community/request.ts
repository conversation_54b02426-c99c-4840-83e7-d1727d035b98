import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import { CommunityRequestCreateOneResultI } from '@interfaces/forum/request';
import {
  CommunitRequestApprovalQueryI,
  CommunityRequestApprovalBodyI,
  CommunityRequestBodyI,
  CommunityRequestRejectionQueryI,
  CommunityRequestRevocationQueryI,
} from '@schemas/forum/request';
import { CommunityAccessE, MemberTypeE, CommunityRequestStatusE } from '@prisma/postgres';
import { errorHandler } from '@utils/errors/handler';

const CommunityRequestModule = {
  createOne: async ({
    communityId,
    profileId,
    requestedType,
  }: CommunityRequestBodyI): Promise<CommunityRequestCreateOneResultI> => {
    const existingMember = await prismaPG.communityMember.findFirst({
      where: {
        communityId,
        profileId,
      },
    });
    if (existingMember) {
      throw new AppError('CMRQ003');
    }
    const existingRequest = await prismaPG.communityRequest.findFirst({
      where: {
        communityId,
        profileId,
        status: CommunityRequestStatusE.PENDING,
      },
    });
    if (existingRequest) {
      throw new AppError('CMRQ002');
    }
    const revokedRequest = await prismaPG.communityRequest.findFirst({
      where: {
        communityId,
        profileId,
        status: CommunityRequestStatusE.REVOKED,
      },
    });
    const community = await prismaPG.community.findUnique({
      where: { id: communityId },
      select: {
        access: true,
        isRestricted: true,
      },
    });

    if (!community) {
      throw new AppError('CMTY001');
    }
    let status: CommunityRequestStatusE = CommunityRequestStatusE.PENDING;
    let acceptedType: MemberTypeE | null = null;
    const isPublic = community.access === CommunityAccessE.PUBLIC;
    const isPublicRestrictive = isPublic && community.isRestricted;

    if (isPublic && !isPublicRestrictive) {
      if (([MemberTypeE.MEMBER, MemberTypeE.CONTRIBUTOR] as MemberTypeE[]).includes(requestedType)) {
        status = CommunityRequestStatusE.ACCEPTED;
        acceptedType = requestedType;
      }
    } else if (isPublicRestrictive && requestedType === MemberTypeE.MEMBER) {
      status = CommunityRequestStatusE.ACCEPTED;
      acceptedType = requestedType;
    }
    const result = await prismaPG.$transaction(async (tx) => {
      let communityRequestResult: CommunityRequestCreateOneResultI;
      if (revokedRequest) {
        communityRequestResult = await tx.communityRequest.update({
          where: {
            communityId_profileId: {
              communityId,
              profileId,
            },
          },
          data: {
            requestedType,
            acceptedType,
            status,
          },
        });
      } else {
        communityRequestResult = await tx.communityRequest.create({
          data: {
            communityId,
            profileId,
            requestedType,
            acceptedType,
            status,
          },
        });
      }
      if (status === CommunityRequestStatusE.ACCEPTED && acceptedType) {
        await tx.communityMember.create({
          data: {
            communityId,
            profileId,
            type: acceptedType,
          },
        });
        await tx.community.update({
          where: { id: communityId },
          data: {
            memberCount: { increment: 1 },
          },
        });
      }
      return communityRequestResult;
    });

    return result;
  },
  approveRequestTypeChange: async (
    state: FastifyStateI,
    { profileId, communityId }: CommunitRequestApprovalQueryI,
    { acceptedType }: CommunityRequestApprovalBodyI,
  ): Promise<CommunityRequestCreateOneResultI> => {
    const adminProfileId = state.profileId;
    const communityMemberResult = await prismaPG.communityMember.findFirst({
      where: {
        profileId: adminProfileId,
        communityId,
      },
    });

    if (!communityMemberResult || communityMemberResult.type !== 'ADMIN') {
      throw new AppError('CMTY004');
    }

    const request = await prismaPG.communityRequest.findFirst({
      where: {
        profileId,
        communityId,
        status: CommunityRequestStatusE.PENDING,
      },
    });
    if (!request) {
      throw new AppError('CMRQ004');
    }
    const status =
      request.requestedType === acceptedType
        ? CommunityRequestStatusE.ACCEPTED
        : CommunityRequestStatusE.PARTIALLY_ACCEPTED;

    const result = await prismaPG.$transaction(async (tx) => {
      const communityRequestResult = await tx.communityRequest.update({
        where: {
          communityId_profileId: {
            communityId,
            profileId,
          },
        },
        data: {
          acceptedType,
          status,
        },
      });

      if ([CommunityRequestStatusE.ACCEPTED, CommunityRequestStatusE.PARTIALLY_ACCEPTED].includes(status)) {
        await prismaPG.$transaction([
          tx.communityMember.create({
            data: { communityId, profileId, type: acceptedType },
          }),
          tx.community.update({
            where: { id: communityId },
            data: { memberCount: { increment: 1 } },
          }),
        ]);
      }
      return communityRequestResult;
    });

    return result;
  },
  rejectRequest: async (
    state: FastifyStateI,
    { profileId, communityId }: CommunityRequestRejectionQueryI,
  ): Promise<void> => {
    try {
      const adminProfileId = state.profileId;
      const adminMember = await prismaPG.communityMember.findFirst({
        where: {
          profileId: adminProfileId,
          communityId,
          type: MemberTypeE.ADMIN,
        },
      });

      if (!adminMember) {
        throw new AppError('CMTY004');
      }

      const request = await prismaPG.communityRequest.findFirst({
        where: {
          profileId,
          communityId,
          status: CommunityRequestStatusE.PENDING,
        },
      });

      if (!request) {
        throw new AppError('CMRQ004');
      }

      await prismaPG.communityRequest.delete({
        where: {
          communityId_profileId: {
            communityId,
            profileId,
          },
        },
      });
    } catch (error) {
      errorHandler(error);
    }
  },

  revokeRequest: async ({ profileId, communityId }: CommunityRequestRevocationQueryI): Promise<void> => {
    const request = await prismaPG.communityRequest.findFirst({
      where: {
        profileId,
        communityId,
        status: CommunityRequestStatusE.PENDING,
      },
    });

    if (!request) {
      throw new AppError('CMRQ004');
    }

    await prismaPG.communityRequest.update({
      where: {
        communityId_profileId: {
          communityId,
          profileId,
        },
      },
      data: {
        status: CommunityRequestStatusE.REVOKED,
      },
    });
  },
};

export default CommunityRequestModule;
