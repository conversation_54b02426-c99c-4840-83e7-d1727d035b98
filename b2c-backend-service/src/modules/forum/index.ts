import CommunityMemberModule from './member';
import CommunityRequestModule from './request';
import { QuestionModule } from './question/question';
import { AnswerModule } from './answer/answer';
import { QuestionCommentModule } from './question/comment';
import { AnswerCommentModule } from './answer/comment';
import { AnswerVoteModule } from './answer/vote';
import { TopicModule } from './topic';
import { QuestionVoteModule } from './question/vote';
import { CommunityModule } from './community/community';
import { ExploreModule } from './explore';

const ForumModule = {
  AnswerModule,
  AnswerCommentModule,
  AnswerVoteModule,
  CommunityModule,
  QuestionModule,
  QuestionVoteModule,
  CommunityMemberModule,
  CommunityRequestModule,
  QuestionCommentModule,
  TopicModule,
  ExploreModule,
};

export default ForumModule;
