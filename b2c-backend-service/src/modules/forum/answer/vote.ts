import type { FastifyStateI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';
import { AnswerModule } from './answer';
import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';
import type {
  ForumAnswerVoteCreateOneI,
  ForumAnswerVoteDeleteOneI,
  ForumAnswerVoteFetchManyI,
} from '@schemas/forum/vote';
import type { ProfileExternalI } from '@interfaces/user/profile';
import type { IdCursorIdI, TotalCursorDataI } from '@interfaces/common/data';
import User from '@modules/user';
import { CommunityModule } from '../community/community';

export const AnswerVoteModule = {
  fetchOne: async (
    filters: Prisma.AnswerVoteWhereUniqueInput,
    select: Prisma.AnswerVoteSelect = {
      id: true,
    },
    isThrowingError: boolean = true,
  ) => {
    const voteResult = await prismaPG.answerVote.findUnique({
      select,
      where: filters,
    });
    if (isThrowingError && !voteResult) {
      throw new AppError('FMANS002');
    }
    return voteResult;
  },
  createOne: async (
    state: FastifyStateI,
    { answerId, type: requestedType }: ForumAnswerVoteCreateOneI,
  ): Promise<IdCursorIdI> => {
    const selfProfileId = state.profileId;
    const answerResult = await AnswerModule.fetchById({ id: answerId }, { communityId: true, questionId: true });
    const communityId = answerResult.communityId;

    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });
    const existingVoteResult = await AnswerVoteModule.fetchOne(
      {
        answerId_profileId: { answerId, profileId: selfProfileId },
      },
      {
        id: true,
        type: true,
      },
      false,
    );
    if (existingVoteResult?.type === requestedType) {
      switch (requestedType) {
        case 'UPVOTE': {
          throw new AppError('FMAVT003');
        }
        case 'DOWNVOTE': {
          throw new AppError('FMAVT004');
        }
      }
    }
    const [voteResult, _answerResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          existingVoteResult
            ? txn.answerVote.update({
                data: {
                  type: requestedType,
                },
                select: { id: true, cursorId: true },
                where: { id: existingVoteResult.id },
              })
            : txn.answerVote.create({
                data: {
                  answerId,
                  communityId,
                  questionId: answerResult.questionId,
                  profileId: selfProfileId,
                  type: requestedType,
                },
                select: { id: true, cursorId: true },
              }),
          txn.answer.update({
            data:
              requestedType === 'UPVOTE'
                ? {
                    upvoteCount: {
                      increment: 1,
                    },
                  }
                : {
                    downvoteCount: {
                      increment: 1,
                    },
                  },
            where: {
              id: answerId,
            },
            select: { id: true },
          }),
        ]),
    );

    if (!voteResult) {
      throw new AppError('FMANS001');
    }
    return { id: voteResult.id, cursorId: Number(voteResult.cursorId) };
  },
  deleteOne: async (state: FastifyStateI, { answerId }: ForumAnswerVoteDeleteOneI): Promise<void> => {
    const selfProfileId = state.profileId;
    const existingVoteResult = await AnswerVoteModule.fetchOne(
      { answerId_profileId: { answerId, profileId: selfProfileId } },
      {
        id: true,
        answerId: true,
        communityId: true,
        type: true,
        Answer: {
          select: {
            upvoteCount: true,
            downvoteCount: true,
          },
        },
      },
    );
    const communityId = existingVoteResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    const [deletedVoteResult, _updatedAnswerResult] = await prismaPG.$transaction(async (txn) => {
      const [deletedVoteResult, updatedAnswerResult] = await Promise.all([
        txn.answerVote.delete({
          select: { id: true },
          where: { id: existingVoteResult.id },
        }),
        existingVoteResult.type === 'UPVOTE' && existingVoteResult?.Answer?.upvoteCount > 0
          ? txn.answer.update({
              data: {
                upvoteCount: {
                  decrement: 1,
                },
              },
              select: { id: true },
              where: {
                id: existingVoteResult.answerId,
              },
            })
          : existingVoteResult.type === 'DOWNVOTE' && existingVoteResult?.Answer?.downvoteCount > 0
            ? txn.answer.update({
                data: {
                  downvoteCount: {
                    decrement: 1,
                  },
                },
                select: { id: true },
                where: {
                  id: existingVoteResult.answerId,
                },
              })
            : null,
      ]);
      return [deletedVoteResult, updatedAnswerResult];
    });
    if (!deletedVoteResult) {
      throw new AppError('FMAVT005');
    }
    return;
  },
  fetchMany: async (
    state: FastifyStateI,
    { answerId, cursorId, pageSize, type }: ForumAnswerVoteFetchManyI,
  ): Promise<TotalCursorDataI<ProfileExternalI>> => {
    const selfProfileId = state.profileId;
    const filters: Prisma.AnswerVoteWhereInput = {
      answerId,
      type,
      Profile: {
        status: 'ACTIVE',
      },
      NOT: {
        Profile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: selfProfileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: selfProfileId,
                },
              },
            },
          ],
        },
      },
    };
    const [total, votedProfileResultTemp] = await Promise.all([
      prismaPG.answerVote.count({
        where: filters,
      }),
      prismaPG.answerVote.findMany({
        select: {
          Profile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
            },
          },
        },
        where: {
          ...filters,
          ...(typeof cursorId === 'number' && cursorId > 0
            ? {
                cursorId: {
                  lt: BigInt(cursorId),
                },
              }
            : {}),
        },
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);
    const votedProfileResult: ProfileExternalI[] = [];
    let nextCursorId = null;
    if (votedProfileResultTemp?.length) {
      votedProfileResult.push(
        ...votedProfileResultTemp.map(
          (votedProfile) => User.ProfileModule.transformProfile(votedProfile.Profile) as ProfileExternalI,
        ),
      );
      nextCursorId = votedProfileResult[votedProfileResult.length - 1].cursorId;
    }
    return { total, data: votedProfileResult, nextCursorId };
  },
};
