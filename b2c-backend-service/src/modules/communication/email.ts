import AppError from '@classes/AppError';
import ServiceModule from '@modules/service';
import {
  CommunicationServiceAppError,
  type CommunicationEmailSendOneReqI,
  type CommunicationEmailVerifyReqI,
} from '@navicater/b2c-internal-communication';

const EmailModule = {
  sendOne: async ({ email, name, profileId, type }: CommunicationEmailSendOneReqI): Promise<void> => {
    try {
      const { instance } = await ServiceModule.InternalServiceModule.getVendor('COMMUNICATION');
      const _sendOneEmailResult = await instance.sendOneEmail({ email, name: name ?? 'User', profileId, type });
      return;
    } catch (_error) {
      throw new AppError('CMEML002');
    }
  },
  verify: async ({ otp, profileId, type }: CommunicationEmailVerifyReqI): Promise<void> => {
    try {
      const { instance } = await ServiceModule.InternalServiceModule.getVendor('COMMUNICATION');
      const _verifyEmailResult = await instance.verifyEmail({ otp, profileId, type });
      return;
    } catch (_error) {
      if (_error instanceof CommunicationServiceAppError) {
        const error = _error as CommunicationServiceAppError;
        if (error.code === 'CMVFN004') {
          throw new AppError('CMVFN004');
        }
      }
      throw new AppError('CMEML004');
    }
  },
};
export default EmailModule;
