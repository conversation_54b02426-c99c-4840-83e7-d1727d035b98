import { KafkaTopicE, KafkaTopicI } from '@consts/kafka';
import AppError from '@classes/AppError';
import {
  SessionCreateOneI,
  SessionCreateOneSchema,
  SessionDelsertMessageI,
  SessionDelsertMessageSchema,
  SessionUpdateOneI,
  SessionUpdateOneSchema,
} from '@schemas/communication/session';
import KafkaService from 'services/kafka';

const SessionModule = {
  createOne: async (params: SessionCreateOneI): Promise<void> => {
    const { data, error } = SessionCreateOneSchema.safeParse(params);
    if (error) {
      throw new AppError('CMN004');
    }
    await SessionModule.delsertOne('session_topic', {
      deviceToken: data.deviceToken,
      isActive: data.isActive,
      opr: 'CREATE',
      profileId: data.profileId,
      sessionId: data.sessionId,
    });
  },
  deleteOne: async (params: SessionUpdateOneI): Promise<void> => {
    try {
      const { data, error } = SessionUpdateOneSchema.safeParse(params);
      if (error) {
        throw new AppError('CMN004');
      }
      await SessionModule.delsertOne('session_topic', {
        opr: 'DELETE',
        sessionId: data.sessionId,
      });
    } catch (_error) {
      //
    }
  },
  delsertOne: async (topic: KafkaTopicI, params: SessionDelsertMessageI) => {
    const { data: kafkaTopicData, error: kafkaTopicError } = KafkaTopicE.safeParse(topic);
    if (kafkaTopicError) {
      throw new AppError('CMN002');
    }
    const { data: sessionData, error: sessionError } = SessionDelsertMessageSchema.safeParse(params);
    if (sessionError) {
      throw new AppError('CMN003');
    }
    await KafkaService.instance.sendMessage(kafkaTopicData, sessionData);
    return;
  },
};
export default SessionModule;
