import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';

import { Country, Prisma } from '@prisma/postgres';
import { CountryOptionsFetchI } from '@schemas/master/country';

export const CountryModule = {
  fetchOne: async (
    filters: Prisma.CountryWhereUniqueInput,
    select: Prisma.CountrySelect = { iso2: true, name: true },
  ): Promise<Country> => {
    const countryResult = await prismaPG.country.findUnique({
      where: filters,
      select,
    });
    if (!countryResult) {
      throw new AppError('CNRY001');
    }
    return countryResult;
  },
  fetch: async (
    { page, pageSize, search }: CountryOptionsFetchI,
    orderBy: Prisma.CountryOrderByWithRelationInput = { name: 'asc' },
    select: Prisma.CountrySelect = { iso2: true, name: true },
  ): Promise<{ data: Country[]; total: number }> => {
    const filters: Prisma.CountryWhereInput = {};
    if (search?.length) {
      filters.name = {
        startsWith: search,
        mode: 'insensitive',
      };
    }
    const countryResult = await prismaPG.country.findMany({
      where: filters,
      skip: page,
      take: pageSize,
      orderBy,
      select,
    });

    const totalResult = await prismaPG.$queryRaw<{ total: number }[]>`
              SELECT COUNT(*)::INTEGER AS total FROM "master"."Country"
            `;

    return {
      data: countryResult,
      total: totalResult?.[0]?.total || 0,
    };
  },
};
