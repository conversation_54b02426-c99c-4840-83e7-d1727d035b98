import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { FollowDataI, FollowExternalI } from '@interfaces/network/follow';
import type { ProfileNetworkExternalI } from '@interfaces/user/profile';
import CommunicationModule from '@modules/communication';
import { Prisma, Follow } from '@prisma/postgres';
import type { ProfileIdCursorPaginationI } from '@schemas/common/common';
import type { FollowOneParamsI } from '@schemas/network/follow';
import { errorHandler } from '@utils/errors/handler';

export const FollowModule = {
  followOne: async (
    state: FastifyStateI,
    { followeeProfileId }: FollowOneParamsI,
  ): Promise<Omit<Follow, 'cursorId'> & { cursorId: number }> => {
    const selfProfileId = state.profileId;
    const blockedProfileResult = await prismaPG.blockedProfile.findFirst({
      select: { blockedId: true },
      where: {
        Blocked: {
          status: 'ACTIVE',
        },
        OR: [
          {
            blockerId: followeeProfileId,
            blockedId: selfProfileId,
          },
          {
            blockerId: selfProfileId,
            blockedId: followeeProfileId,
          },
        ],
      },
    });
    if (blockedProfileResult) {
      throw new AppError('PFL001');
    }
    const existingFollowResult = await prismaPG.follow.findUnique({
      where: {
        followerProfileId_followeeProfileId: {
          followerProfileId: selfProfileId,
          followeeProfileId: followeeProfileId,
        },
      },
    });

    if (existingFollowResult) throw new AppError('FLW005');
    const [followResult, _followeeProfileResult, _followerProfileResult, selfProfileResult] =
      await prismaPG.$transaction([
        prismaPG.follow.create({
          data: {
            followerProfileId: state.profileId,
            followeeProfileId,
          },
        }),
        prismaPG.profile.update({
          data: {
            followersCount: {
              increment: 1,
            },
          },
          select: {
            id: true,
            followersCount: true,
          },
          where: {
            id: followeeProfileId,
          },
        }),
        prismaPG.profile.update({
          data: {
            followingsCount: {
              increment: 1,
            },
          },
          select: {
            id: true,
            followersCount: true,
          },
          where: {
            id: state.profileId,
          },
        }),
        prismaPG.profile.findUnique({ select: { name: true }, where: { id: selfProfileId } }),
      ]);
    if (!followResult) {
      throw new AppError('FLW002');
    }

    await CommunicationModule.NotificationModule.createOne({
      actorProfileId: selfProfileId,
      actorProfileName: selfProfileResult?.name ?? 'Unknown',
      profileId: followeeProfileId,
      topic: 'communication_topic',
      type: 'FOLLOWER',
    });

    return {
      ...followResult,
      cursorId: Number(followResult.cursorId),
    };
  },
  unfollowOne: async (state: FastifyStateI, { followeeProfileId }: FollowOneParamsI): Promise<void> => {
    try {
      const blockedProfileResult = await prismaPG.blockedProfile.findFirst({
        select: { blockedId: true },
        where: {
          Blocked: {
            status: 'ACTIVE',
          },
          OR: [
            {
              blockerId: followeeProfileId,
              blockedId: state.profileId,
            },
            {
              blockerId: state.profileId,
              blockedId: followeeProfileId,
            },
          ],
        },
      });
      if (blockedProfileResult) {
        throw new AppError('PFL001');
      }
      const [unfollowResult, _followeeProfileResult, _followerProfileResult] = await prismaPG.$transaction([
        prismaPG.follow.delete({
          select: { followerProfileId: true },
          where: {
            followerProfileId_followeeProfileId: {
              followeeProfileId,
              followerProfileId: state.profileId,
            },
          },
        }),
        prismaPG.profile.update({
          data: {
            followersCount: {
              decrement: 1,
            },
          },
          select: {
            id: true,
          },
          where: {
            id: followeeProfileId,
          },
        }),
        prismaPG.profile.update({
          data: {
            followingsCount: {
              decrement: 1,
            },
          },
          select: {
            id: true,
          },
          where: {
            id: state.profileId,
          },
        }),
      ]);
      if (!unfollowResult) {
        throw new AppError('PFL001');
      }
    } catch (error) {
      errorHandler(error, {
        RECORD_NOT_FOUND: 'FLW006',
      });
    }
  },
  followStatus: async (
    state: FastifyStateI,
    { followeeProfileId }: FollowOneParamsI,
  ): Promise<{ isFollowing: boolean }> => {
    const selfProfileId = state.profileId;
    const existingFollowResult = await prismaPG.follow.count({
      where: {
        followerProfileId: selfProfileId,
        followeeProfileId,
      },
    });
    return { isFollowing: Boolean(existingFollowResult) };
  },
  fetchManyFollowers: async (
    state: FastifyStateI,
    { cursorId, pageSize, profileId }: ProfileIdCursorPaginationI,
  ): Promise<{ data: FollowExternalI[]; total: number }> => {
    try {
      const selfProfileId = state.profileId;
      const followers: FollowExternalI[] = [];

      const [followersResultTemp, totalFollowers] = await Promise.all([
        prismaPG.$queryRaw<FollowDataI[]>`
          SELECT
            f."cursorId" AS "cursorId",
            json_build_object(
              'id', u."id",
              'name', u."name",
              'avatar', u."avatar",
              'designationText', u."designationText",
              'designationAlternativeId', u."designationAlternativeId",
              'designationRawDataId', u."designationRawDataId",
              'entityText', u."entityText",
              'entityId', u."entityId",
              'entityRawDataId', u."entityRawDataId"
              ${
                selfProfileId !== profileId
                  ? Prisma.sql`,
                'isConnected', EXISTS (
                  SELECT 1 FROM "network"."Connection" c
                  WHERE c."profileId" = ${selfProfileId}::uuid
                  AND c."connectedId" = u."id"
                ),
                'isFollowing', EXISTS (
                  SELECT 1 FROM "network"."Follow" f2
                  WHERE f2."followerProfileId" = ${selfProfileId}::uuid
                  AND f2."followeeProfileId" = u."id"
                )`
                  : Prisma.empty
              }
            ) AS "Profile"
          FROM "network"."Follow" f
          INNER JOIN "user"."Profile" u
            ON f."followerProfileId" = u."id"
            AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          WHERE f."followeeProfileId" = ${profileId}::uuid
            AND NOT EXISTS (
              SELECT 1 FROM "network"."BlockedProfile" b
              WHERE (
                (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = f."followerProfileId")
                OR
                (b."blockerId" = f."followerProfileId" AND b."blockedId" = ${selfProfileId}::uuid)
              )
            )
            ${typeof cursorId === 'number' ? Prisma.sql`AND f."cursorId" < ${cursorId}` : Prisma.empty}
          ORDER BY f."createdAt" DESC
          LIMIT ${pageSize}
        `,
        prismaPG.follow.count({
          where: {
            followeeProfileId: profileId,
            FolloweeProfile: {
              status: 'ACTIVE',
            },
            NOT: {
              OR: [
                {
                  FollowerProfile: {
                    BlockedByProfile: { some: { blockerId: selfProfileId } },
                  },
                },
                {
                  FollowerProfile: {
                    BlockedProfile: { some: { blockedId: selfProfileId } },
                  },
                },
              ],
            },
          },
        }),
      ]);

      if (followersResultTemp?.length) {
        followers.push(
          ...followersResultTemp.map(
            (item) =>
              ({
                cursorId: Number(item.cursorId),
                Profile: {
                  id: item.Profile.id,
                  name: item.Profile.name,
                  avatar: item.Profile.avatar,
                  designation: item.Profile?.designationAlternativeId
                    ? {
                        id: item.Profile.designationAlternativeId,
                        name: item.Profile.designationText,
                        dataType: 'master',
                      }
                    : item.Profile?.designationRawDataId
                      ? {
                          id: item.Profile.designationRawDataId,
                          name: item.Profile.designationText,
                          dataType: 'raw',
                        }
                      : null,
                  entity: item.Profile?.entityId
                    ? {
                        id: item.Profile.entityId,
                        name: item.Profile.entityText,
                        dataType: 'master',
                      }
                    : item.Profile?.entityRawDataId
                      ? {
                          id: item.Profile.entityRawDataId,
                          name: item.Profile.entityText,
                          dataType: 'raw',
                        }
                      : null,
                  isConnected: item.isConnected,
                  isFollowing: item.isFollowing,
                } as ProfileNetworkExternalI,
              }) as FollowExternalI,
          ),
        );
      }

      return {
        data: followers,
        total: totalFollowers,
      };
    } catch (error) {
      throw new AppError('FLW001', error);
    }
  },
  fetchManyFollowings: async (
    state: FastifyStateI,
    { cursorId, pageSize, profileId }: ProfileIdCursorPaginationI,
  ): Promise<{ data: FollowExternalI[]; total: number }> => {
    try {
      const selfProfileId = state.profileId;
      const followings: FollowExternalI[] = [];

      const [followingsResultTemp, followingsCountResult] = await Promise.all([
        prismaPG.$queryRaw<FollowDataI[]>`
          SELECT
            f."cursorId" AS "cursorId",
            json_build_object(
              'id', u."id",
              'name', u."name",
              'avatar', u."avatar",
              'designationText', u."designationText",
              'designationAlternativeId', u."designationAlternativeId",
              'designationRawDataId', u."designationRawDataId",
              'entityText', u."entityText",
              'entityId', u."entityId",
              'entityRawDataId', u."entityRawDataId"
              ${
                selfProfileId !== profileId
                  ? Prisma.sql`,
                'isConnected', EXISTS (
                  SELECT 1 FROM "network"."Connection" c
                  WHERE c."profileId" = ${selfProfileId}::uuid
                  AND c."connectedId" = u."id"
                ),
                'isFollowing', EXISTS (
                  SELECT 1 FROM "network"."Follow" f2
                  WHERE f2."followerProfileId" = ${selfProfileId}::uuid
                  AND f2."followeeProfileId" = u."id"
                )`
                  : Prisma.empty
              }
            ) AS "Profile"
          FROM "network"."Follow" f
          INNER JOIN "user"."Profile" u
            ON f."followeeProfileId" = u."id"
            AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          WHERE f."followerProfileId" = ${profileId}::uuid
            AND NOT EXISTS (
              SELECT 1 FROM "network"."BlockedProfile" b
              WHERE (
                (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = f."followeeProfileId")
                OR
                (b."blockerId" = f."followeeProfileId" AND b."blockedId" = ${selfProfileId}::uuid)
              )
            )
            ${typeof cursorId === 'number' ? Prisma.sql`AND f."cursorId" < ${cursorId}` : Prisma.empty}
          ORDER BY f."createdAt" DESC
          LIMIT ${pageSize}
        `,
        prismaPG.follow.count({
          where: {
            followerProfileId: profileId,
            FollowerProfile: {
              status: 'ACTIVE',
            },
            NOT: {
              OR: [
                { FolloweeProfile: { BlockedByProfile: { some: { blockerId: selfProfileId } } } },
                { FolloweeProfile: { BlockedProfile: { some: { blockedId: selfProfileId } } } },
              ],
            },
          },
        }),
      ]);

      if (followingsResultTemp?.length) {
        followings.push(
          ...followingsResultTemp.map(
            (item) =>
              ({
                cursorId: Number(item.cursorId),
                Profile: {
                  id: item.Profile.id,
                  name: item.Profile.name,
                  avatar: item.Profile.avatar,
                  designation: item.Profile?.designationAlternativeId
                    ? {
                        id: item.Profile.designationAlternativeId,
                        name: item.Profile.designationText,
                        dataType: 'master',
                      }
                    : item.Profile?.designationRawDataId
                      ? {
                          id: item.Profile.designationRawDataId,
                          name: item.Profile.designationText,
                          dataType: 'raw',
                        }
                      : null,
                  entity: item.Profile?.entityId
                    ? {
                        id: item.Profile.entityId,
                        name: item.Profile.entityText,
                        dataType: 'master',
                      }
                    : item.Profile?.entityRawDataId
                      ? {
                          id: item.Profile.entityRawDataId,
                          name: item.Profile.entityText,
                          dataType: 'raw',
                        }
                      : null,
                  isConnected: item.isConnected,
                  isFollowing: item.isFollowing,
                } as ProfileNetworkExternalI,
              }) as FollowExternalI,
          ),
        );
      }

      return { data: followings, total: followingsCountResult };
    } catch (error) {
      throw new AppError('FLW002', error);
    }
  },
};

export default FollowModule;
