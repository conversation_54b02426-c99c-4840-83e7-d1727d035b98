import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { TotalI } from '@interfaces/common/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type {
  ConnectionDataI,
  ConnectionExternalI,
  ConnectionFetchManyResultI,
  ConnectionRawExternalI,
  ConnectionSearchResultI,
} from '@interfaces/network/connection';
import type { ProfileNetworkExternalI, ProfileSearchExternalI, ProfileSearchI } from '@interfaces/user/profile';
import { Prisma } from '@prisma/postgres';
import type { ProfileIdRouteParamsI } from '@schemas/common/common';
import type { ConnectionFetchManyI, ConnectionSearchI } from '@schemas/network/connection';

export const ConnectionModule = {
  fetchManySearchGlobal: async (
    state: FastifyStateI,
    { search, page, pageSize }: ConnectionSearchI,
  ): Promise<ConnectionSearchResultI> => {
    search = search?.trim();
    const result: ConnectionSearchResultI = {
      data: [],
      total: 0,
    };
    if (!search?.length || (search?.length === 1 && search?.[0] === '@')) {
      return result;
    }
    const selfProfileId = state.profileId;
    const isUsernameSearch = search.startsWith('@');

    const [connectionSearchedCountResult, connectionSearchResultTemp] = await Promise.all([
      prismaPG.$queryRaw<TotalI[]>`
      SELECT COUNT(*)::int AS "total"
      FROM "user"."Profile" u
      WHERE
      NOT EXISTS
      (
        SELECT 1
        FROM "network"."BlockedProfile" b
        WHERE
          (
            b."blockerId" = ${selfProfileId}::uuid
            AND
            b."blockedId" = u."id"
          )
          OR
          (
            b."blockerId" = u."id"
            AND
            b."blockedId" = ${selfProfileId}::uuid
          )
      )
      AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
      AND
      ${
        isUsernameSearch
          ? Prisma.sql`u."username" ILIKE ${search?.slice(1) + '%'}`
          : Prisma.sql`u."name" ILIKE ${search?.toLowerCase() + '%'}`
      }
      `,
      prismaPG.$queryRaw<ProfileSearchI[]>`
        SELECT
        u."id" AS "profileId",
        u."avatar" AS "avatar",
        u."username" AS "username",
        u."name" AS "name",
        u."designationText" AS "designationText",
        u."entityText" AS "entityText",
        u."designationAlternativeId" AS "designationAlternativeId",
        u."designationRawDataId" AS "designationRawDataId",
        u."entityId" AS "entityId",
        u."entityRawDataId" AS "entityRawDataId",
        CASE
          WHEN EXISTS
          (
            SELECT 1
            FROM "network"."Connection" c1
            WHERE
            c1."profileId" = ${selfProfileId}::uuid
            AND
            c1."connectedId" = u."id"
          ) THEN 1
          WHEN EXISTS
          (
            SELECT 1
            FROM "network"."Connection" c1
            INNER JOIN "network"."Connection" c2
            ON c1."connectedId" = c2."connectedId"
            AND c1."profileId" = ${selfProfileId}::uuid
            AND c2."profileId" = u."id"
          ) THEN 2
          ELSE 3
        END AS priority
        FROM "user"."Profile" u
        WHERE
        NOT EXISTS (
          SELECT 1
          FROM "network"."BlockedProfile" b
          WHERE
          (
            b."blockerId" = ${selfProfileId}::uuid
            AND
            b."blockedId" = u."id"
          )
          OR
          (
            b."blockerId" = u."id"
            AND
            b."blockedId" = ${selfProfileId}::uuid
          )
        )
        AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        AND
        ${
          isUsernameSearch
            ? Prisma.sql`u."username" ILIKE ${search?.slice(1) + '%'}`
            : Prisma.sql`u."name" ILIKE ${search?.toLowerCase() + '%'}`
        }
        ORDER BY
        priority,
        ${isUsernameSearch ? Prisma.sql`u."username"` : Prisma.sql`u."name"`}
        ASC
        OFFSET ${page * pageSize}
        LIMIT ${pageSize};
      `,
    ]);

    if (connectionSearchedCountResult?.length) {
      result.total = connectionSearchedCountResult[0].total;
    }
    if (connectionSearchResultTemp?.length) {
      result.data = connectionSearchResultTemp.map(
        (item) =>
          ({
            profileId: item.profileId,
            username: item.username,
            name: item.name,
            avatar: item.avatar,
            priority: item.priority,
            designation: item?.designationAlternativeId
              ? {
                  id: item.designationAlternativeId,
                  name: item.designationText,
                  dataType: 'master',
                }
              : item?.designationRawDataId
                ? {
                    id: item.designationAlternativeId,
                    name: item.designationText,
                    dataType: 'raw',
                  }
                : null,
            entity: item?.entityId
              ? {
                  id: item.entityId,
                  name: item.entityText,
                  dataType: 'master',
                }
              : item?.entityRawDataId
                ? {
                    id: item.entityRawDataId,
                    name: item.entityText,
                    dataType: 'raw',
                  }
                : null,
          }) as ProfileSearchExternalI,
      );
    }
    return result;
  },
  mutualFetchMany: async (
    state: FastifyStateI,
    { cursorId, pageSize, profileId }: ConnectionFetchManyI,
  ): Promise<ConnectionFetchManyResultI> => {
    const connectionResult: ConnectionExternalI[] = [];
    const selfProfileId = state.profileId;

    const [connectionsTotalResult, connectionResultTemp] = await Promise.all([
      prismaPG.$queryRaw<TotalI[]>`
        SELECT COUNT(*)::int AS total
        FROM "network"."Connection" c1
        JOIN "network"."Connection" c2
          ON c1."connectedId" = c2."connectedId"
        INNER JOIN
        "user"."Profile" u
          ON u."id" = c1."connectedId"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        WHERE c1."profileId" = ${selfProfileId}::uuid
          AND c2."profileId" = ${profileId}::uuid
      `,
      prismaPG.$queryRaw<ConnectionRawExternalI[]>`
        SELECT
          c1."connectedId",
          c1."cursorId"::int,
          u."id" as "profileId",
          u."name",
          u."avatar",
          u."designationText",
          u."designationAlternativeId",
          u."designationRawDataId",
          u."entityText",
          u."entityId",
          u."entityRawDataId"
        FROM "network"."Connection" c1
        JOIN "network"."Connection" c2
          ON c1."connectedId" = c2."connectedId"
        INNER JOIN "user"."Profile" u
          ON u."id" = c1."connectedId"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        WHERE c1."profileId" = ${selfProfileId}::uuid
          AND c2."profileId" = ${profileId}::uuid
          ${cursorId !== null && cursorId !== undefined ? Prisma.sql`AND c1."cursorId" < ${cursorId}` : Prisma.empty}
        ORDER BY c1."cursorId" DESC
        LIMIT ${pageSize}
      `,
    ]);

    if (connectionResultTemp?.length) {
      for (const item of connectionResultTemp) {
        const transformed: ConnectionExternalI = {
          cursorId: Number(item.cursorId),
          Profile: {
            cursorId: Number(item.cursorId),
            id: item.profileId,
            name: item.name,
            avatar: item.avatar,
            designation: item?.designationAlternativeId
              ? {
                  id: item.designationAlternativeId,
                  name: item.designationText,
                  dataType: 'master',
                }
              : item?.designationRawDataId
                ? {
                    id: item.designationRawDataId,
                    name: item.designationText,
                    dataType: 'raw',
                  }
                : null,
            entity: item?.entityId
              ? {
                  id: item.entityId,
                  name: item.entityText,
                  dataType: 'master',
                }
              : item?.entityRawDataId
                ? {
                    id: item.entityRawDataId,
                    name: item.entityText,
                    dataType: 'raw',
                  }
                : null,
          },
        };

        connectionResult.push(transformed);
      }
    }

    return {
      data: connectionResult,
      total: connectionsTotalResult?.[0]?.total ?? 0,
    };
  },
  fetchMany: async (
    state: FastifyStateI,
    { cursorId, pageSize, profileId, name }: ConnectionFetchManyI,
  ): Promise<ConnectionFetchManyResultI> => {
    const connectionResult: ConnectionExternalI[] = [];
    const selfProfileId = state.profileId;
    const nameFilter = name?.trim().toLowerCase();
    const [connectionCountResult, connectionResultTemp] = await Promise.all([
      prismaPG.connection.count({
        where: {
          profileId,
          Profile: { status: 'ACTIVE' },
          ...(nameFilter && {
            ConnectedId: {
              name: {
                contains: nameFilter,
                mode: 'insensitive',
              },
            },
          }),
        },
      }),
      prismaPG.$queryRaw<ConnectionDataI[]>`
        SELECT
        c."cursorId" AS "cursorId",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
          ${
            selfProfileId !== profileId
              ? Prisma.sql`
            , 'isConnected', EXISTS (
              SELECT 1 FROM "network"."Connection" c
              WHERE c."profileId" = ${selfProfileId}::uuid
              AND c."connectedId" = u."id"
            )
          `
              : Prisma.empty
          }
        ) AS "Profile"
        FROM "network"."Connection" c
        INNER JOIN "user"."Profile" u
          ON u."id" = c."connectedId"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        WHERE c."profileId" = ${profileId}::uuid
        AND NOT EXISTS (
          SELECT 1
          FROM "network"."BlockedProfile" b
          WHERE (
            b."blockerId" = ${selfProfileId}::uuid
            AND
            b."blockedId" = u."id"
            OR
            b."blockerId" = u."id"
            AND
            b."blockedId" = ${selfProfileId}::uuid
          )
        )
        ${nameFilter ? Prisma.sql`AND u."name" ILIKE ${nameFilter + '%'}` : Prisma.empty}
        ${typeof cursorId === 'number' ? Prisma.sql`AND c."cursorId" < ${cursorId}` : Prisma.empty}
        ORDER BY c."createdAt" DESC
        LIMIT ${pageSize}
      `,
    ]);
    if (connectionResultTemp?.length) {
      connectionResult.push(
        ...connectionResultTemp.map(
          (item) =>
            ({
              cursorId: Number(item.cursorId),
              Profile: {
                id: item.Profile.id,
                name: item.Profile.name,
                avatar: item.Profile.avatar,
                designation: item.Profile?.designationAlternativeId
                  ? {
                      id: item.Profile.designationAlternativeId,
                      name: item.Profile.designationText,
                      dataType: 'master',
                    }
                  : item.Profile?.designationRawDataId
                    ? {
                        id: item.Profile.designationAlternativeId,
                        name: item.Profile.designationText,
                        dataType: 'raw',
                      }
                    : null,
                entity: item.Profile?.entityId
                  ? {
                      id: item.Profile.entityId,
                      name: item.Profile.entityText,
                      dataType: 'master',
                    }
                  : item.Profile?.entityRawDataId
                    ? {
                        id: item.Profile.entityRawDataId,
                        name: item.Profile.entityText,
                        dataType: 'raw',
                      }
                    : null,
                isConnected: item.isConnected,
              } as ProfileNetworkExternalI,
            }) as ConnectionExternalI,
        ),
      );
    }
    return { data: connectionResult, total: connectionCountResult };
  },
  removeOne: async (state: FastifyStateI, { profileId }: ProfileIdRouteParamsI): Promise<void> => {
    const selfProfileId = state.profileId;
    const blockedProfileResult = await prismaPG.blockedProfile.findFirst({
      select: { blockedId: true },
      where: {
        OR: [
          {
            blockedId: selfProfileId,
            blockerId: profileId,
          },
          {
            blockedId: profileId,
            blockerId: selfProfileId,
          },
        ],
      },
    });
    if (blockedProfileResult) {
      throw new AppError('CN005');
    }
    const [selfProfileConnectionResult, profileConnectionResult] = await Promise.all([
      prismaPG.connection.findUnique({
        select: { cursorId: true },
        where: {
          profileId_connectedId: {
            profileId: selfProfileId,
            connectedId: profileId,
          },
        },
      }),
      prismaPG.connection.findUnique({
        select: { cursorId: true },
        where: {
          profileId_connectedId: {
            profileId: profileId,
            connectedId: selfProfileId,
          },
        },
      }),
    ]);
    if (!selfProfileConnectionResult) {
      throw new AppError('CN001');
    }
    const [_selfProfileResult, profileResult, sentResult, receivedResult] = await Promise.all([
      prismaPG.profile.findFirst({
        select: {
          connectionsCount: true,
        },
        where: {
          id: selfProfileId,
        },
      }),
      prismaPG.profile.findFirst({
        select: {
          connectionsCount: true,
        },
        where: {
          id: profileId,
        },
      }),
      prismaPG.request.findUnique({
        select: { status: true },
        where: {
          senderProfileId_receiverProfileId: {
            senderProfileId: selfProfileId,
            receiverProfileId: profileId,
          },
        },
      }),
      prismaPG.request.findUnique({
        select: { status: true },
        where: {
          senderProfileId_receiverProfileId: {
            senderProfileId: profileId,
            receiverProfileId: selfProfileId,
          },
        },
      }),
    ]);
    await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.connection.delete({
            select: { cursorId: true },
            where: {
              profileId_connectedId: {
                profileId: selfProfileId,
                connectedId: profileId,
              },
            },
          }),
          profileConnectionResult
            ? txn.connection.delete({
                select: { cursorId: true },
                where: {
                  profileId_connectedId: {
                    profileId: profileId,
                    connectedId: selfProfileId,
                  },
                },
              })
            : null,
          _selfProfileResult?.connectionsCount > 0
            ? prismaPG.profile.update({
                data: {
                  connectionsCount: {
                    decrement: 1,
                  },
                },
                select: {
                  id: true,
                },
                where: {
                  id: selfProfileId,
                },
              })
            : null,
          profileResult?.connectionsCount > 0
            ? prismaPG.profile.update({
                data: {
                  connectionsCount: {
                    decrement: 1,
                  },
                },
                select: {
                  id: true,
                },
                where: {
                  id: profileId,
                },
              })
            : null,

          sentResult?.status === 'ACCEPTED'
            ? prismaPG.request.update({
                data: {
                  status: 'DISCONNECTED',
                },
                select: {
                  id: true,
                },
                where: {
                  senderProfileId_receiverProfileId: {
                    senderProfileId: selfProfileId,
                    receiverProfileId: profileId,
                  },
                },
              })
            : null,
          receivedResult?.status === 'ACCEPTED'
            ? prismaPG.request.update({
                data: {
                  status: 'DISCONNECTED',
                },
                select: {
                  id: true,
                },
                where: {
                  senderProfileId_receiverProfileId: {
                    senderProfileId: profileId,
                    receiverProfileId: selfProfileId,
                  },
                },
              })
            : null,
        ]),
    );
  },
};

export default ConnectionModule;
