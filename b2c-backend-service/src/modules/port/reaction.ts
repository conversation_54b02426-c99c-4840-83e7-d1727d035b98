import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { PAGINATION } from '@consts/common/pagination';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type {
  ProfileItemI,
  ScrapBookReactionFetchForClientI,
  ScrapBookReactionRawResultI,
} from '@interfaces/port/reaction';
import { Prisma } from '@prisma/postgres';
import type { PaginationI } from '@schemas/common/common';
import type {
  ScrapBookReactionpUsertOneParamsI,
  ScrapBookReactionPostIdParamsI,
  ScrapBookReactionFetchForClientParamsI,
} from '@schemas/port/reaction';

export const ScrapBookReactionModule = {
  fetchForClient: async (
    params: ScrapBookReactionFetchForClientParamsI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ reactions: ScrapBookReactionFetchForClientI[]; totalCount: number }> => {
    if (!params.scrapBookPostId) {
      throw new AppError('SCBKRCN006');
    }

    const totalCountQuery = await prismaPG.$queryRaw<{ count: bigint }[]>`
      SELECT COUNT(*)::bigint as count
      FROM "port"."ScrapBookReaction"
      WHERE "scrapBookPostId" = ${params.scrapBookPostId}::uuid
    `;
    const total = Number(totalCountQuery[0].count);
    const skip = pagination.page > 0 ? (pagination.page - 1) * pagination.pageSize : 0;

    const scrapBookReactionResult = await prismaPG.$queryRaw<ScrapBookReactionRawResultI[]>`
      SELECT
        r."reactionType",
        json_build_object(
          'id', p."id",
          'name', p."name",
          'avatar', p."avatar",
          'designationText', p."designationText",
          'designationAlternativeId', p."designationAlternativeId",
          'designationRawDataId', p."designationRawDataId",
          'entityText', p."entityText",
          'entityId', p."entityId",
          'entityRawDataId', p."entityRawDataId"
        ) as "Profile"
      FROM "port"."ScrapBookReaction" r
      JOIN "user"."Profile" p ON r."profileId" = p."id"
      WHERE r."scrapBookPostId" = ${params.scrapBookPostId}::uuid
      ORDER BY r."createdAt" DESC
      LIMIT ${pagination.pageSize} OFFSET ${skip}
    `;

    const transformProfile = (profile: ProfileItemI) => ({
      id: profile.id,
      name: profile.name,
      avatar: profile.avatar,
      designation: profile.designationAlternativeId
        ? {
            id: profile.designationAlternativeId,
            name: profile.designationText || '',
            dataType: 'master' as const,
          }
        : profile.designationRawDataId
          ? {
              id: profile.designationRawDataId,
              name: profile.designationText || '',
              dataType: 'raw' as const,
            }
          : null,
      entity: profile.entityId
        ? {
            id: profile.entityId,
            name: profile.entityText || '',
            dataType: 'master' as const,
          }
        : profile.entityRawDataId
          ? {
              id: profile.entityRawDataId,
              name: profile.entityText || '',
              dataType: 'raw' as const,
            }
          : null,
    });

    const scrapBookReactionFetchForClientResult: ScrapBookReactionFetchForClientI[] = scrapBookReactionResult.map(
      (reaction) => ({
        reactionType: reaction.reactionType,
        Profile: transformProfile(reaction.Profile),
      }),
    );

    return { reactions: scrapBookReactionFetchForClientResult, totalCount: total };
  },
  upsertOne: async (state: FastifyStateI, params: ScrapBookReactionpUsertOneParamsI): Promise<void> => {
    if (!state.profileId) {
      throw new AppError('SCBKRCN005');
    }

    if (!params.scrapBookPostId) {
      throw new AppError('SCBKRCN006');
    }

    if (!params.reactionType) {
      throw new AppError('SCBKRCN007');
    }
    try {
      const postExists = await prismaPG.$queryRaw<{ id: string }[]>`
      SELECT id FROM "port"."ScrapBookPost" WHERE id = ${params.scrapBookPostId}::uuid
    `;

      if (postExists.length === 0) {
        throw new AppError('SCBKRCN011');
      }

      await prismaPG.$transaction(async (tx) => {
        const existingReaction = await tx.scrapBookReaction.findFirst({
          where: {
            profileId: state.profileId,
            scrapBookPostId: params.scrapBookPostId,
          },
          select: {
            reactionType: true,
          },
        });

        await tx.scrapBookReaction.upsert({
          where: {
            profileId_scrapBookPostId: {
              profileId: state.profileId,
              scrapBookPostId: params.scrapBookPostId,
            },
          },
          create: {
            profileId: state.profileId,
            scrapBookPostId: params.scrapBookPostId,
            reactionType: params.reactionType,
          },
          update: {
            reactionType: params.reactionType,
            updatedAt: new Date(),
          },
        });

        if (!existingReaction) {
          await tx.scrapBookPost.update({
            where: {
              id: params.scrapBookPostId,
            },
            data: {
              reactionCount: {
                increment: 1,
              },
            },
          });
        }
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new AppError('SCBKRCN008');
        }
        if (error.code === 'P2025') {
          throw new AppError('SCBKRCN001');
        }
      }
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('SCBKRCN010');
    }
  },
  deleteOne: async (state: FastifyStateI, filtersP: ScrapBookReactionPostIdParamsI): Promise<void> => {
    if (!state.profileId) {
      throw new AppError('SCBKRCN005');
    }

    if (!filtersP.scrapBookPostId) {
      throw new AppError('SCBKRCN006');
    }

    try {
      const postExists = await prismaPG.$queryRaw<{ id: string }[]>`
        SELECT id FROM "port"."ScrapBookPost" WHERE id = ${filtersP.scrapBookPostId}::uuid
      `;

      if (postExists.length === 0) {
        throw new AppError('SCBKRCN011');
      }

      const existingReaction = await prismaPG.scrapBookReaction.findFirst({
        where: {
          profileId: state.profileId,
          scrapBookPostId: filtersP.scrapBookPostId,
        },
        select: {
          reactionType: true,
        },
      });

      if (!existingReaction) {
        throw new AppError('SCBKRCN001');
      }

      await prismaPG.$transaction(async (tx) => {
        await tx.scrapBookReaction.delete({
          where: {
            profileId_scrapBookPostId: {
              profileId: state.profileId,
              scrapBookPostId: filtersP.scrapBookPostId,
            },
          },
        });

        await tx.scrapBookPost.update({
          where: {
            id: filtersP.scrapBookPostId,
          },
          data: {
            reactionCount: {
              decrement: 1,
            },
          },
        });
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new AppError('SCBKRCN001');
        }
        if (error.code === 'P2003') {
          throw new AppError('SCBKRCN008');
        }
      }
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('SCBKRCN009');
    }
  },
};
