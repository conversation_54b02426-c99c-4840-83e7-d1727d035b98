import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import {
  ProfileEducationCreateForClientI,
  ProfileEducationCreateOneDataI,
  ProfileEducationForExternalClientI,
  ProfileEducationForInternalClientI,
} from '@interfaces/career/education';
import Company from '@modules/company';
import { Prisma } from '@prisma/postgres';
import type { ProfileEducation } from '@prisma/postgres';
import { ProfileEducationCreateOneParamsI, ProfileEducationPatchBodyI } from '@schemas/career/education';
import type { IdTypeI, IdTypeMapI, ProfileIdPaginationI } from '@schemas/common/common';
import { separateMasterAndRawData, uniqueArrayObj } from '@utils/data/array';
import { SkillModule } from './skill';
import { isFilled } from '@utils/data/object';
import { SkillNestedClientI } from '@interfaces/company/skill';
import { EntityNestedClientI } from '@interfaces/company/entity';
import { DegreeNestedClientI } from '@interfaces/company/degree';

export const EducationModule = {
  createOne: async (
    state: FastifyStateI,
    params: ProfileEducationCreateOneParamsI,
  ): Promise<ProfileEducationCreateForClientI> => {
    const [entityClientResult, degreeClientResult] = await Promise.all([
      Company.EntityModule.fetchById(params.institute),
      Company.DegreeModule.fetchById(params.degree),
    ]);
    const masterSkills: string[] = [];
    const rawDataSkills: string[] = [];
    if (params?.skills?.length) {
      const { master, rawData } = separateMasterAndRawData(params?.skills);
      masterSkills.push(...master);
      rawDataSkills.push(...rawData);

      const { countMasterSkills, countRawDataSkills } = await Company.SkillModule.count(masterSkills, rawDataSkills);
      if (!(masterSkills.length === countMasterSkills && rawDataSkills.length === countRawDataSkills)) {
        throw new AppError('PFEDU005');
      }
    }
    const toCreateProfileEducationData: ProfileEducationCreateOneDataI = {
      profileId: state.profileId,
      fromDate: params.fromDate,
    };
    if (params?.toDate) {
      toCreateProfileEducationData.toDate = params.toDate;
    }
    if (entityClientResult.dataType === 'master') {
      toCreateProfileEducationData.entityId = entityClientResult.id;
    } else if (entityClientResult.dataType === 'raw') {
      toCreateProfileEducationData.entityRawDataId = entityClientResult.id;
    }
    if (degreeClientResult.dataType === 'master') {
      toCreateProfileEducationData.degreeId = degreeClientResult.id;
    } else if (degreeClientResult.dataType === 'raw') {
      toCreateProfileEducationData.degreeRawDataId = degreeClientResult.id;
    }
    const [profileEducationResult, _profileMetaResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.profileEducation.create({
            data: toCreateProfileEducationData,
            select: { id: true },
          }),
          txn.profileMeta.update({
            data: {
              educationCount: {
                increment: 1,
              },
            },
            select: {
              educationCount: true,
            },
            where: { profileId: state.profileId },
          }),
        ]),
    );
    if (!profileEducationResult) {
      throw new AppError('PFEDU002');
    }
    if (masterSkills?.length || rawDataSkills?.length) {
      const _profileSkillResult = await SkillModule.createManyWithEntityDegree({
        profileId: state.profileId,
        masterSkills,
        rawDataSkills,
        entity: entityClientResult,
        degree: degreeClientResult,
      });
    }
    return profileEducationResult;
  },
  deleteOne: async (state: FastifyStateI, { id }: Pick<ProfileEducation, 'id'>): Promise<void> => {
    const [profileMetaResult, profileEducationResult] = await Promise.all([
      prismaPG.profileMeta.findUnique({
        select: {
          educationCount: true,
        },
        where: {
          profileId: state.profileId,
        },
      }),
      prismaPG.profileEducation.findUnique({
        select: { id: true, profileId: true },
        where: { id, profileId: state.profileId },
      }),
    ]);
    if (!profileEducationResult) {
      throw new AppError('PFEDU001');
    }
    const [_profileMetaResult, deletedProfileEducationResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          profileMetaResult?.educationCount > 0
            ? txn.profileMeta.update({
                data: {
                  educationCount: {
                    decrement: 1,
                  },
                },
                where: {
                  profileId: state.profileId,
                },
              })
            : null,
          txn.profileEducation.delete({
            select: { id: true },
            where: { id, profileId: state.profileId },
          }),
        ]),
    );

    if (!deletedProfileEducationResult) {
      throw new AppError('PFEDU001');
    }
    return;
  },
  updateOne: async (
    state: FastifyStateI,
    params: ProfileEducationPatchBodyI,
    filter: Pick<Prisma.ProfileEducationWhereInput, 'id'>,
  ): Promise<void> => {
    const profileEducationResult = await prismaPG.profileEducation.findUnique({
      where: {
        id: String(filter.id),
        profileId: state.profileId,
      },
      select: {
        id: true,
        profileId: true,
        entityId: true,
        entityRawDataId: true,
        degreeId: true,
        degreeRawDataId: true,
      },
    });
    const [entityClientResult, degreeClientResult] = await Promise.all([
      params.institute?.id?.length
        ? Company.EntityModule.fetchById(params.institute)
        : params?.skillsToAdd?.length
          ? Company.EntityModule.fetchById(
              profileEducationResult?.entityId?.length
                ? { id: profileEducationResult.entityId, dataType: 'master' }
                : { id: profileEducationResult.entityRawDataId, dataType: 'raw' },
            )
          : null,

      params.degree?.id?.length
        ? Company.DegreeModule.fetchById(params.degree)
        : params?.skillsToAdd?.length
          ? Company.DegreeModule.fetchById(
              profileEducationResult?.degreeId?.length
                ? { id: profileEducationResult.degreeId, dataType: 'master' }
                : { id: profileEducationResult.degreeRawDataId, dataType: 'raw' },
            )
          : null,
    ]);
    if (params.skillsToDelete?.length) {
      params.skillsToDelete = uniqueArrayObj(params.skillsToDelete);
      const existingSkillsToDeleteResult: IdTypeI[] = await SkillModule.fetchSpecificForEntityDegree({
        entity: {
          id: entityClientResult.id,
          name: entityClientResult.name,
          dataType: entityClientResult.dataType,
        },
        degree: {
          id: degreeClientResult.id,
          name: degreeClientResult.name,
          dataType: degreeClientResult.dataType,
        },
        profileId: state.profileId,
        idTypes: params.skillsToDelete,
      });
      if (existingSkillsToDeleteResult?.length) {
        const _deletedCountResult = await SkillModule.deleteManyForEntityDegree({
          idTypes: existingSkillsToDeleteResult,
          profileId: state.profileId,
          degree: degreeClientResult,
          entity: entityClientResult,
        });
      }
    }
    if (params.skillsToAdd?.length) {
      params.skillsToAdd = uniqueArrayObj(params.skillsToAdd);
      const existingSkillsResult: IdTypeI[] = await SkillModule.fetchSpecificForEntityDegree({
        entity: {
          id: entityClientResult.id,
          name: entityClientResult.name,
          dataType: entityClientResult.dataType,
        },
        degree: {
          id: degreeClientResult.id,
          name: degreeClientResult.name,
          dataType: degreeClientResult.dataType,
        },
        profileId: state.profileId,
        idTypes: params.skillsToAdd,
      });
      const existingSkillsMapResult: IdTypeMapI = existingSkillsResult.reduce((acc, curr) => {
        acc[curr.id] = curr;
        return acc;
      }, {} as IdTypeMapI);

      const masterSkills: string[] = [];
      const rawDataSkills: string[] = [];
      params.skillsToAdd.forEach((skillItem) => {
        if (!existingSkillsMapResult?.[skillItem.id]?.id) {
          if (skillItem.dataType === 'master') {
            masterSkills.push(skillItem.id);
          } else {
            rawDataSkills.push(skillItem.id);
          }
        }
      });
      const _profileSkillResult = await SkillModule.createManyWithEntityDegree({
        masterSkills,
        rawDataSkills,
        degree: degreeClientResult,
        entity: entityClientResult,
        profileId: state.profileId,
      });
    }
    const updateInput: Prisma.ProfileEducationUncheckedUpdateInput = {};
    if (isFilled(params?.institute)) {
      if (params.institute.dataType === 'master') {
        updateInput.entityId = params.institute.id;
      } else {
        updateInput.entityRawDataId = params.institute.id;
      }
    }
    if (isFilled(params?.degree)) {
      if (params.degree.dataType === 'master') {
        updateInput.degreeId = params.degree.id;
      } else {
        updateInput.degreeRawDataId = params.degree.id;
      }
    }
    if (params?.fromDate) {
      updateInput.fromDate = params.fromDate;
    }
    if (params?.toDate !== undefined) {
      updateInput.toDate = params.toDate;
    }
    if (isFilled(updateInput)) {
      await prismaPG.profileEducation.update({
        data: updateInput,
        where: {
          id: String(filter.id),
          profileId: state.profileId,
        },
      });
    }
    return;
  },
  fetchOneForInternalClient: async (
    state: FastifyStateI,
    filters: Pick<Prisma.ProfileEducationWhereInput, 'id'>,
  ): Promise<ProfileEducationForInternalClientI> => {
    const profileEducationResult = await prismaPG.profileEducation.findFirst({
      where: { ...filters, profileId: state.profileId },
      select: {
        id: true,
        profileId: true,
        entityId: true,
        entityRawDataId: true,
        degreeId: true,
        degreeRawDataId: true,
        fromDate: true,
        toDate: true,
        Entity: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        EntityRawData: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        Degree: {
          select: {
            id: true,
            name: true,
          },
        },
        DegreeRawData: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!profileEducationResult) {
      console.error('Profile education not found');
      throw new AppError('VISA001');
    }

    const entity: EntityNestedClientI = isFilled(profileEducationResult?.Entity)
      ? {
          id: profileEducationResult?.Entity.id,
          name: profileEducationResult?.Entity.name,
          type: profileEducationResult?.Entity.type,
          dataType: 'master',
        }
      : {
          id: profileEducationResult?.EntityRawData.id,
          name: profileEducationResult?.EntityRawData.name,
          type: profileEducationResult?.EntityRawData.type,
          dataType: 'raw',
        };

    const degree: DegreeNestedClientI = isFilled(profileEducationResult?.Degree)
      ? {
          id: profileEducationResult.Degree.id,
          name: profileEducationResult.Degree.name,
          dataType: 'master',
        }
      : {
          id: profileEducationResult.DegreeRawData.id,
          name: profileEducationResult.DegreeRawData.name,
          dataType: 'raw',
        };

    const skills: SkillNestedClientI[] = await SkillModule.fetchForEntityDegree({
      profileId: state.profileId,
      entity,
      degree,
    });

    const profileEducationForClientResult: ProfileEducationForInternalClientI = {
      id: profileEducationResult.id,
      fromDate: profileEducationResult.fromDate,
      toDate: profileEducationResult.toDate,
      profileId: profileEducationResult.profileId,
      entity,
      degree,
      skills,
    };

    return profileEducationForClientResult;
  },
  fetchForExternalClient: async (
    params: ProfileIdPaginationI,
    select: Prisma.ProfileEducationSelect = {
      id: true,
      Entity: {
        select: {
          id: true,
          name: true,
        },
      },
      EntityRawData: {
        select: {
          id: true,
          name: true,
        },
      },
      Degree: {
        select: {
          id: true,
          name: true,
        },
      },
      DegreeRawData: {
        select: {
          id: true,
          name: true,
        },
      },
      fromDate: true,
      toDate: true,
      createdAt: true,
    },
  ): Promise<ProfileEducationForExternalClientI[]> => {
    const profileEducationResult = await prismaPG.profileEducation.findMany({
      where: {
        profileId: params.profileId,
      },
      orderBy: [
        {
          toDate: {
            sort: 'desc',
            nulls: 'first',
          },
        },
        {
          fromDate: 'desc',
        },
        {
          createdAt: 'desc',
        },
      ],
      skip: params.page,
      take: params.pageSize,
      select,
    });

    const profileEducationForClientResult: ProfileEducationForExternalClientI[] = profileEducationResult.map(
      (profileEducation) =>
        ({
          id: profileEducation.id,
          fromDate: profileEducation.fromDate,
          toDate: profileEducation.toDate,
          createdAt: profileEducation.createdAt,
          entity: isFilled(profileEducation?.Entity)
            ? {
                id: profileEducation.Entity.id,
                name: profileEducation.Entity.name,
                dataType: 'master',
              }
            : {
                id: profileEducation.EntityRawData.id,
                name: profileEducation.EntityRawData.name,
                dataType: 'raw',
              },
          degree: isFilled(profileEducation?.Degree)
            ? {
                id: profileEducation.Degree.id,
                name: profileEducation.Degree.name,
                dataType: 'master',
              }
            : {
                id: profileEducation.DegreeRawData.id,
                name: profileEducation.DegreeRawData.name,
                dataType: 'raw',
              },
        }) as ProfileEducationForExternalClientI,
    );

    return profileEducationForClientResult;
  },
};
