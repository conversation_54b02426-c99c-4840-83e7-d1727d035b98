import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import ServiceModule from '@modules/storage';
import type {
  ProfileCertificateCreateForClientI,
  ProfileCertificateCreateOneDataI,
  ProfileCertificateCreateOneParamsI,
  ProfileCertificateForExternalClientI,
  ProfileCertificateForInternalClientI,
  ProfileCertificateUpdateOneParamsI,
} from '@interfaces/career/certificate';
import Company from '@modules/company';
import type { Prisma, ProfileCertificate } from '@prisma/postgres';
import type { ProfileCertificateFetchForClientI } from '@schemas/career/certificate';
import { separateMasterAndRawData, uniqueArrayObj } from '@utils/data/array';
import { SkillModule } from './skill';
import { isFilled } from '@utils/data/object';
import type { SkillNestedClientI } from '@interfaces/company/skill';
import type { CertificateCourseNestedClientI } from '@interfaces/company/certificateCourse';
import type { EntityNestedClientI } from '@interfaces/company/entity';
import type { IdTypeI, IdTypeMapI } from '@schemas/common/common';

export const CertificateModule = {
  createOne: async (
    state: FastifyStateI,
    params: ProfileCertificateCreateOneParamsI,
  ): Promise<ProfileCertificateCreateForClientI> => {
    const [entityClientResult, certificateCourseClientResult] = await Promise.all([
      Company.EntityModule.fetchById(params.institute),
      Company.CertificateCourseModule.fetchById(params.certificateCourse),
    ]);
    const masterSkills: string[] = [];
    const rawDataSkills: string[] = [];

    if (params?.skills?.length) {
      const { master, rawData } = separateMasterAndRawData(params?.skills);
      masterSkills.push(...master);
      rawDataSkills.push(...rawData);
      const { countMasterSkills, countRawDataSkills } = await Company.SkillModule.count(masterSkills, rawDataSkills);
      if (!(masterSkills.length === countMasterSkills && rawDataSkills.length === countRawDataSkills)) {
        throw new AppError('PFCRT005');
      }
    } else {
      console.log('No skills provided');
    }

    const toCreateProfileCertificateData: ProfileCertificateCreateOneDataI = {
      profileId: state.profileId,
      fromDate: params.fromDate,
      untilDate: params.untilDate,
      fileUrl: params.fileUrl || null,
    };

    if (entityClientResult.dataType === 'master') {
      toCreateProfileCertificateData.entityId = entityClientResult.id;
    } else if (entityClientResult.dataType === 'raw') {
      toCreateProfileCertificateData.entityRawDataId = entityClientResult.id;
    }

    if (certificateCourseClientResult.dataType === 'master') {
      toCreateProfileCertificateData.certificateCourseId = certificateCourseClientResult.id;
    } else if (certificateCourseClientResult.dataType === 'raw') {
      toCreateProfileCertificateData.certificateCourseRawDataId = certificateCourseClientResult.id;
    }
    const updateProfileMetaParams: Prisma.ProfileMetaUncheckedUpdateInput = {};
    let resolvedCertificateType: string | undefined;

    if (certificateCourseClientResult.dataType === 'master') {
      resolvedCertificateType = certificateCourseClientResult.type;
    } else if (certificateCourseClientResult.dataType === 'raw') {
      const rawDataCourse = await prismaPG.certificateCourseRawData.findUnique({
        where: { id: certificateCourseClientResult.id },
        select: { type: true },
      });
      resolvedCertificateType = rawDataCourse?.type;
    }

    switch (resolvedCertificateType) {
      case 'STATUTORY': {
        updateProfileMetaParams.statutoryCertCount = { increment: 1 };
        break;
      }
      case 'VALUE_ADDED': {
        updateProfileMetaParams.valueAddedCertCount = { increment: 1 };
        break;
      }
      default:
        console.log('No specific certificate type handling needed');
        break;
    }

    const [profileCertificateResult, _profileMetaResult] = await prismaPG.$transaction(async (txn) => {
      const promises = [];
      promises.push(
        txn.profileCertificate.create({ data: toCreateProfileCertificateData, select: { id: true, fileUrl: true } }),
      );

      if (isFilled(updateProfileMetaParams)) {
        promises.push(
          txn.profileMeta.update({
            select: { profileId: true },
            data: updateProfileMetaParams,
            where: { profileId: state.profileId },
          }),
        );
      } else {
        promises.push(Promise.resolve(null));
      }
      return Promise.all(promises);
    });
    if (!profileCertificateResult) {
      throw new AppError('PFCRT002');
    }

    if (masterSkills?.length || rawDataSkills?.length) {
      await SkillModule.createManyWithEntityCertificate({
        profileId: state.profileId,
        masterSkills,
        rawDataSkills,
        entity: entityClientResult,
        certificate: certificateCourseClientResult,
      });
    } else {
      console.log('No skills to create');
    }
    return profileCertificateResult;
  },

  deleteOne: async (state: FastifyStateI, { id }: Pick<ProfileCertificate, 'id'>): Promise<void> => {
    const [profileMetaResult, profileCertificateResult] = await Promise.all([
      prismaPG.profileMeta.findUnique({
        select: {
          statutoryCertCount: true,
          valueAddedCertCount: true,
        },
        where: {
          profileId: state.profileId,
        },
      }),
      prismaPG.profileCertificate.findUnique({
        select: {
          id: true,
          fileUrl: true,
          CertificateCourse: { select: { type: true } },
          CertificateCourseRawData: { select: { type: true } },
        },
        where: { id },
      }),
    ]);

    if (!profileCertificateResult) {
      throw new AppError('PFCRT001');
    }

    if (profileCertificateResult.fileUrl) {
      try {
        await ServiceModule.CoreStorageModule.deleteFile({ fileUrl: profileCertificateResult.fileUrl });
      } catch (_error) {
        //
      }
    }

    const updateProfileMetaParams: Prisma.ProfileMetaUncheckedUpdateInput = {};
    const certType =
      profileCertificateResult?.CertificateCourse?.type || profileCertificateResult?.CertificateCourseRawData?.type;

    if (certType === 'STATUTORY' || certType === 'VALUE_ADDED') {
      const field = certType === 'STATUTORY' ? 'statutoryCertCount' : 'valueAddedCertCount';
      const currentCount = profileMetaResult?.[field] ?? 0;

      if (currentCount > 1) {
        updateProfileMetaParams[field] = { decrement: 1 };
      } else {
        updateProfileMetaParams[field] = 0;
      }
    }

    const [_profileMetaResult, deletedProfileCertificateResult] = await prismaPG.$transaction(async (txn) => {
      const promises = [];
      promises.push(txn.profileCertificate.delete({ select: { id: true }, where: { id } }));
      if (isFilled(updateProfileMetaParams)) {
        promises.push(txn.profileMeta.update({ data: updateProfileMetaParams, where: { profileId: state.profileId } }));
      } else {
        promises.push(Promise.resolve(null));
      }
      return Promise.all(promises);
    });

    if (!deletedProfileCertificateResult) {
      throw new AppError('PFCRT010');
    }

    return;
  },

  updateOne: async (
    state: FastifyStateI,
    params: ProfileCertificateUpdateOneParamsI,
    filter: Pick<Prisma.ProfileCertificateWhereInput, 'id'>,
  ): Promise<void> => {
    const profileCertificateResult = await prismaPG.profileCertificate.findUnique({
      where: {
        id: String(filter.id),
        profileId: state.profileId,
      },
      select: {
        id: true,
        profileId: true,
        entityId: true,
        entityRawDataId: true,
        certificateCourseId: true,
        certificateCourseRawDataId: true,
        fileUrl: true,
      },
    });

    if (!profileCertificateResult) {
      throw new AppError('PFCRT001');
    }

    const [entityClientResult, certificateCourseClientResult] = await Promise.all([
      params.institute?.id?.length
        ? Company.EntityModule.fetchById(params.institute)
        : params?.skillsToAdd?.length || params?.skillsToDelete?.length
          ? Company.EntityModule.fetchById(
              profileCertificateResult?.entityId?.length
                ? { id: profileCertificateResult.entityId, dataType: 'master' }
                : { id: profileCertificateResult.entityRawDataId, dataType: 'raw' },
            )
          : null,
      params.certificateCourse?.id?.length
        ? Company.CertificateCourseModule.fetchById(params.certificateCourse)
        : params?.skillsToAdd?.length || params?.skillsToDelete?.length
          ? Company.CertificateCourseModule.fetchById(
              profileCertificateResult?.certificateCourseId?.length
                ? { id: profileCertificateResult.certificateCourseId, dataType: 'master' }
                : { id: profileCertificateResult.certificateCourseRawDataId, dataType: 'raw' },
            )
          : null,
    ]);

    if (params.skillsToDelete?.length) {
      params.skillsToDelete = uniqueArrayObj(params.skillsToDelete);

      const existingSkillsToDeleteResult: IdTypeI[] = await SkillModule.fetchSpecificForEntityCertificate({
        entity: {
          id: entityClientResult.id,
          name: entityClientResult.name,
          dataType: entityClientResult.dataType,
        },
        certificateCourse: {
          id: certificateCourseClientResult.id,
          name: certificateCourseClientResult.name,
          dataType: certificateCourseClientResult.dataType,
          type: certificateCourseClientResult.type,
        },
        profileId: state.profileId,
        idTypes: params.skillsToDelete,
      });

      if (existingSkillsToDeleteResult?.length) {
        await SkillModule.deleteManyForEntityCertificate({
          idTypes: existingSkillsToDeleteResult,
          profileId: state.profileId,
          certificateCourse: certificateCourseClientResult,
          entity: entityClientResult,
        });
      }
    }

    if (params.skillsToAdd?.length) {
      params.skillsToAdd = uniqueArrayObj(params.skillsToAdd);

      const existingSkillsResult: IdTypeI[] = await SkillModule.fetchSpecificForEntityCertificate({
        entity: {
          id: entityClientResult.id,
          name: entityClientResult.name,
          dataType: entityClientResult.dataType,
        },
        certificateCourse: {
          id: certificateCourseClientResult.id,
          name: certificateCourseClientResult.name,
          type: certificateCourseClientResult.type,
          dataType: certificateCourseClientResult.dataType,
        },
        profileId: state.profileId,
        idTypes: params.skillsToAdd,
      });

      const existingSkillsMapResult: IdTypeMapI = existingSkillsResult.reduce((acc, curr) => {
        acc[curr.id] = curr;
        return acc;
      }, {} as IdTypeMapI);

      const masterSkills: string[] = [];
      const rawDataSkills: string[] = [];
      params.skillsToAdd.forEach((skillItem) => {
        if (!existingSkillsMapResult?.[skillItem.id]?.id) {
          if (skillItem.dataType === 'master') {
            masterSkills.push(skillItem.id);
          } else {
            rawDataSkills.push(skillItem.id);
          }
        }
      });

      if (masterSkills.length || rawDataSkills.length) {
        await SkillModule.createManyWithEntityCertificate({
          masterSkills,
          rawDataSkills,
          certificate: certificateCourseClientResult,
          entity: entityClientResult,
          profileId: state.profileId,
        });
      }
    }

    const toUpdateInput: Prisma.ProfileCertificateUncheckedUpdateInput = {};

    if (params?.untilDate !== undefined) {
      toUpdateInput.untilDate = params.untilDate;
    }

    if (isFilled(params?.institute)) {
      if (params.institute.dataType === 'master') {
        toUpdateInput.entityId = params.institute.id;
        toUpdateInput.entityRawDataId = null;
      } else {
        toUpdateInput.entityRawDataId = params.institute.id;
        toUpdateInput.entityId = null;
      }
    }

    if (isFilled(params?.certificateCourse)) {
      if (params.certificateCourse.dataType === 'master') {
        toUpdateInput.certificateCourseId = params.certificateCourse.id;
        toUpdateInput.certificateCourseRawDataId = null;
      } else {
        toUpdateInput.certificateCourseRawDataId = params.certificateCourse.id;
        toUpdateInput.certificateCourseId = null;
      }
    }

    if (params?.fromDate) {
      toUpdateInput.fromDate = params.fromDate;
    }

    if (params?.file?.opr) {
      if (params.file.opr === 'CREATE' || params.file.opr === 'UPDATE') {
        if (params.file.opr === 'UPDATE' && profileCertificateResult?.fileUrl) {
          try {
            await ServiceModule.CoreStorageModule.deleteFile({ fileUrl: profileCertificateResult.fileUrl });
          } catch (_error) {
            //
          }
        }
        if (params?.file?.fileUrl) {
          toUpdateInput.fileUrl = params.file.fileUrl;
        }
      } else if (params.file.opr === 'DELETE') {
        if (profileCertificateResult?.fileUrl) {
          try {
            await ServiceModule.CoreStorageModule.deleteFile({ fileUrl: profileCertificateResult.fileUrl });
          } catch (_error) {
            //
          }
        }
        toUpdateInput.fileUrl = null;
      }
    }

    if (isFilled(toUpdateInput)) {
      await prismaPG.profileCertificate.update({
        data: toUpdateInput,
        where: {
          id: String(filter.id),
          profileId: state.profileId,
        },
      });
    }

    return;
  },

  fetchOneForInternalClient: async (
    filters: Pick<Prisma.ProfileCertificateWhereInput, 'id'>,
  ): Promise<ProfileCertificateForInternalClientI> => {
    const profileCertificateResult = await prismaPG.profileCertificate.findFirst({
      where: filters,
      select: {
        id: true,
        profileId: true,
        fileUrl: true,
        fromDate: true,
        untilDate: true,
        Entity: { select: { id: true, name: true, type: true } },
        EntityRawData: { select: { id: true, name: true, type: true } },
        CertificateCourse: { select: { id: true, name: true, type: true } },
        CertificateCourseRawData: { select: { id: true, name: true, type: true } },
      },
    });

    if (!profileCertificateResult) {
      throw new AppError('PFCRT001');
    }

    const certificateCourse: CertificateCourseNestedClientI = isFilled(profileCertificateResult?.CertificateCourse)
      ? {
          id: profileCertificateResult?.CertificateCourse.id,
          name: profileCertificateResult?.CertificateCourse.name,
          type: profileCertificateResult?.CertificateCourse.type,
          dataType: 'master',
        }
      : {
          id: profileCertificateResult?.CertificateCourseRawData.id,
          name: profileCertificateResult?.CertificateCourseRawData.name,
          type: profileCertificateResult?.CertificateCourseRawData.type,
          dataType: 'raw',
        };

    const entity: EntityNestedClientI = isFilled(profileCertificateResult?.Entity)
      ? {
          id: profileCertificateResult.Entity.id,
          name: profileCertificateResult.Entity.name,
          type: profileCertificateResult.Entity.type,
          dataType: 'master',
        }
      : {
          id: profileCertificateResult.EntityRawData!.id,
          name: profileCertificateResult.EntityRawData!.name,
          type: profileCertificateResult.EntityRawData!.type,
          dataType: 'raw',
        };

    const skills: SkillNestedClientI[] = await SkillModule.fetchForEntityCertificateCourse({
      profileId: profileCertificateResult.profileId,
      certificateCourse,
      entity,
    });

    const profileCertificateForInternalClientResult: ProfileCertificateForInternalClientI = {
      id: profileCertificateResult.id!,
      fromDate: profileCertificateResult.fromDate!,
      untilDate: profileCertificateResult.untilDate!,
      fileUrl: profileCertificateResult.fileUrl!,
      certificateCourse,
      entity,
      skills,
    };

    return profileCertificateForInternalClientResult;
  },

  fetchForClient: async ({
    type,
    page,
    pageSize,
    profileId,
  }: ProfileCertificateFetchForClientI): Promise<ProfileCertificateForExternalClientI[]> => {
    const profileCertificateResult = await prismaPG.profileCertificate.findMany({
      where: {
        profileId: profileId,
        OR: [
          {
            CertificateCourse: {
              type,
            },
          },
          {
            CertificateCourseRawData: {
              type,
            },
          },
        ],
      },
      orderBy: [{ untilDate: 'desc' }, { fromDate: 'desc' }, { createdAt: 'desc' }],
      skip: page * pageSize,
      take: pageSize,
      select: {
        id: true,
        Entity: { select: { id: true, name: true } },
        EntityRawData: { select: { id: true, name: true } },
        CertificateCourse: { select: { id: true, name: true } },
        CertificateCourseRawData: { select: { id: true, name: true } },
        fromDate: true,
        untilDate: true,
        createdAt: true,
      },
    });

    const profileCertificateForClientResult: ProfileCertificateForExternalClientI[] = profileCertificateResult.map(
      (profileCertificate) =>
        ({
          id: profileCertificate.id,
          fromDate: profileCertificate.fromDate,
          untilDate: profileCertificate.untilDate,
          createdAt: profileCertificate.createdAt,
          entity: isFilled(profileCertificate?.Entity)
            ? { id: profileCertificate.Entity.id, name: profileCertificate.Entity.name }
            : { id: profileCertificate.EntityRawData.id, name: profileCertificate.EntityRawData.name },
          certificateCourse: isFilled(profileCertificate?.CertificateCourse)
            ? { id: profileCertificate.CertificateCourse.id, name: profileCertificate.CertificateCourse.name }
            : {
                id: profileCertificate.CertificateCourseRawData.id,
                name: profileCertificate.CertificateCourseRawData.name,
              },
        }) as ProfileCertificateForExternalClientI,
    );

    return profileCertificateForClientResult;
  },
};
