import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { ExperienceCargo } from '@prisma/postgres';
import { ExperienceCargosFetchManyI } from '@schemas/career/cargo';
import type { RouteParamsI } from '@schemas/common/common';

export const CargoModule = {
  fetchOne: async ({
    id,
  }: RouteParamsI): Promise<Pick<ExperienceCargo, 'id' | 'name' | 'description' | 'fromDate' | 'toDate'>> => {
    const cargoResult = await prismaPG.experienceCargo.findUnique({
      select: {
        id: true,
        name: true,
        description: true,
        fromDate: true,
        toDate: true,
      },
      where: {
        id,
      },
    });
    if (!cargoResult) {
      throw new AppError('EXP013');
    }
    return cargoResult;
  },
  fetchMany: async ({
    experienceShipId,
  }: ExperienceCargosFetchManyI): Promise<
    Pick<ExperienceCargo, 'id' | 'name' | 'description' | 'fromDate' | 'toDate'>[]
  > => {
    const cargoResult = await prismaPG.experienceCargo.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        fromDate: true,
        toDate: true,
      },
      where: {
        experienceShipId,
      },
    });
    if (!cargoResult) {
      throw new AppError('EXP013');
    }
    return cargoResult;
  },
};
