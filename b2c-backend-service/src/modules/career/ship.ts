import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { ExperienceShipResultI, ExperienceShipResultTempI } from '@interfaces/career/ship';
import type { FastifyStateI } from '@interfaces/common/declaration';
import { SkillNestedClientI } from '@interfaces/company/skill';
import { EquipmentCategoryShipNestedClientI } from '@interfaces/ship/equipmentCategory';
import type { RouteParamsI } from '@schemas/common/common';
import { isFilledString } from '@utils/data/string';

export const CoreShipModule = {
  fetchShipForClient: async (_state: FastifyStateI, { id }: RouteParamsI): Promise<ExperienceShipResultI> => {
    const shipResultTempArr = await prismaPG.$queryRaw<ExperienceShipResultTempI[]>`
      SELECT
        x."id",
        x."shipImo",
        x."shipRawDataImo",
        x."name",
        x."sizeGt",
        x."powerKw",
        x."dwt",
        x."details",
        x."fromDate",
        x."toDate",
        x."subVesselTypeId",
        x."subVesselTypeRawDataId",
        vs."name" AS "subVesselTypeName",
        vsrw."name" AS "subVesselTypeRawDataName",
        x."departmentAlternativeId",
        x."departmentRawDataId",
        d."name" AS "departmentAlternativeName",
        drw."name" AS "departmentRawDataName",
        (
          SELECT
            (
              json_agg(
                json_build_object(
                  'skillId', s."id",
                  'skillRawDataId', srw."id",
                  'skillName', s."name",
                  'skillRawDataName', srw."name",
                  'skillCategory', s."category",
                  'skillRawDataCategory', srw."category"
                )
              )
            )
          FROM
            "career"."ProfileSkillExperienceShip" ps
          LEFT JOIN
            "company"."Skill" s
          ON
            s."id" = ps."skillId"
          LEFT JOIN
            "rawData"."SkillRawData" srw
          ON
            srw."id" = ps."skillRawDataId"
          WHERE
            ps."experienceShipId" = x."id"
        ) AS "skills",
      (
        SELECT
          (
            json_agg(
                json_build_object(
                  'equipmentId', e."id",
                  'equipmentRawDataId', erw."id",
                  'equipmentName', e."name",
                  'equipmentRawDataName', erw."name",
                  'manufacturerName', eqc."manufacturerName",
                  'model', eqc."model",
                  'id', eqc."id"
                )
              )
          )
        FROM
          "career"."ExperienceEquipmentCategory" eqc
        LEFT JOIN
          "ship"."EquipmentCategory" e
        ON
          e."id" = eqc."equipmentCategoryId"
        LEFT JOIN
          "rawData"."EquipmentCategoryRawData" erw
        ON
          erw."id" = eqc."equipmentCategoryRawDataId"
        WHERE
        eqc."experienceShipId" = x."id"
      ) AS "equipments"
      FROM
        "career"."ExperienceShip" x
      LEFT JOIN
        "ship"."SubVesselType" vs
      ON
        vs."id" = x."subVesselTypeId"
      LEFT JOIN
        "rawData"."SubVesselTypeRawData" vsrw
      ON
        vsrw."id" = x."subVesselTypeRawDataId"
      LEFT JOIN
        "company"."DepartmentAlternative" d
      ON
        d."id" = x."departmentAlternativeId"
      LEFT JOIN
        "rawData"."DepartmentRawData" drw
      ON
        drw."id" = x."departmentRawDataId"
      WHERE
        x."id" = ${id}::uuid
    `;
    if (!shipResultTempArr?.length) {
      throw new AppError('SHIP001');
    }
    const shipResultTemp = shipResultTempArr[0];
    const shipResult: ExperienceShipResultI = {
      id: shipResultTemp?.id,
      name: shipResultTemp?.name,
      sizeGt: shipResultTemp?.sizeGt,
      powerKw: shipResultTemp?.powerKw,
      dwt: shipResultTemp?.dwt,
      details: shipResultTemp?.details,
      fromDate: shipResultTemp?.fromDate,
      toDate: shipResultTemp?.toDate,
      subVesselType: isFilledString(shipResultTemp?.subVesselTypeId)
        ? { id: shipResultTemp?.subVesselTypeId, name: shipResultTemp?.subVesselTypeName, dataType: 'master' }
        : isFilledString(shipResultTemp?.subVesselTypeRawDataId)
          ? {
              id: shipResultTemp?.subVesselTypeRawDataId,
              name: shipResultTemp?.subVesselTypeRawDataName,
              dataType: 'raw',
            }
          : null,
      department: isFilledString(shipResultTemp?.departmentAlternativeId)
        ? {
            id: shipResultTemp?.departmentAlternativeId,
            name: shipResultTemp?.departmentAlternativeName,
            dataType: 'master',
          }
        : isFilledString(shipResultTemp?.departmentRawDataId)
          ? { id: shipResultTemp?.departmentRawDataId, name: shipResultTemp?.departmentRawDataName, dataType: 'raw' }
          : null,
      skills: shipResultTemp?.skills?.map(
        (skillItem) =>
          (isFilledString(skillItem?.skillId)
            ? {
                id: skillItem?.skillId,
                name: skillItem?.skillName,
                category: skillItem?.skillCategory,
                dataType: 'master',
              }
            : {
                id: skillItem?.skillRawDataId,
                name: skillItem?.skillRawDataName,
                category: skillItem?.skillRawDataCategory,
                dataType: 'raw',
              }) as SkillNestedClientI,
      ),
      equipments: shipResultTemp?.equipments?.map(
        (equipmentItem) =>
          (isFilledString(equipmentItem?.equipmentId)
            ? {
                category: {
                  id: equipmentItem?.equipmentId,
                  name: equipmentItem?.equipmentName,
                  dataType: 'master',
                },
                manufacturerName: equipmentItem.manufacturerName,
                model: equipmentItem.model,
                id: equipmentItem.id,
              }
            : {
                category: {
                  id: equipmentItem?.equipmentRawDataId,
                  name: equipmentItem?.equipmentRawDataName,
                  dataType: 'raw',
                },
                manufacturerName: equipmentItem.manufacturerName,
                model: equipmentItem.model,
                id: equipmentItem.id,
              }) as EquipmentCategoryShipNestedClientI,
      ),
    };
    return shipResult;
  },
};
