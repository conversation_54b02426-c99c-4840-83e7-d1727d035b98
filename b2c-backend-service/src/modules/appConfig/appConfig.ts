import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import {
  AnnouncementNearByConfigI,
  AuthSessionAppConfigI,
  CommunicationConfigI,
  ForumQuestionConfigI,
} from '@interfaces/appConfig/appConfig';
import { ModuleE, Prisma, SubModuleE } from '@prisma/mongodb';
const AppConfigModule = {
  fetchById: async (
    filters: Prisma.AppConfigWhereInput,
    select: Prisma.AppConfigSelect = {
      id: true,
      config: true,
    },
  ): Promise<AnnouncementNearByConfigI | AuthSessionAppConfigI | CommunicationConfigI | ForumQuestionConfigI> => {
    const appConfigResult = await prismaMG.appConfig.findFirst({
      where: filters,
      select,
    });
    if (!appConfigResult) {
      throw new AppError('APPCFG001');
    }
    switch (filters.module) {
      case ModuleE.ANNOUNCEMENT: {
        switch (filters.subModule) {
          case SubModuleE.NEAR_BY: {
            return appConfigResult.config as AnnouncementNearByConfigI;
          }
        }
        break;
      }
      case ModuleE.AUTH: {
        switch (filters.subModule) {
          case SubModuleE.SESSION: {
            return appConfigResult.config as AuthSessionAppConfigI;
          }
        }
        break;
      }
      case ModuleE.COMMUNICATION: {
        switch (filters.subModule) {
          case SubModuleE.COMMUNICATION: {
            return appConfigResult.config as CommunicationConfigI;
          }
        }
        break;
      }
      case ModuleE.FORUM: {
        switch (filters.subModule) {
          case SubModuleE.QUESTION: {
            return appConfigResult.config as ForumQuestionConfigI;
          }
        }
        break;
      }
    }
    throw new AppError('APPCFG001');
  },

  fetchByIdWithFallback: async (
    filters: Prisma.AppConfigWhereInput,
    select: Prisma.AppConfigSelect = {
      id: true,
      config: true,
    },
    platformId?: string,
  ): Promise<AnnouncementNearByConfigI | AuthSessionAppConfigI | CommunicationConfigI | ForumQuestionConfigI> => {
    try {
      return await AppConfigModule.fetchById(filters, select);
    } catch (error) {
      // If config not found and platform is web_app, return default config
      if (error instanceof AppError && error.errorCode === 'APPCFG001' && platformId === 'web_app') {
        return AppConfigModule.getDefaultConfig(filters);
      }
      throw error;
    }
  },

  getDefaultConfig: (
    filters: Prisma.AppConfigWhereInput,
  ): AnnouncementNearByConfigI | AuthSessionAppConfigI | CommunicationConfigI | ForumQuestionConfigI => {
    switch (filters.module) {
      case ModuleE.ANNOUNCEMENT: {
        switch (filters.subModule) {
          case SubModuleE.NEAR_BY: {
            return { profileRadiusInKM: 50 } as AnnouncementNearByConfigI;
          }
        }
        break;
      }
      case ModuleE.AUTH: {
        switch (filters.subModule) {
          case SubModuleE.SESSION: {
            return { expiry: 2592000000, maxSessions: 3 } as AuthSessionAppConfigI; // 30 days
          }
        }
        break;
      }
      case ModuleE.COMMUNICATION: {
        switch (filters.subModule) {
          case SubModuleE.COMMUNICATION: {
            return {
              name: 'Navicater',
              verification: {
                email: {
                  emailId: '<EMAIL>',
                  limit: { count: 3, duration: 7200000 }, // 2 hours
                  otp: { expiry: 180000 }, // 3 minutes
                },
              },
            } as CommunicationConfigI;
          }
        }
        break;
      }
      case ModuleE.FORUM: {
        switch (filters.subModule) {
          case SubModuleE.QUESTION: {
            return { liveExpiry: 86400000 } as ForumQuestionConfigI; // 24 hours
          }
        }
        break;
      }
    }
    throw new AppError('APPCFG001');
  },
};
export default AppConfigModule;
