import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameTypeI, NullableI, TotalDataI } from '@interfaces/common/data';
import { PostgresTxnI } from '@interfaces/common/db';
import {
  EquipmentManufacturerNestedClientI,
  EquipmentManufacturerTransformParamsI,
} from '@interfaces/ship/equipmentManufacturer';
import { Prisma, EquipmentManufacturer } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type {
  EquipmentManufacturerFetchForClientI,
  EquipmentManufacturerFetchsertI,
} from '@schemas/ship/equipmentManufacturer';

export const EquipmentManufacturerModule = {
  fetchById: async (
    { id, dataType }: IdTypeI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<EquipmentManufacturerNestedClientI> => {
    const equipmentManufacturerResultTemp = await txn.$queryRaw<Pick<EquipmentManufacturer, 'id' | 'name'>>`
    ${
      dataType === 'master'
        ? Prisma.sql`
          SELECT
            e."id",
            e."name"
          FROM
            "ship"."EquipmentManufacturer" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
        : Prisma.sql`
          SELECT
            e."id",
            e."name"
          FROM
            "rawData"."EquipmentManufacturerRawData" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
    }
    `;
    if (!equipmentManufacturerResultTemp) {
      throw new AppError('EQMNF001');
    }
    return {
      ...equipmentManufacturerResultTemp,
      dataType,
    } as EquipmentManufacturerNestedClientI;
  },
  fetchForClient: async (
    filtersP: EquipmentManufacturerFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<TotalDataI<EquipmentManufacturerNestedClientI>> => {
    filtersP.search = filtersP.search?.trim()?.toLowerCase();
    const [equipmentManufacturersResult, equipmentManufacturersTotalResult] = await Promise.all([
      prismaPG.$queryRaw<EquipmentManufacturerNestedClientI[]>`
      SELECT
        e."id",
        e."name",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentManufacturer" e
      ${filtersP.search ? Prisma.sql`WHERE e."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}

      UNION

      SELECT
        erw."id",
        erw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentManufacturerRawData" erw
      ${filtersP.search ? Prisma.sql`WHERE erw."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}

      ORDER BY
        "dataType" ASC,
        "name" ASC
      LIMIT ${pagination.pageSize}
      OFFSET ${pagination.page * pagination.pageSize}
    `,
      prismaPG.$queryRaw<{ total: number }[]>`
      SELECT (
        (SELECT COUNT(*) FROM "ship"."EquipmentManufacturer" e
         ${filtersP.search ? Prisma.sql`WHERE e."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}) +
        (SELECT COUNT(*) FROM "rawData"."EquipmentManufacturerRawData" erw
         ${filtersP.search ? Prisma.sql`WHERE erw."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty})
      )::INTEGER AS total
    `,
    ]);
    return {
      data: equipmentManufacturersResult,
      total: Number(equipmentManufacturersTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async ({ name }: EquipmentManufacturerFetchsertI): Promise<EquipmentManufacturerNestedClientI> => {
    name = name?.toLowerCase()?.trim();
    const results = await prismaPG.$queryRaw<EquipmentManufacturerNestedClientI[]>`
    SELECT * FROM (
      SELECT
        e."id",
        e."name",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentManufacturer" e
      WHERE
        e."name" = ${name}

      UNION

      SELECT
        erw."id",
        erw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentManufacturerRawData" erw
      WHERE
        erw."name" = ${name}
    ) AS combinedResult
    ORDER BY
      combinedResult."dataType" ASC,
      combinedResult."name" ASC
    LIMIT 1
  `;

    if (results && results.length > 0) {
      return results[0];
    }

    const equipmentManufacturerResultTemp = await prismaPG.equipmentManufacturerRawData.create({
      data: {
        name,
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!equipmentManufacturerResultTemp) {
      throw new AppError('EQMNF002', 'Failed to create equipment category');
    }

    return {
      ...equipmentManufacturerResultTemp,
      dataType: 'raw' as DBDataTypeI,
    } as EquipmentManufacturerNestedClientI;
  },
  transform: ({
    equipmentManufacturerId,
    equipmentManufacturerName,
    equipmentManufacturerRawDataId,
    equipmentManufacturerRawDataName,
  }: EquipmentManufacturerTransformParamsI): NullableI<IdNameTypeI> =>
    equipmentManufacturerId
      ? { id: equipmentManufacturerId, name: equipmentManufacturerName, type: 'master' }
      : equipmentManufacturerRawDataId
        ? { id: equipmentManufacturerRawDataId, name: equipmentManufacturerRawDataName, type: 'raw' }
        : null,
};
