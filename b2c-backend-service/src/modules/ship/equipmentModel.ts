import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameTypeI, NullableI, TotalDataI } from '@interfaces/common/data';
import { PostgresTxnI } from '@interfaces/common/db';
import { EquipmentModelNestedClientI, EquipmentModelTransformParamsI } from '@interfaces/ship/equipmentModel';
import { Prisma, EquipmentModel } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type { EquipmentModelFetchForClientI, EquipmentModelFetchsertI } from '@schemas/ship/equipmentModel';

export const EquipmentModelModule = {
  fetchById: async ({ id, dataType }: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<EquipmentModelNestedClientI> => {
    const equipmentModelResultTemp = await txn.$queryRaw<Pick<EquipmentModel, 'id' | 'name'>>`
    ${
      dataType === 'master'
        ? Prisma.sql`
          SELECT
            e."id",
            e."name"
          FROM
            "ship"."EquipmentModel" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
        : Prisma.sql`
          SELECT
            e."id",
            e."name"
          FROM
            "rawData"."EquipmentModelRawData" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
    }
    `;
    if (!equipmentModelResultTemp) {
      throw new AppError('EQMDL001');
    }
    return {
      ...equipmentModelResultTemp,
      dataType,
    } as EquipmentModelNestedClientI;
  },

  fetchForClient: async (
    filtersP: EquipmentModelFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<TotalDataI<EquipmentModelNestedClientI>> => {
    filtersP.search = filtersP.search?.trim()?.toLowerCase();
    const [equipmentModelsResult, equipmentModelsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<EquipmentModelNestedClientI[]>`
      SELECT
        e."id",
        e."name",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentModel" e
      ${filtersP.search ? Prisma.sql`WHERE e."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}

      UNION

      SELECT
        erw."id",
        erw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentModelRawData" erw
      ${filtersP.search ? Prisma.sql`WHERE erw."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}

      ORDER BY
        "dataType" ASC,
        "name" ASC
      LIMIT ${pagination.pageSize}
      OFFSET ${pagination.page * pagination.pageSize}
    `,
      prismaPG.$queryRaw<{ total: number }[]>`
      SELECT (
        (SELECT COUNT(*) FROM "ship"."EquipmentModel" e
         ${filtersP.search ? Prisma.sql`WHERE e."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty}) +
        (SELECT COUNT(*) FROM "rawData"."EquipmentModelRawData" erw
         ${filtersP.search ? Prisma.sql`WHERE erw."name" ILIKE ${'%' + filtersP.search + '%'}` : Prisma.empty})
      )::INTEGER AS total
    `,
    ]);
    return {
      data: equipmentModelsResult,
      total: Number(equipmentModelsTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async ({ name }: EquipmentModelFetchsertI): Promise<EquipmentModelNestedClientI> => {
    name = name?.toLowerCase()?.trim();
    const results = await prismaPG.$queryRaw<EquipmentModelNestedClientI[]>`
    SELECT * FROM (
      SELECT
        e."id",
        e."name",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentModel" e
      WHERE
        e."name" = ${name}

      UNION

      SELECT
        erw."id",
        erw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentModelRawData" erw
      WHERE
        erw."name" = ${name}
    ) AS combinedResult
    ORDER BY
      combinedResult."dataType" ASC,
      combinedResult."name" ASC
    LIMIT 1
  `;

    if (results && results.length > 0) {
      return results[0];
    }

    const equipmentModelResultTemp = await prismaPG.equipmentModelRawData.create({
      data: {
        name,
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!equipmentModelResultTemp) {
      throw new AppError('EQMDL002', 'Failed to create equipment category');
    }

    return {
      ...equipmentModelResultTemp,
      dataType: 'raw' as DBDataTypeI,
    } as EquipmentModelNestedClientI;
  },
  transform: ({
    equipmentModelId,
    equipmentModelName,
    equipmentModelRawDataId,
    equipmentModelRawDataName,
  }: EquipmentModelTransformParamsI): NullableI<IdNameTypeI> =>
    equipmentModelId
      ? { id: equipmentModelId, name: equipmentModelName, type: 'master' }
      : equipmentModelRawDataId
        ? { id: equipmentModelRawDataId, name: equipmentModelRawDataName, type: 'raw' }
        : null,
};
