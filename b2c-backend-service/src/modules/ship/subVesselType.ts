import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { PostgresTxnI } from '@interfaces/common/db';
import { SubVesselTypeNestedClientI } from '@interfaces/ship/subVesselType';
import { Prisma, SubVesselType } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type { SubVesselTypeFetchForClientI, SubVesselTypeFetchsertI } from '@schemas/ship/subVesselType';

export const SubVesselTypeModule = {
  fetchById: async ({ id, dataType }: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<SubVesselTypeNestedClientI> => {
    const subVesselTypeResultTemp = await txn.$queryRaw<Pick<SubVesselType, 'id' | 'name'>>`
      ${
        dataType === 'master'
          ? Prisma.sql`
          SELECT
            v."id",
            v."name"
          FROM
            "ship"."SubVesselType" v
          WHERE
            v."id" = ${id}::uuid
          LIMIT 1
        `
          : Prisma.sql`
          SELECT
            vrw."id",
            vrw."name"
          FROM
            "rawData"."SubVesselTypeRawData" vrw
          WHERE
            vrw."id" = ${id}::uuid
          LIMIT 1
        `
      }
    `;
    if (!subVesselTypeResultTemp) {
      throw new AppError('MVSLTP001');
    }
    return {
      ...subVesselTypeResultTemp,
      dataType: 'master',
    } as SubVesselTypeNestedClientI;
  },

  fetchForClient: async (
    filtersP: SubVesselTypeFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: SubVesselTypeNestedClientI[]; total: number }> => {
    filtersP.search = filtersP.search.trim().toLowerCase();
    const [masterResults, rawResults, masterCount, rawCount] = await Promise.all([
      prismaPG.$queryRaw<SubVesselTypeNestedClientI[]>`
        SELECT
          v."id",
          v."name",
          'master' AS "dataType"
        FROM
          "ship"."SubVesselType" v
        WHERE
          LOWER(v."name") ILIKE ${`${filtersP.search + '%'}%`}
        ORDER BY
          v."name" ASC
        LIMIT ${pagination.pageSize}
        OFFSET ${pagination.page}
      `,
      prismaPG.$queryRaw<SubVesselTypeNestedClientI[]>`
        SELECT
          vrw."id",
          vrw."name",
          'raw' AS "dataType"
        FROM
          "rawData"."SubVesselTypeRawData" vrw
        WHERE
          LOWER(vrw."name") ILIKE ${`${filtersP.search + '%'}%`}
        ORDER BY
          vrw."name" ASC
        LIMIT ${pagination.pageSize}
        OFFSET ${pagination.page}
      `,
      prismaPG.$queryRaw<{ count: bigint }[]>`
        SELECT
          COUNT(*) as count
        FROM
          "ship"."SubVesselType" v
        WHERE
          LOWER(v."name") ILIKE ${`${filtersP.search + '%'}%`}
      `,
      prismaPG.$queryRaw<{ count: bigint }[]>`
        SELECT
          COUNT(*) as count
        FROM
          "rawData"."SubVesselTypeRawData" vrw
        WHERE
          LOWER(vrw."name") ILIKE ${`${filtersP.search + '%'}%`}
      `,
    ]);

    const combinedData = [...masterResults, ...rawResults].sort((a, b) => a.name.localeCompare(b.name));

    return {
      data: combinedData,
      total: Number(masterCount[0]?.count || 0) + Number(rawCount[0]?.count || 0),
    };
  },

  fetchsert: async ({ name, mainVesselTypeId }: SubVesselTypeFetchsertI): Promise<SubVesselTypeNestedClientI> => {
    name = name?.toLowerCase()?.trim();
    const subVesselTypeRawQueryResult = await prismaPG.$queryRaw<SubVesselTypeNestedClientI[]>`
      SELECT * FROM
      (
        SELECT
          v."id",
          v."name",
          'master' AS "dataType"
        FROM
        "ship"."SubVesselType" v
        WHERE
        v."name" = ${name}

        UNION
        SELECT
          vrw."id",
          vrw."name",
          'raw' AS "dataType"
        FROM
          "rawData"."SubVesselTypeRawData" vrw
        WHERE
          vrw."name" = ${name}

      ) AS combinedResult
      ORDER BY
        combinedResult."dataType" ASC,
        combinedResult."name" ASC
      LIMIT 1
    `;

    if (subVesselTypeRawQueryResult && subVesselTypeRawQueryResult.length > 0) {
      return subVesselTypeRawQueryResult[0];
    }

    const subVesselTypResultTemp = await prismaPG.subVesselTypeRawData.create({
      data: {
        name,
        mainVesselTypeId,
      },
      select: {
        id: true,
        name: true,
      },
    });
    return {
      id: subVesselTypResultTemp.id,
      name,
      dataType: 'raw' as DBDataTypeI,
    } as SubVesselTypeNestedClientI;
  },
};
