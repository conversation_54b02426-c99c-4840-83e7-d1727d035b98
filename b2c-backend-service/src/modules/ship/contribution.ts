import { CoreShipModule } from './ship';

import { prismaPG } from '@config/db';
import { ObjStrI } from '@interfaces/common/data';
import { FastifyStateI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';

import AppError from '@classes/AppError';
import { ShipContributionFetchOneParamsI, ShipContributionUpsertOneParamsI } from '@schemas/ship/contribution';
import { KeyContributionParamsI } from '@interfaces/common/contribution';
import { isPrimitiveData } from '@utils/data/data';
import { ShipContributionLabelI } from '@consts/ship/contribution';

export const ShipContributionModule = {
  fetch: async (state: FastifyStateI, params: ShipContributionFetchOneParamsI) => {
    await CoreShipModule.fetchByImo({ imo: params.imo, dataType: params.dataType });
    const filters: Prisma.ShipContributionWhereInput = {
      profileId: state.profileId,
    };
    if (params.dataType === 'master') {
      filters.shipImo = params.imo;
    } else if (params.dataType === 'raw') {
      filters.shipRawDataImo = params.imo;
    }
    const shipContributionsResult = await prismaPG.shipContribution.findMany({
      select: {
        label: true,
        value: true,
      },
      where: filters,
      orderBy: {
        createdAt: 'desc',
      },
    });
    return shipContributionsResult;
  },
  upsertOne: async (state: FastifyStateI, params: ShipContributionUpsertOneParamsI): Promise<void> => {
    await CoreShipModule.fetchByImo({ imo: params.imo, dataType: params.dataType });
    const contributionsMap: KeyContributionParamsI = params.contributions.reduce((acc, curr) => {
      acc[curr.label] = curr.value;
      return acc;
    }, {} as KeyContributionParamsI);
    const filters: Prisma.ShipContributionWhereInput = {
      profileId: state.profileId,
      label: {
        in: Object.keys(contributionsMap) as ShipContributionLabelI[],
      },
    };
    if (params.dataType === 'master') {
      filters.shipImo = params.imo;
    } else if (params.dataType === 'raw') {
      filters.shipRawDataImo = params.imo;
    }
    const existingShipContributionsResult = await prismaPG.shipContribution.findMany({
      select: {
        id: true,
        label: true,
      },
      where: filters,
    });
    const existingContributionLabelIdMap: ObjStrI = existingShipContributionsResult.reduce((acc, curr) => {
      acc[curr.label] = curr.id;
      return acc;
    }, {} as ObjStrI);

    const createInput: Prisma.ShipContributionUncheckedCreateInput[] = [];

    const updateInputFilter: {
      input: Prisma.ShipContributionUpdateInput;
      filter: Prisma.ShipContributionWhereUniqueInput;
    }[] = [];

    Object.keys(contributionsMap).forEach((labelKey) => {
      const contributionValue = contributionsMap[labelKey];

      const existingContributionId = existingContributionLabelIdMap?.[labelKey];
      if (existingContributionId) {
        updateInputFilter.push({
          input: {
            value: isPrimitiveData(contributionValue) ? String(contributionValue) : JSON.stringify(contributionValue),
          },
          filter: {
            id: existingContributionId,
          },
        });
      } else {
        const createInputItem: Prisma.ShipContributionUncheckedCreateInput = {
          label: labelKey as ShipContributionLabelI,
          value: isPrimitiveData(contributionValue) ? String(contributionValue) : JSON.stringify(contributionValue),
          profileId: state.profileId,
        };
        if (params.dataType === 'master') {
          createInputItem.shipImo = params.imo;
        } else if (params.dataType === 'raw') {
          createInputItem.shipRawDataImo = params.imo;
        }
        createInput.push(createInputItem);
      }
    });
    let countUpserted: number = 0;
    if (createInput?.length) {
      countUpserted = (await prismaPG.shipContribution.createMany({ data: createInput })).count;
    }
    if (updateInputFilter?.length) {
      const updatedShipContributionResult = await Promise.all(
        updateInputFilter.map((updateInputFilterItem) =>
          prismaPG.shipContribution.update({
            data: updateInputFilterItem.input,
            where: updateInputFilterItem.filter,
            select: {
              id: true,
            },
          }),
        ),
      );
      updatedShipContributionResult.forEach((item) => {
        if (item.id) {
          ++countUpserted;
        }
      });
    }
    if (countUpserted !== Object.keys(contributionsMap).length) {
      throw new AppError('SHPCNB006');
    }
    return;
  },
};
