import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { PostgresTxnI } from '@interfaces/common/db';
import { MainVesselTypeNestedClientI } from '@interfaces/ship/mainVesselType';

import { MainVesselType, Prisma } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type { MainVesselTypeFetchForClientI, MainVesselTypeFetchsertI } from '@schemas/ship/mainVesselType';

export const MainVesselTypeModule = {
  fetchOne: async ({ id, dataType }: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<MainVesselTypeNestedClientI> => {
    const mainVesselTypeResultTemp = await txn.$queryRaw<Pick<MainVesselType, 'id' | 'name'>>`
      ${
        dataType === 'master'
          ? Prisma.sql`
          SELECT
            v."id",
            v."name"
          FROM
            "ship"."MainVesselType" v
          WHERE
            v."id" = ${id}::uuid
          LIMIT 1
        `
          : Prisma.sql`
          SELECT
            vrw."id",
            vrw."name"
          FROM
            "rawData"."MainVesselTypeRawData" vrw
          WHERE
            vrw."id" = ${id}::uuid
          LIMIT 1
        `
      }
    `;
    if (!mainVesselTypeResultTemp) {
      throw new AppError('MVSLTP001');
    }
    return {
      ...mainVesselTypeResultTemp,
      dataType,
    } as MainVesselTypeNestedClientI;
  },
  fetchForClient: async (
    filtersP: MainVesselTypeFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<MainVesselTypeNestedClientI[]> => {
    filtersP.search = filtersP.search.trim().toLowerCase();
    const filters: Prisma.MainVesselTypeWhereInput = {
      name: {
        startsWith: filtersP.search,
        mode: 'insensitive',
      },
    };
    const mainVesselTypesResult = (await prismaPG.mainVesselType.findMany({
      where: filters,
      skip: pagination.page,
      take: pagination.pageSize,
      orderBy: { name: 'asc' },
      select: { id: true, name: true },
    })) as MainVesselTypeNestedClientI[];

    return mainVesselTypesResult;
  },
  fetchsert: async ({ name }: MainVesselTypeFetchsertI): Promise<MainVesselTypeNestedClientI> => {
    name = name?.toLowerCase()?.trim();
    const mainVesselTypeRawResult = await prismaPG.$queryRaw<MainVesselTypeNestedClientI>`
      SELECT * FROM
      (
        SELECT
          v."id",
          v."name",
          'master' AS "dataType"
        FROM
        "ship"."MainVesselType" v
        WHERE
        v."name" = ${name}
        ORDER BY
        v."name" ASC
        LIMIT 1
        UNION
        SELECT
          vrw."id",
          vrw."name",
          'raw' AS "dataType"
        FROM
          "rawData"."MainVesselTypeRawData" vrw
        WHERE
          vrw."name" = ${name}
        ORDER BY
        vrw."name" ASC
          LIMIT 1
      ) AS combinedResult
      ORDER BY
        combinedResult."dataType" ASC,
        combinedResult."name" ASC
      LIMIT 1
    `;
    if (mainVesselTypeRawResult) {
      return mainVesselTypeRawResult;
    }
    const mainVesselTypResultTemp = await prismaPG.mainVesselTypeRawData.create({
      data: {
        name,
      },
      select: {
        id: true,
      },
    });
    return {
      id: mainVesselTypResultTemp.id,
      name,
      dataType: 'raw' as DBDataTypeI,
    } as MainVesselTypeNestedClientI;
  },
};
