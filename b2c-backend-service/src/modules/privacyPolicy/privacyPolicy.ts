import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import {
  FetchPrivacyPolicyParams,
  PrivacyPolicyContentI,
  PrivacyPolicyStatusI,
} from '@interfaces/privacyPolicy/privacyPolicy';

const PrivacyPolicyModule = {
  fetch: async (params: FetchPrivacyPolicyParams): Promise<PrivacyPolicyContentI> => {
    const policy = await prismaPG.privacyPolicy.findFirst({
      where: {
        isActive: true,
        ...(params.type && { type: params.type }),
      },
      select: {
        id: true,
        content: true,
      },
    });

    if (!policy) {
      throw new AppError('PP001');
    }

    return policy;
  },
  acceptPrivacyPolicy: async (state: FastifyStateI): Promise<PrivacyPolicyStatusI> => {
    const existingStatus = await prismaPG.profileStatus.findUnique({
      where: { profileId: state.profileId },
      select: {
        profileId: true,
        isPrivacyPolicyAccepted: true,
      },
    });

    if (!existingStatus) {
      throw new AppError('PFSTS001');
    }

    if (existingStatus.isPrivacyPolicyAccepted) {
      return existingStatus;
    }
    const updatedStatus = await prismaPG.profileStatus.update({
      where: { profileId: state.profileId },
      data: { isPrivacyPolicyAccepted: true },
      select: {
        profileId: true,
        isPrivacyPolicyAccepted: true,
      },
    });

    return updatedStatus;
  },
};

export default PrivacyPolicyModule;
