import { prismaMG } from '../../../src/config/db';
import { SeedMetaItem } from './types';

export const seedMeta: SeedMetaItem<typeof prismaMG.appConfig | typeof prismaMG.communicationTemplate | typeof prismaMG.internalService | typeof prismaMG.notificationTemplate | typeof prismaMG.vendor>[] = [
  {
    table: prismaMG.appConfig,
    fileName: 'appConfig.json',
    id: ['id'],
  },
  {
    table: prismaMG.communicationTemplate,
    fileName: 'communicationTemplate.json',
    id: ['id'],
  },
  {
    table: prismaMG.notificationTemplate,
    fileName: 'notificationTemplate.json',
    id: ['id'],
  },
  {
    table: prismaMG.internalService,
    fileName: 'internalService.json',
    id: ['id'],
  },
  {
    table: prismaMG.vendor,
    fileName: 'vendor.json',
    id: ['id'],
  },
];
