export interface SeedMetaItem<T> {
  table: T;
  fileName: string;
  objId?: string;
  id: string[];
}

export interface BatchInput<T> {
  inputs: T[];
  callbackFn: (param: T) => Promise<unknown>;
  batchSize: number;
}

export interface UnsavedRecord {
  record: unknown;
  error: string;
  index: number;
  fileName: string;
}

export interface SeedInput {
  table: unknown;
  record: Record<string, unknown>;
  objId?: string;
  id: string[];
  index: number;
  fileName: string;
}
