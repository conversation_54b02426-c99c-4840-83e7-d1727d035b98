[{"countryIso2": "AD", "code": "+376", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AE", "code": "+971", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AF", "code": "+93", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AG", "code": "+1268", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AI", "code": "+1264", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AL", "code": "+355", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AM", "code": "+374", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AO", "code": "+244", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AQ", "code": "+672", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AR", "code": "+54", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AS", "code": "+1684", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AT", "code": "+43", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AU", "code": "+61", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AW", "code": "+297", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AX", "code": "+358", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "AZ", "code": "+994", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BA", "code": "+387", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BB", "code": "+1246", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BD", "code": "+880", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BE", "code": "+32", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BF", "code": "+226", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BG", "code": "+359", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BH", "code": "+973", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BI", "code": "+257", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BJ", "code": "+229", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BL", "code": "+590", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BM", "code": "+1441", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BN", "code": "+673", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BO", "code": "+591", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BQ", "code": "+599", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BR", "code": "+55", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BS", "code": "+1242", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BT", "code": "+975", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BV", "code": "+47", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BW", "code": "+267", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BY", "code": "+375", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "BZ", "code": "+501", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CA", "code": "+1", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CC", "code": "+61", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CD", "code": "+243", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CF", "code": "+236", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CG", "code": "+242", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CH", "code": "+41", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CI", "code": "+225", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CK", "code": "+682", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CL", "code": "+56", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CM", "code": "+237", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CN", "code": "+86", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CO", "code": "+57", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CR", "code": "+506", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CU", "code": "+53", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CV", "code": "+238", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CW", "code": "+599", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CX", "code": "+61", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CY", "code": "+357", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "CZ", "code": "+420", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "DE", "code": "+49", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "DJ", "code": "+253", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "DK", "code": "+45", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "DM", "code": "+1767", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "DO", "code": "+1849", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "DZ", "code": "+213", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "EC", "code": "+593", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "EE", "code": "+372", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "EG", "code": "+20", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "EH", "code": "+212", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ER", "code": "+291", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ES", "code": "+34", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ET", "code": "+251", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "FI", "code": "+358", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "FJ", "code": "+679", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "FK", "code": "+500", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "FM", "code": "+691", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "FO", "code": "+298", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "FR", "code": "+33", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GA", "code": "+241", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GB", "code": "+44", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GD", "code": "+1473", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GE", "code": "+995", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GF", "code": "+594", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GG", "code": "+44", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GH", "code": "+233", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GI", "code": "+350", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GL", "code": "+299", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GM", "code": "+220", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GN", "code": "+224", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GP", "code": "+590", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GQ", "code": "+240", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GR", "code": "+30", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GS", "code": "+500", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GT", "code": "+502", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GU", "code": "+1671", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GW", "code": "+245", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "GY", "code": "+595", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "HK", "code": "+852", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "HM", "code": "+672", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "HN", "code": "+504", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "HR", "code": "+385", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "HT", "code": "+509", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "HU", "code": "+36", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ID", "code": "+62", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IE", "code": "+353", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IL", "code": "+972", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IM", "code": "+44", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IN", "code": "+91", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IO", "code": "+246", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IQ", "code": "+964", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IR", "code": "+98", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IS", "code": "+354", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "IT", "code": "+39", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "JE", "code": "+44", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "JM", "code": "+1876", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "JO", "code": "+962", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "JP", "code": "+81", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KE", "code": "+254", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KG", "code": "+996", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KH", "code": "+855", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KI", "code": "+686", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KM", "code": "+269", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KN", "code": "+1869", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KP", "code": "+850", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KR", "code": "+82", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KW", "code": "+965", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KY", "code": "+ 345", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "KZ", "code": "+77", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LA", "code": "+856", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LB", "code": "+961", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LC", "code": "+1758", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LI", "code": "+423", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LK", "code": "+94", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LR", "code": "+231", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LS", "code": "+266", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LT", "code": "+370", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LU", "code": "+352", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LV", "code": "+371", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "LY", "code": "+218", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MA", "code": "+212", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MC", "code": "+377", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MD", "code": "+373", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ME", "code": "+382", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MF", "code": "+590", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MG", "code": "+261", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MH", "code": "+692", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MK", "code": "+389", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ML", "code": "+223", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MM", "code": "+95", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MN", "code": "+976", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MO", "code": "+853", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MP", "code": "+1670", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MQ", "code": "+596", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MR", "code": "+222", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MS", "code": "+1664", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MT", "code": "+356", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MU", "code": "+230", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MV", "code": "+960", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MW", "code": "+265", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MX", "code": "+52", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MY", "code": "+60", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "MZ", "code": "+258", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NA", "code": "+264", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NC", "code": "+687", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NE", "code": "+227", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NF", "code": "+672", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NG", "code": "+234", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NI", "code": "+505", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NL", "code": "+31", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NO", "code": "+47", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NP", "code": "+977", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NR", "code": "+674", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NU", "code": "+683", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "NZ", "code": "+64", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "OM", "code": "+968", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PA", "code": "+507", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PE", "code": "+51", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PF", "code": "+689", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PG", "code": "+675", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PH", "code": "+63", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PK", "code": "+92", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PL", "code": "+48", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PM", "code": "+508", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PN", "code": "+872", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PR", "code": "+1939", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PS", "code": "+970", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PT", "code": "+351", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PW", "code": "+680", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "PY", "code": "+595", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "QA", "code": "+974", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "RE", "code": "+262", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "RO", "code": "+40", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "RS", "code": "+381", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "RU", "code": "+7", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "RW", "code": "+250", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SA", "code": "+966", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SB", "code": "+677", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SC", "code": "+248", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SD", "code": "+249", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SE", "code": "+46", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SG", "code": "+65", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SH", "code": "+290", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SI", "code": "+386", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SJ", "code": "+47", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SK", "code": "+421", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SL", "code": "+232", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SM", "code": "+378", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SN", "code": "+221", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SO", "code": "+252", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SR", "code": "+597", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SS", "code": "+211", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ST", "code": "+239", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SV", "code": "+503", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SX", "code": "+1-721", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SY", "code": "+963", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "SZ", "code": "+268", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TC", "code": "+1649", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TD", "code": "+235", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TF", "code": "+262", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TG", "code": "+228", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TH", "code": "+66", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TJ", "code": "+992", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TK", "code": "+690", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TL", "code": "+670", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TM", "code": "+993", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TN", "code": "+216", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TO", "code": "+676", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TR", "code": "+90", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TT", "code": "+1868", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TV", "code": "+688", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TW", "code": "+886", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "TZ", "code": "+255", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "UA", "code": "+380", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "UG", "code": "+256", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "UM", "code": "+011", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "US", "code": "+1", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "UY", "code": "+598", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "UZ", "code": "+998", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "VA", "code": "+379", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "VC", "code": "+1784", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "VE", "code": "+58", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "VG", "code": "+1284", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "VI", "code": "+1340", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "VN", "code": "+84", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "VU", "code": "+678", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "WF", "code": "+681", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "WS", "code": "+685", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "XK", "code": "+383", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "YE", "code": "+967", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "YT", "code": "+262", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ZA", "code": "+27", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ZM", "code": "+260", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}, {"countryIso2": "ZW", "code": "+263", "createdAt": "2025-01-10T11:57:31.477Z", "updatedAt": "2025-01-10T11:57:31.477Z"}]