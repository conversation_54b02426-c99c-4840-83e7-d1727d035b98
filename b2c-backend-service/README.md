### Setup Guide

##### Environment Setup

###### a) Local Setup

1. Install Docker from the following link:
   https://www.docker.com/products/docker-desktop/
2. Create a .env file with the variables in .env.example
3. Create a .npmrc file consisting the token
4. Execute the following cmds:

```sh
# Install packages
npm i
# Generate Prisma files
npm run generate
# Setup containers
docker compose -f docker-compose-local.yaml up
```
