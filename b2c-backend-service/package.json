{"name": "b2c-backend", "version": "1.0.0", "description": "B2C Social Network App Backend", "scripts": {"prepare": "if [ \"$NODE_ENV\" = \"development\" ]; then husky install; fi", "dev": "ts-node-dev --respawn --transpile-only --require tsconfig-paths/register --poll --watch src src/index.ts", "start": "node ./dist/index.js", "build": "tsc -p tsconfig.json && tsc-alias -p tsconfig.json", "lint": "eslint . --ext .ts --ignore-pattern dist/", "lint:fix": "eslint . --ext .ts --ignore-pattern dist/ --fix", "fmt": "prettier --write . && npm run fmt:all", "lint-staged": "lint-staged", "migrate:pg": "prisma migrate dev --name init --schema=./prisma/postgresql/schema.prisma", "migrate:sc": "ts-node prisma/postgresql/migration/index.ts", "migrate:mg": "prisma db push --schema=prisma/mongodb/schema.prisma", "migrate:mg1": "prisma migrate dev --name init --schema=./prisma/mongodb/schema.prisma", "migrate": "npm run migrate:pg && npm run migrate:sc && npm run migrate:mg", "reset:pg": "prisma migrate reset --schema=./prisma/postgresql/schema.prisma", "reset:mg": "prisma migrate reset --schema=./prisma/mongodb/schema.prisma", "reset:all": "npm run reset:pg && npm run reset:mg", "deploy:pg": "prisma migrate deploy --schema=./prisma/postgresql/schema.prisma", "deploy:mg": "prisma migrate deploy --schema=./prisma/mongodb/schema.prisma", "deploy:all": "npm run deploy:pg && npm run deploy:mg", "generate:pg": "prisma generate --schema=prisma/postgresql/schema.prisma", "generate:mg": "prisma generate --schema=prisma/mongodb/schema.prisma", "generate": "npm run generate:pg && npm run generate:mg", "fmt:pg": "prisma format --schema=prisma/postgresql/schema.prisma", "fmt:mg": "prisma format --schema=prisma/mongodb/schema.prisma", "fmt:all": "npm run fmt:pg && npm run fmt:mg", "seed:pg": "ts-node prisma/postgresql/seed/seed.ts", "deseed:pg": "ts-node prisma/postgresql/seed/deseed.ts", "seed:mg": "ts-node prisma/mongodb/seed/seed.ts", "deseed:mg": "ts-node prisma/mongodb/seed/deseed.ts", "seed": "npm run seed:pg && npm run seed:mg", "deseed": "npm run prisma:deseed:pg && npm run prisma:deseed:mg", "data:generate": "ts-node ./src/utils/generator/dataGenerator.ts"}, "lint-staged": {"**/*": ["npx lint", "npx format"]}, "keywords": ["Social Network"], "author": "Navicater Team", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@fastify/autoload": "^6.0.3", "@fastify/cors": "^10.0.1", "@fastify/env": "^5.0.1", "@fastify/helmet": "^13.0.1", "@fastify/multipart": "^9.0.3", "@fastify/swagger": "^9.4.0", "@fastify/swagger-ui": "^5.2.1", "@fastify/under-pressure": "^9.0.3", "@navicater/b2c-internal-communication": "^0.0.13", "@navicater/vendor-firebase": "^0.0.6", "@navicater/vendor-google": "^0.0.3", "@prisma/client": "^6.8.2", "@types/sharp": "^0.31.1", "apple-signin-auth": "^2.0.0", "dotenv": "^16.4.7", "dotenv-expand": "^12.0.1", "fastify": "^5.2.0", "fastify-cli": "^7.1.0", "fastify-healthcheck": "^5.1.0", "fastify-type-provider-zod": "^4.0.2", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "nanoid": "^5.1.5", "sharp": "^0.34.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/eslint__js": "^8.42.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.15.23", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "fastify-tsconfig": "^2.0.0", "husky": "^9.1.7", "lint-staged": "^15.2.5", "prettier": "^3.2.5", "prisma": "^6.8.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.22.0"}}