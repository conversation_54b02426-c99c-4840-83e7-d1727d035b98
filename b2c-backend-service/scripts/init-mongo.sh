#!/bin/bash

set -e

echo "🔧 Starting mongod on port ${MONGO_REPLICA_PORT} with replica set 'rs0'..."
mongod --port "${MONGO_REPLICA_PORT}" --replSet rs0 --bind_ip_all &

pid=$!

echo "⏳ Waiting for MongoDB to be ready..."
until mongosh --port "${MONGO_REPLICA_PORT}" --eval "db.adminCommand('ping')" >/dev/null 2>&1; do
  sleep 1
done

echo "✅ MongoDB is up."

echo "🔍 Checking if replica set is already initiated..."
IS_RS_INITIATED=$(mongosh --port "${MONGO_REPLICA_PORT}" --quiet --eval "rs.status().ok" || echo "0")

if [[ "$IS_RS_INITIATED" != "1" ]]; then
  echo "⚙️ Initiating replica set..."
  mongosh --port "${MONGO_REPLICA_PORT}" --eval "
    rs.initiate({
      _id: 'rs0',
      members: [{ _id: 0, host: '${MONGO_REPLICA_HOST}:${MONGO_REPLICA_PORT}' }]
    });
  "
else
  echo "✅ Replica set already initiated."
fi

echo "⏳ Waiting for replica set to become PRIMARY..."
until mongosh --port "${MONGO_REPLICA_PORT}" --quiet --eval "rs.isMaster().ismaster" | grep true >/dev/null 2>&1; do
  sleep 1
done

echo "✅ Replica set is PRIMARY."

echo "🔍 Checking if admin user exists..."
USER_EXISTS=$(mongosh --port "${MONGO_REPLICA_PORT}" --quiet --eval "db.getSiblingDB('admin').getUser('${MONGO_INITDB_ROOT_USERNAME}') !== null")

if [[ "$USER_EXISTS" == "false" ]]; then
  echo "👤 Creating admin user..."
  mongosh --port "${MONGO_REPLICA_PORT}" --eval "
    db.getSiblingDB('admin').createUser({
      user: '${MONGO_INITDB_ROOT_USERNAME}',
      pwd: '${MONGO_INITDB_ROOT_PASSWORD}',
      roles: [
        { role: 'readWrite', db: '${MONGO_INITDB_DATABASE}' },
        { role: 'userAdminAnyDatabase', db: 'admin' },
        { role: 'dbAdminAnyDatabase', db: 'admin' }
      ]
    });
  "
  echo "✅ Admin user created."
else
  echo "✅ Admin user already exists."
fi

echo "🎉 MongoDB replica set and admin user initialized successfully."

wait $pid